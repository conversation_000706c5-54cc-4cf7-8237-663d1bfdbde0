from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from jose import J<PERSON><PERSON><PERSON>r, jwt
from fastapi import HTT<PERSON>Ex<PERSON>, status, Depends
from fastapi.security import HTT<PERSON><PERSON>earer
from .config import settings

security = HTTPBearer()

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.secret_key, algorithm=settings.algorithm)
    return encoded_jwt

def verify_token(token: str = Depends(security)) -> Dict[str, Any]:
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token.credentials, settings.secret_key, algorithms=[settings.algorithm])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        return payload
    except JWTError:
        raise credentials_exception

def check_permission(user: Dict[str, Any], required_permission: str) -> bool:
    """Check if user has required permission"""
    user_permissions = user.get("permissions", [])
    return required_permission in user_permissions or "admin" in user_permissions
