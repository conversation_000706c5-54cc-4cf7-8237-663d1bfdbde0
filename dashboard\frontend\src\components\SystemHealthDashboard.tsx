import React from 'react';
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  Grid,
  Typography,
  Box,
  Chip,
  CircularProgress
} from '@mui/material';
import {
  Storage as Database,
  Memory,
  Storage,
  CheckCircle,
  Error,
  Warning
} from '@mui/icons-material';
import { useQuery } from '@tanstack/react-query';
import { apiService } from '../services/api';

const SystemHealthDashboard: React.FC = () => {
  const { data: systemHealth, isLoading, error } = useQuery({
    queryKey: ['systemHealth'],
    queryFn: () => apiService.getSystemStatus(),
    refetchInterval: 10000, // Refresh every 10 seconds
  });

  const getConnectionIcon = (connected: boolean) => {
    return connected ? <CheckCircle color="success" /> : <Error color="error" />;
  };

  const getConnectionChip = (connected: boolean, label: string) => {
    return (
      <Chip
        icon={getConnectionIcon(connected)}
        label={`${label}: ${connected ? 'Connected' : 'Disconnected'}`}
        color={connected ? 'success' : 'error'}
        variant="outlined"
      />
    );
  };

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight={200}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent>
          <Typography color="error">
            Failed to load system health: {(error as Error).message}
          </Typography>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader title="System Health" />
      <CardContent>
        <Grid container spacing={3}>
          {/* Database Status */}
          <Grid item xs={12} md={6}>
            <Box display="flex" alignItems="center" mb={2}>
              <Database sx={{ mr: 1 }} />
              {getConnectionChip(systemHealth?.database_connection || false, 'Database')}
            </Box>
          </Grid>

          {/* Redis Status */}
          <Grid item xs={12} md={6}>
            <Box display="flex" alignItems="center" mb={2}>
              <Memory sx={{ mr: 1 }} />
              {getConnectionChip(systemHealth?.redis_connection || false, 'Redis')}
            </Box>
          </Grid>

          {/* Last Processing Date */}
          <Grid item xs={12} md={6}>
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Last Processing Date
              </Typography>
              <Typography variant="h6" color="primary">
                {systemHealth?.last_processing_date || 'Unknown'}
              </Typography>
            </Box>
          </Grid>

          {/* Active Processes */}
          <Grid item xs={12} md={6}>
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Active Processes
              </Typography>
              <Typography variant="h6" color="primary">
                {systemHealth?.active_processes || 0}
              </Typography>
            </Box>
          </Grid>

          {/* Disk Space */}
          {systemHealth?.disk_space_gb && (
            <Grid item xs={12} md={6}>
              <Box display="flex" alignItems="center">
                <Storage sx={{ mr: 1 }} />
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    Free Disk Space
                  </Typography>
                  <Typography variant="h6" color="primary">
                    {systemHealth.disk_space_gb.toFixed(1)} GB
                  </Typography>
                </Box>
              </Box>
            </Grid>
          )}

          {/* Memory Usage */}
          {systemHealth?.memory_usage_percent && (
            <Grid item xs={12} md={6}>
              <Box display="flex" alignItems="center">
                <Memory sx={{ mr: 1 }} />
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    Memory Usage
                  </Typography>
                  <Box display="flex" alignItems="center">
                    <Typography variant="h6" color="primary" sx={{ mr: 1 }}>
                      {systemHealth.memory_usage_percent.toFixed(1)}%
                    </Typography>
                    {systemHealth.memory_usage_percent > 80 && (
                      <Warning color="warning" />
                    )}
                    {systemHealth.memory_usage_percent > 90 && (
                      <Error color="error" />
                    )}
                  </Box>
                </Box>
              </Box>
            </Grid>
          )}
        </Grid>
      </CardContent>
    </Card>
  );
};

export default SystemHealthDashboard;
