#!/usr/bin/env python3
"""
Test script to run a simple process and check if it appears in history
"""
import requests
import json
import time
from config_utils import get_urls

def create_test_process():
    """Create a test process to verify the history functionality"""
    urls = get_urls()
    try:
        # First, let's check current history
        print("=== BEFORE: Current process history ===")
        history_response = requests.get(f"{urls['backend']}/api/v1/processes/history")
        if history_response.status_code == 200:
            history_data = history_response.json()
            print(f"Current history count: {len(history_data)}")
            if len(history_data) > 0:
                print("Sample history item fields:")
                for key in history_data[0].keys():
                    print(f"  - {key}")
        else:
            print(f"Failed to get history: {history_response.status_code}")
            
        # Create a test process (a simple echo command)
        print("\n=== Creating test process ===")
        process_data = {
            "script_name": "test_echo",
            "script_path": "echo",  # Simple echo command
            "args": ["Hello World"],
            "cwd": "/tmp"
        }
          create_response = requests.post(
            f"{urls['backend']}/api/v1/processes/run", 
            json=process_data
        )
        
        if create_response.status_code == 200:
            process_info = create_response.json()
            print(f"Process created: {process_info.get('task_id')}")
            
            # Wait a moment for the process to complete
            print("Waiting for process to complete...")
            time.sleep(3)
            
            # Check history again
            print("\n=== AFTER: Process history ===")
            history_response = requests.get(f"{urls['backend']}/api/v1/processes/history")
            if history_response.status_code == 200:
                history_data = history_response.json()
                print(f"Updated history count: {len(history_data)}")
                
                if len(history_data) > 0:
                    latest_process = history_data[0]  # Assuming newest first
                    print(f"\nLatest process details:")
                    for key, value in latest_process.items():
                        print(f"  {key}: {value}")
                    
                    # Check for our fixed field
                    if 'completed_at' in latest_process:
                        print("\n✅ SUCCESS: 'completed_at' field found!")
                    elif 'end_time' in latest_process:
                        print("\n❌ ISSUE: Still using 'end_time' instead of 'completed_at'")
                    else:
                        print("\n⚠️  WARNING: Neither 'completed_at' nor 'end_time' found")
            else:
                print(f"Failed to get updated history: {history_response.status_code}")
        else:
            print(f"Failed to create process: {create_response.status_code} - {create_response.text}")
            
    except requests.exceptions.ConnectionError:
        print(f"❌ ERROR: Cannot connect to backend server. Is it running on port {urls['backend'].split(':')[-1]}?")
    except Exception as e:
        print(f"❌ ERROR: {e}")

if __name__ == "__main__":
    create_test_process()
