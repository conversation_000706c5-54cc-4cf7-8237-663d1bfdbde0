<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Connection Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .log { background-color: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; margin: 10px 0; border-radius: 5px; max-height: 300px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>WebSocket Connection Test</h1>
    <p>This page tests WebSocket connectivity through the nginx proxy with CSP headers.</p>
    
    <div id="status" class="status info">Initializing...</div>
    
    <button id="connectBtn" onclick="connectWebSocket()">Connect WebSocket</button>
    <button id="disconnectBtn" onclick="disconnectWebSocket()" disabled>Disconnect</button>
    <button id="sendTestBtn" onclick="sendTestMessage()" disabled>Send Test Message</button>
    
    <div class="log" id="log"></div>

    <script>
        let socket = null;
        let messageCount = 0;

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function connectWebSocket() {
            try {
                // Connect to WebSocket through nginx proxy (no direct port 8000)
                socket = new WebSocket('ws://localhost/ws');
                
                socket.onopen = function(event) {
                    updateStatus('WebSocket Connected Successfully!', 'success');
                    log('✅ WebSocket connection opened');
                    document.getElementById('connectBtn').disabled = true;
                    document.getElementById('disconnectBtn').disabled = false;
                    document.getElementById('sendTestBtn').disabled = false;
                };
                
                socket.onmessage = function(event) {
                    log(`📨 Received: ${event.data}`);
                    messageCount++;
                };
                
                socket.onclose = function(event) {
                    updateStatus('WebSocket Disconnected', 'info');
                    log(`🔌 WebSocket closed. Code: ${event.code}, Reason: ${event.reason}`);
                    document.getElementById('connectBtn').disabled = false;
                    document.getElementById('disconnectBtn').disabled = true;
                    document.getElementById('sendTestBtn').disabled = true;
                };
                
                socket.onerror = function(error) {
                    updateStatus('WebSocket Error - Check Console for Details', 'error');
                    log(`❌ WebSocket error: ${error}`);
                    console.error('WebSocket error:', error);
                };
                
            } catch (error) {
                updateStatus('Failed to Create WebSocket Connection', 'error');
                log(`❌ Connection error: ${error.message}`);
                console.error('Connection error:', error);
            }
        }

        function disconnectWebSocket() {
            if (socket) {
                socket.close();
                socket = null;
            }
        }

        function sendTestMessage() {
            if (socket && socket.readyState === WebSocket.OPEN) {
                const message = {
                    type: 'test',
                    data: `Test message ${Date.now()}`,
                    timestamp: new Date().toISOString()
                };
                socket.send(JSON.stringify(message));
                log(`📤 Sent: ${JSON.stringify(message)}`);
            } else {
                log('❌ Cannot send message - WebSocket not connected');
            }
        }

        // Auto-connect on page load
        window.onload = function() {
            log('🚀 Page loaded - Testing WebSocket connection...');
            connectWebSocket();
        };
    </script>
</body>
</html>
