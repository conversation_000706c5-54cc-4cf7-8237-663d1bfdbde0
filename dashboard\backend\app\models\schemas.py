from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
from enum import Enum

class ProcessStatus(str, Enum):
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"

class ProcessStartRequest(BaseModel):
    process: str
    parameters: Dict[str, Any] = {}

class ProcessStatusResponse(BaseModel):
    task_id: str
    process: str
    status: ProcessStatus
    progress: Optional[int] = None
    message: Optional[str] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    records_processed: Optional[int] = None

class SystemHealthResponse(BaseModel):
    database_connection: bool
    redis_connection: bool
    last_processing_date: Optional[str] = None
    active_processes: int
    disk_space_gb: Optional[float] = None
    memory_usage_percent: Optional[float] = None

class TableMetric(BaseModel):
    table_name: str
    record_count: int
    last_updated: Optional[datetime] = None
    data_quality_score: Optional[float] = None
    data_quality_status: str = "unknown"

class TableMetricsResponse(BaseModel):
    metrics: List[TableMetric]
    updated_at: datetime

class DataQualityCheck(BaseModel):
    check_type: str
    table_name: str
    status: str
    score: Optional[float] = None
    message: Optional[str] = None
    details: Optional[Dict[str, Any]] = None

class DataQualityResponse(BaseModel):
    overall_score: float
    checks: List[DataQualityCheck]
    updated_at: datetime

class ErrorLogEntry(BaseModel):
    id: str
    timestamp: datetime
    severity: str
    process: Optional[str] = None
    message: str
    details: Optional[Dict[str, Any]] = None

class ErrorLogResponse(BaseModel):
    errors: List[ErrorLogEntry]
    total_count: int
    page: int
    page_size: int

class ProcessingTrend(BaseModel):
    date: str
    process: str
    records_processed: int
    duration_minutes: Optional[float] = None
    success: bool

class ProcessingTrendsResponse(BaseModel):
    trends: List[ProcessingTrend]
    date_range: List[str]
