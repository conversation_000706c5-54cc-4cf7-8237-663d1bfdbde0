import React, { useState, useEffect } from 'react';
import {
  <PERSON>rid,
  Typo<PERSON>,
  <PERSON>,
  Card,
  CardContent,
  Card<PERSON><PERSON>er,
  Alert,
  CircularProgress,
  Chip
} from '@mui/material';
import SystemHealthDashboard from '../components/SystemHealthDashboard';
import SystemHealthChart from '../components/SystemHealthChart';
import TableMetricsGrid from '../components/TableMetricsGrid';
import TableMetricsChart from '../components/TableMetricsChart';
import ProcessStatusCard from '../components/ProcessStatusCard';
import DataQualityCharts from '../components/DataQualityCharts';
import ProcessManager from '../components/ProcessManager';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { apiService } from '../services/api';
import { useWebSocket } from '../hooks/useNativeWebSocket';
import { getWebSocketUrl } from '../config/environment';
import { SystemHealth, TableMetric, DataQualityCheck, WebSocketMessage } from '../types';

const MainDashboard: React.FC = () => {
  const [systemHealthHistory, setSystemHealthHistory] = useState<SystemHealth[]>([]);
  const [realTimeStats, setRealTimeStats] = useState({
    totalOptionsToday: 0,
    dataQualityScore: 0,
    lastUpdateTime: '',
    indexSymbols: 0
  });
  
  const queryClient = useQueryClient();
  
  // WebSocket connection for real-time updates
  const { isConnected, lastMessage, sendMessage } = useWebSocket(getWebSocketUrl(), {
    onOpen: () => console.log('Dashboard WebSocket connected'),
    onClose: () => console.log('Dashboard WebSocket disconnected'),
    onError: (error) => console.error('Dashboard WebSocket error:', error)
  });
  // API queries with proper error handling
  const { data: activeProcesses, isLoading: processesLoading } = useQuery({
    queryKey: ['activeProcesses'],
    queryFn: () => apiService.getActiveProcesses(),
    refetchInterval: 5000,
    retry: 3,
  });

  const { data: systemHealth, isLoading: healthLoading } = useQuery({
    queryKey: ['systemHealth'],
    queryFn: () => apiService.getSystemHealth(),
    refetchInterval: 10000,
    retry: 3,
  });

  const { data: tableMetrics, isLoading: metricsLoading } = useQuery({
    queryKey: ['tableMetrics'],
    queryFn: () => apiService.getTableMetrics(),
    refetchInterval: 30000,
    retry: 3,
  });

  const { data: dataQualityChecks, isLoading: qualityLoading } = useQuery({
    queryKey: ['dataQualityChecks'],
    queryFn: () => apiService.getDataQualityChecks(),
    refetchInterval: 30000,
    retry: 3,
  });

  // Update system health history when new data arrives
  useEffect(() => {
    if (systemHealth) {
      setSystemHealthHistory(prev => {
        const newHistory = [...prev, systemHealth];
        // Keep only last 20 data points for chart
        return newHistory.slice(-20);
      });
    }
  }, [systemHealth]);
  // Handle WebSocket messages for real-time updates
  useEffect(() => {
    if (lastMessage) {
      try {
        const message: WebSocketMessage = JSON.parse(lastMessage);
        
        switch (message.type) {
          case 'system_alert':
            // Handle system health updates
            if (message.data.system_health) {
              setSystemHealthHistory(prev => {
                const newHistory = [...prev, message.data.system_health];
                return newHistory.slice(-20);
              });
              queryClient.invalidateQueries({ queryKey: ['systemHealth'] });
            }
            break;
            
          case 'process_update':
            // Handle process status updates
            queryClient.invalidateQueries({ queryKey: ['activeProcesses'] });
            break;
            
          case 'data_quality_update':
            // Handle data quality updates
            queryClient.invalidateQueries({ queryKey: ['dataQualityChecks'] });
            queryClient.invalidateQueries({ queryKey: ['tableMetrics'] });
            break;
            
          default:
            console.log('Unknown WebSocket message type:', message.type);
        }
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    }
  }, [lastMessage, queryClient]);

  // Update real-time stats when data changes
  useEffect(() => {
    if (tableMetrics && dataQualityChecks) {
      const totalOptions = tableMetrics.metrics.reduce((sum, metric) => {
        if (metric.table_name.includes('option')) {
          return sum + metric.record_count;
        }
        return sum;
      }, 0);

      setRealTimeStats({
        totalOptionsToday: totalOptions,
        dataQualityScore: Math.round(dataQualityChecks.overall_score),
        lastUpdateTime: new Date(tableMetrics.updated_at).toLocaleTimeString(),
        indexSymbols: tableMetrics.metrics.filter(m => m.table_name.includes('index')).length
      });
    }
  }, [tableMetrics, dataQualityChecks]);
  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" gutterBottom>
          HKEX Processing Dashboard
        </Typography>
        <Box display="flex" alignItems="center" gap={2}>
          <Chip 
            label={isConnected ? "Connected" : "Disconnected"} 
            color={isConnected ? "success" : "error"}
            variant="outlined"
          />
          {(processesLoading || healthLoading || metricsLoading || qualityLoading) && (
            <CircularProgress size={20} />
          )}
        </Box>
      </Box>
      
      <Grid container spacing={3}>
        {/* Connection Status Alert */}
        {!isConnected && (
          <Grid item xs={12}>
            <Alert severity="warning">
              WebSocket connection lost. Real-time updates are disabled.
            </Alert>
          </Grid>
        )}        {/* System Health Chart */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="System Health" />
            <CardContent>
              <SystemHealthChart data={systemHealthHistory.length > 0 ? systemHealthHistory : (systemHealth ? [systemHealth] : [])} />
            </CardContent>
          </Card>
        </Grid>

        {/* Quick Stats */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Quick Stats" />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="h6" color="primary">
                    {realTimeStats.totalOptionsToday.toLocaleString()}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Options Today
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="h6" color="primary">
                    {realTimeStats.dataQualityScore}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Data Quality Score
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="h6" color="primary">
                    {realTimeStats.lastUpdateTime || 'N/A'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Last Update
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="h6" color="primary">
                    {realTimeStats.indexSymbols}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Index Symbols
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Active Processes */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Active Processes" />
            <CardContent>
              {processesLoading ? (
                <CircularProgress />
              ) : activeProcesses && activeProcesses.length > 0 ? (
                activeProcesses.map((process: any) => (
                  <ProcessStatusCard
                    key={process.task_id}
                    process={{
                      task_id: process.task_id,
                      process: process.name,
                      status: process.status || 'running',
                      progress: process.progress || 50,
                      message: process.message || 'Processing...',
                      records_processed: process.records_processed || 0
                    }}
                  />
                ))
              ) : (
                <Typography color="text.secondary">
                  No active processes
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Data Quality Overview */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Data Quality Overview" />
            <CardContent>              {qualityLoading ? (
                <CircularProgress />
              ) : dataQualityChecks ? (
                <DataQualityCharts checks={dataQualityChecks.checks} />
              ) : (
                <Typography color="text.secondary">
                  No data quality information available
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Table Metrics Chart */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Table Record Counts" />
            <CardContent>
              {metricsLoading ? (
                <CircularProgress />
              ) : tableMetrics ? (
                <TableMetricsChart data={tableMetrics.metrics} />
              ) : (
                <Typography color="text.secondary">
                  No table metrics available
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>        {/* System Health Dashboard */}
        <Grid item xs={12} md={6}>
          <SystemHealthDashboard />
        </Grid>

        {/* Process Manager */}
        <Grid item xs={12}>
          <ProcessManager />
        </Grid>

        {/* Table Metrics Grid */}
        <Grid item xs={12}>
          <TableMetricsGrid />
        </Grid>
      </Grid>
    </Box>
  );
};

export default MainDashboard;
