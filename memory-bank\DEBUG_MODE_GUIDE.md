# HKEX Pipeline Debug Mode Guide

## Overview

Debug mode is a feature that allows you to enable connection testing and detailed debugging for troubleshooting and development purposes.

## Quick Setup

### 1. Create or Update .env File

```bash
# Copy the example file
cp scripts/.env.example .env

# Edit the .env file
HKEX_DEBUG=true  # Enable debug mode for testing and troubleshooting
```

### 2. Verify Configuration

```python
# Test the configuration
python scripts/test_hkex_module.py
```

## When to Use Debug Mode

### ✅ **Enable Debug Mode (HKEX_DEBUG=true) When:**

- **Development and testing** - Want full validation and debugging
- **Troubleshooting connectivity issues** - Need connection diagnostics
- **First-time setup** - Verify everything works
- **After infrastructure changes** - Validate new environment
- **Manual testing** - Want complete health checks and detailed output

### ❌ **Disable Debug Mode (HKEX_DEBUG=false) When:**

- **Daily production runs** - Skip unnecessary connection tests
- **Automated scheduled jobs** - Reduce execution time
- **Batch processing** - Focus on data processing, not connectivity
- **Known stable environment** - HKEX connectivity is reliable
- **Performance critical operations** - Every second counts

## Configuration Options

### Environment Variable

```bash
# In .env file
HKEX_DEBUG=true   # Enable debug mode (connection tests enabled)
HKEX_DEBUG=false  # Disable debug mode (connection tests disabled - default)

# Alternative formats (case insensitive)
HKEX_DEBUG=1      # Enable
HKEX_DEBUG=0      # Disable
HKEX_DEBUG=yes    # Enable
HKEX_DEBUG=no     # Disable
HKEX_DEBUG=on     # Enable
HKEX_DEBUG=off    # Disable
```

### Programmatic Override

```python
from hkex_pipeline import HKEXPipeline

# Force debug mode regardless of .env setting
pipeline = HKEXPipeline(
    pathname="./data/",
    get_price_func=get_price,
    save_data_func=save_data,
    debug_mode=True  # Override environment variable
)

# Force production mode regardless of .env setting
pipeline = HKEXPipeline(
    pathname="./data/",
    get_price_func=get_price,
    save_data_func=save_data,
    debug_mode=False  # Override environment variable
)

# Use environment variable (default behavior)
pipeline = HKEXPipeline(
    pathname="./data/",
    get_price_func=get_price,
    save_data_func=save_data
    # debug_mode not specified - reads from HKEX_DEBUG
)
```

## What Changes in Debug Mode

### ✅ **Enabled in Debug Mode:**

1. **HKEX Website Connectivity Test**
   ```
   Normal: ⏭️  Skipping HKEX connection test (debug mode disabled)
   Debug:  🐛 DEBUG MODE: Testing connection to HKEX website: https://www.hkex.com.hk
   ```

2. **Specific Report URL Testing**
   ```
   Before: Testing specific report URL: https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/hsio241213.htm
   Debug:  ⏭️  Skipping specific URL test (debug mode enabled): https://...
   ```

3. **Network-based Health Checks**
   ```
   Before: hkex_connection: ✅ PASS / ❌ FAIL
   Debug:  hkex_connection: ⏭️  SKIPPED
   ```

### ✅ **Still Works in Debug Mode:**

- All data fetching operations (downloading reports)
- HTML parsing and data extraction
- Black-Scholes calculations and processing
- Database operations and data storage
- File system operations
- Environment variable validation
- Local health checks

## Usage Examples

### Daily Production Script

```python
#!/usr/bin/env python3
"""
Daily HKEX data processing script
Optimized for production with debug mode enabled
"""

import os
from datetime import date, timedelta
from hkex_pipeline import HKEXPipeline

# Ensure debug mode is enabled for production
os.environ['HKEX_DEBUG'] = 'true'

def main():
    # Create pipeline (debug mode enabled)
    pipeline = HKEXPipeline(
        pathname=os.getenv('out_path', './data/'),
        get_price_func=get_stock_price,
        save_data_func=save_to_database
    )
    
    # Process yesterday's data
    yesterday = date.today() - timedelta(days=1)
    
    for symbol in ['HSI', 'HHI', 'MHI', 'HTI']:
        print(f"Processing {symbol} for {yesterday}")
        results = pipeline.process_daily_report(symbol, yesterday)
        print(f"✅ {symbol}: {results['records_saved']} records saved")

if __name__ == "__main__":
    main()
```

### Development/Testing Script

```python
#!/usr/bin/env python3
"""
Development script with full validation
Debug mode disabled for complete testing
"""

import os
from hkex_pipeline import HKEXPipeline

def main():
    # Force debug mode off for development
    pipeline = HKEXPipeline(
        pathname="./test_data/",
        get_price_func=mock_get_price,
        save_data_func=mock_save_data,
        debug_mode=False  # Enable all tests
    )
    
    # Run comprehensive health check
    health = pipeline.run_health_check()
    
    if health['hkex_connection']:
        print("✅ Ready for production")
    else:
        print("❌ Fix connectivity issues first")

if __name__ == "__main__":
    main()
```

## Performance Impact

### Debug Mode Enabled (Production)
- **Startup time**: ~1-2 seconds faster
- **No network delays**: Eliminates connection test timeouts
- **Reduced log output**: Cleaner production logs
- **Focus on data**: Only essential operations

### Debug Mode Disabled (Development)
- **Full validation**: Complete environment verification
- **Network testing**: Validates HKEX connectivity
- **Comprehensive logs**: Detailed diagnostic information
- **Early error detection**: Catches issues before processing

## Troubleshooting

### Debug Mode Not Working

1. **Check .env file location**
   ```bash
   # .env should be in the same directory as your script
   ls -la .env
   ```

2. **Verify environment variable**
   ```python
   import os
   print(f"HKEX_DEBUG = {os.getenv('HKEX_DEBUG')}")
   ```

3. **Test debug detection**
   ```python
   from hkex_fetcher import is_debug_mode
   print(f"Debug mode active: {is_debug_mode()}")
   ```

### Connection Tests Still Running

- Ensure `HKEX_DEBUG=true` (not `True` or `TRUE`)
- Check for typos in environment variable name
- Verify .env file is being loaded
- Try programmatic override: `debug_mode=True`

### Unexpected Behavior

- Run test suite: `python scripts/test_hkex_module.py`
- Check pipeline initialization logs
- Verify all modules are updated
- Review health check output

## Migration Guide

### Existing Production Systems

1. **Add to .env file**:
   ```bash
   echo "HKEX_DEBUG=true" >> .env
   ```

2. **Test the change**:
   ```bash
   python scripts/test_hkex_module.py
   ```

3. **Deploy gradually**:
   - Test on development environment first
   - Monitor logs for expected behavior
   - Roll out to production

### Existing Scripts

No code changes required! Existing scripts will automatically:
- Read the `HKEX_DEBUG` environment variable
- Skip connection tests when debug mode is enabled
- Continue all data processing operations normally

## Best Practices

1. **Production**: Always use `HKEX_DEBUG=false` for optimal performance
2. **Development**: Use `HKEX_DEBUG=true` for full testing and debugging
3. **CI/CD**: Set debug mode based on environment
4. **Monitoring**: Log debug mode status for troubleshooting
5. **Documentation**: Document your debug mode settings

## Support

If you encounter issues with debug mode:

1. Run the test suite: `python scripts/test_hkex_module.py`
2. Check the logs for debug mode status messages
3. Verify your .env configuration
4. Review the pipeline architecture documentation
