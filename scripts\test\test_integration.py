"""
Test Integration of Firecrawl Fallback with Enhanced HTTP

This script tests the integration without database dependencies.
"""

import os
import sys
import datetime as dt
from pathlib import Path

# Add the scripts directory to Python path
script_dir = Path(__file__).parent
sys.path.append(str(script_dir))

# Set required environment variables for testing
os.environ['out_path'] = str(script_dir / 'output') + '/'
os.environ['LOG_LEVEL'] = '30'  # WARNING level
os.environ['SQL_ECHO'] = '0'
os.environ['WILL9700_DB'] = 'postgresql://dummy:dummy@localhost/dummy'  # Dummy DB for testing

# Import the enhanced functions
try:
    from UpdateStockOptionReportPostgres import (
        safe_http_get,
        safe_http_get_with_firecrawl_fallback,
        test_hkex_connection,
        test_specific_report_url,
        FIRECRAWL_AVAILABLE
    )
    print("✅ Successfully imported enhanced functions")
except Exception as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

def test_direct_vs_fallback():
    """Test direct HTTP vs Firecrawl fallback methods"""
    print("🧪 Testing Direct HTTP vs Firecrawl Fallback")
    print("=" * 60)
    
    # Test URL that should work
    test_url = "https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/hsiwo250613.htm"
    
    print(f"\n1️⃣ Testing Direct HTTP Method")
    print(f"URL: {test_url}")
    
    try:
        response_direct = safe_http_get(test_url, timeout=30, max_retries=1, delay_between_retries=2)
        if response_direct and response_direct.status_code == 200:
            print(f"✅ Direct method succeeded")
            print(f"📏 Content length: {len(response_direct.content)} bytes")
            direct_success = True
        else:
            print(f"❌ Direct method failed")
            direct_success = False
    except Exception as e:
        print(f"❌ Direct method error: {e}")
        direct_success = False
    
    print(f"\n2️⃣ Testing Fallback Method")
    print(f"URL: {test_url}")
    
    try:
        response_fallback = safe_http_get_with_firecrawl_fallback(test_url, timeout=30, max_retries=1, delay_between_retries=2)
        if response_fallback and response_fallback.status_code == 200:
            print(f"✅ Fallback method succeeded")
            print(f"📏 Content length: {len(response_fallback.content)} bytes")
            fallback_success = True
        else:
            print(f"❌ Fallback method failed")
            fallback_success = False
    except Exception as e:
        print(f"❌ Fallback method error: {e}")
        fallback_success = False
    
    print(f"\n📊 Results Summary:")
    print(f"Direct HTTP:     {'✅ SUCCESS' if direct_success else '❌ FAILED'}")
    print(f"Fallback Method: {'✅ SUCCESS' if fallback_success else '❌ FAILED'}")
    print(f"Firecrawl Available: {'✅ YES' if FIRECRAWL_AVAILABLE else '❌ NO'}")
    
    return direct_success or fallback_success

def test_problematic_url():
    """Test with the URL that was originally failing"""
    print("\n🎯 Testing Problematic URL (Original Failure)")
    print("=" * 60)
    
    # The URL that was originally failing
    today = dt.date.today()
    t = ('dqe' + today.strftime("%y%m%d")).lower()
    problem_url = f'https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/{t}.htm'
    
    print(f"URL: {problem_url}")
    print(f"Date: {today}")
    
    try:
        response = safe_http_get_with_firecrawl_fallback(problem_url, timeout=60, max_retries=1, delay_between_retries=3)
        if response and response.status_code == 200:
            print(f"✅ Problematic URL now works!")
            print(f"📏 Content length: {len(response.content)} bytes")
            
            # Check if it looks like a valid report
            content_str = response.text.lower() if hasattr(response, 'text') else str(response.content).lower()
            if any(keyword in content_str for keyword in ['stock option', 'hkex', 'hong kong exchange', 'daily market report']):
                print(f"✅ Content appears to be a valid HKEX report")
            else:
                print(f"⚠️  Content may not be a valid HKEX report")
            
            return True
        else:
            print(f"❌ Problematic URL still fails")
            if response:
                print(f"Status code: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error testing problematic URL: {e}")
        return False

def test_integration_functions():
    """Test the integration functions"""
    print("\n🔧 Testing Integration Functions")
    print("=" * 60)
    
    print("\n1️⃣ Testing HKEX connection...")
    try:
        hkex_result = test_hkex_connection()
        print(f"HKEX Connection: {'✅ SUCCESS' if hkex_result else '❌ FAILED'}")
    except Exception as e:
        print(f"❌ HKEX connection test error: {e}")
        hkex_result = False
    
    print("\n2️⃣ Testing specific report URL...")
    try:
        today = dt.date.today()
        report_result = test_specific_report_url(today)
        print(f"Report URL Test: {'✅ SUCCESS' if report_result else '❌ FAILED'}")
    except Exception as e:
        print(f"❌ Report URL test error: {e}")
        report_result = False
    
    return hkex_result and report_result

def main():
    """Main test function"""
    print("🚀 Integration Test Suite")
    print("=" * 60)
    
    print(f"🔥 Firecrawl Available: {'YES' if FIRECRAWL_AVAILABLE else 'NO'}")
    
    # Run tests
    test1_result = test_direct_vs_fallback()
    test2_result = test_problematic_url()
    test3_result = test_integration_functions()
    
    # Summary
    print("\n" + "=" * 60)
    print("🏁 FINAL RESULTS")
    print("=" * 60)
    
    tests = [
        ("Direct vs Fallback", test1_result),
        ("Problematic URL", test2_result),
        ("Integration Functions", test3_result)
    ]
    
    passed = sum(1 for _, result in tests if result)
    total = len(tests)
    
    for test_name, result in tests:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<25}: {status}")
    
    print(f"\n📊 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Firecrawl fallback integration is working correctly")
        print("✅ Your original timeout issues should now be resolved")
        print("\n💡 Next steps:")
        print("   1. Set up your database environment variables")
        print("   2. Run the main UpdateStockOptionReportPostgres.py script")
        print("   3. The script will now automatically fall back to Firecrawl if direct HTTP fails")
    else:
        print("\n⚠️  SOME TESTS FAILED")
        print("💡 Troubleshooting:")
        if not FIRECRAWL_AVAILABLE:
            print("   - Install Firecrawl: pip install firecrawl-py")
        print("   - Check your internet connection")
        print("   - Verify Firecrawl API key is working")

if __name__ == "__main__":
    main()
