#!/usr/bin/env python3
"""
Configuration utilities for reading environment variables and ports.
"""
import os
from typing import Dict, Any

def load_env_file(env_file_path: str = ".env") -> Dict[str, str]:
    """Load environment variables from .env file."""
    env_vars = {}
    if os.path.exists(env_file_path):
        with open(env_file_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    # Remove quotes if present
                    value = value.strip().strip('\'"')
                    env_vars[key] = value
    return env_vars

def get_ports() -> Dict[str, int]:
    """Get frontend and backend ports from environment or .env file."""
    # First try to get from environment variables
    frontend_port = os.getenv('FRONTEND_PORT')
    backend_port = os.getenv('BACKEND_PORT')
    
    # If not found, load from .env file
    if not frontend_port or not backend_port:
        env_vars = load_env_file()
        frontend_port = frontend_port or env_vars.get('FRONTEND_PORT', '3080')
        backend_port = backend_port or env_vars.get('BACKEND_PORT', '8004')
    
    return {
        'frontend': int(frontend_port),
        'backend': int(backend_port)
    }

def get_urls() -> Dict[str, str]:
    """Get application URLs based on configured ports."""
    ports = get_ports()
    return {
        'frontend': f"http://localhost:{ports['frontend']}",
        'backend': f"http://localhost:{ports['backend']}",
        'websocket': f"ws://localhost:{ports['backend']}/ws",
        'api_health': f"http://localhost:{ports['frontend']}/api/health",
        'api_docs': f"http://localhost:{ports['frontend']}/docs"
    }

if __name__ == "__main__":
    # Test the utilities
    ports = get_ports()
    urls = get_urls()
    
    print("Configured Ports:")
    print(f"  Frontend: {ports['frontend']}")
    print(f"  Backend:  {ports['backend']}")
    print()
    print("Application URLs:")
    for name, url in urls.items():
        print(f"  {name.replace('_', ' ').title()}: {url}")
