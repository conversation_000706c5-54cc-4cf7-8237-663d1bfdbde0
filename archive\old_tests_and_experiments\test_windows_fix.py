#!/usr/bin/env python3
"""Test Windows subprocess fix"""
import asyncio
import os
import sys

async def test_windows_subprocess():
    """Test that subprocess creation works on Windows"""
    print("Testing Windows subprocess fix...")
    
    # Set Windows event loop policy
    if os.name == 'nt':
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        print("✅ Windows ProactorEventLoopPolicy set")
    
    # Test subprocess creation
    try:
        process = await asyncio.create_subprocess_exec(
            sys.executable, '--version',
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        stdout, stderr = await process.communicate()
        
        if process.returncode == 0:
            version = stdout.decode().strip()
            print(f"✅ Subprocess test successful: {version}")
            return True
        else:
            error = stderr.decode().strip()
            print(f"❌ Subprocess failed: {error}")
            return False
            
    except Exception as e:
        print(f"❌ Subprocess test failed with error: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_windows_subprocess())
    if success:
        print("🎉 Windows subprocess fix is working!")
    else:
        print("💥 Windows subprocess fix failed!")
    sys.exit(0 if success else 1)
