#!/usr/bin/env python3
"""
Final comprehensive test to verify the Windows subprocess fix is complete.
"""

import asyncio
import sys
import os
from pathlib import Path

def test_event_loop_policy():
    """Test that the Windows event loop policy is set correctly."""
    print("🔍 Testing Windows event loop policy...")
    
    if sys.platform == 'win32':
        try:
            # Test that we can set the ProactorEventLoop policy
            asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
            print("✅ Successfully set ProactorEventLoop policy")
            
            # Test that we get the right event loop type
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            print(f"✅ Event loop type: {type(loop)}")
            
            if 'ProactorEventLoop' in str(type(loop)):
                print("✅ ProactorEventLoop is being used")
                return True
            else:
                print("❌ ProactorEventLoop is NOT being used")
                return False
                
        except Exception as e:
            print(f"❌ Failed to set event loop policy: {e}")
            return False
    else:
        print("ℹ️  Not on Windows, skipping ProactorEventLoop test")
        return True

async def test_subprocess_creation():
    """Test actual subprocess creation with asyncio."""
    print("\n🔍 Testing asyncio subprocess creation...")
    
    try:
        # Test basic subprocess creation
        process = await asyncio.create_subprocess_exec(
            'python', '--version',
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout, stderr = await process.communicate()
        
        if process.returncode == 0:
            print(f"✅ Subprocess executed successfully")
            print(f"✅ Python version: {stdout.decode().strip()}")
            return True
        else:
            print(f"❌ Subprocess failed with return code: {process.returncode}")
            print(f"❌ Error: {stderr.decode()}")
            return False
            
    except NotImplementedError as e:
        print(f"❌ NotImplementedError - Windows subprocess fix needed: {e}")
        return False
    except Exception as e:
        print(f"❌ Subprocess creation failed: {e}")
        return False

def test_orchestrator_import():
    """Test that the orchestrator service can be imported."""
    print("\n🔍 Testing orchestrator service import...")
    
    try:
        # Add the app directory to path
        app_dir = Path(__file__).parent / "app"
        sys.path.insert(0, str(app_dir))
        
        from services.simple_orchestrator import ProcessOrchestratorService
        print("✅ Successfully imported ProcessOrchestratorService")
        
        # Create an instance
        orchestrator = ProcessOrchestratorService()
        print("✅ Successfully created orchestrator instance")
        return True
        
    except Exception as e:
        print(f"❌ Failed to import/create orchestrator: {e}")
        return False

async def main():
    """Main test function."""
    print("=" * 70)
    print("🏁 FINAL WINDOWS SUBPROCESS FIX VERIFICATION")
    print("=" * 70)
    print(f"Platform: {sys.platform}")
    print(f"Python version: {sys.version}")
    
    results = []
    
    # Test 1: Event loop policy
    results.append(test_event_loop_policy())
    
    # Test 2: Subprocess creation
    results.append(await test_subprocess_creation())
    
    # Test 3: Orchestrator import
    results.append(test_orchestrator_import())
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 70)
    
    if all(results):
        print("🎉 ALL TESTS PASSED!")
        print("✅ Windows subprocess fix is COMPLETE and WORKING!")
        print("✅ The HKEX Dashboard backend should now work properly on Windows!")
    else:
        print("❌ Some tests failed:")
        test_names = ["Event Loop Policy", "Subprocess Creation", "Orchestrator Import"]
        for i, (name, result) in enumerate(zip(test_names, results)):
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"   {name}: {status}")
    
    print("=" * 70)

if __name__ == "__main__":
    asyncio.run(main())
