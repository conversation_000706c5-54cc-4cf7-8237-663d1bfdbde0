# System Patterns - HKEX Dashboard

## Current System Architecture

### Data Processing Pipeline
```
HKEX Website → HTML Download → Parse & Calculate → Database Storage → View Refresh
     ↓              ↓              ↓                ↓                ↓
  Daily Reports → Local Files → Option Greeks → PostgreSQL → Materialized Views
```

### Script Dependencies
1. **UpdateIndexOptionPostgres.py** → option_daily_report
2. **UpdateStockOptionReportPostgres.py** → stock_option_report
3. **copyViewMultiDB.py** → Synchronized remote views

### Key Design Patterns

#### Database Connection Pattern
```python
# SQLAlchemy 2.0 pattern used throughout codebase
from sqlalchemy import create_engine, text

cnx = create_engine(db_url, isolation_level="AUTOCOMMIT")
with cnx.connect() as conn:
    result = conn.execute(text("SELECT * FROM table WHERE id = :id"), {"id": value})
```

#### Error Handling Pattern
```python
# Robust error handling with continuation
try:
    process_data()
    success_count += 1
except Exception as e:
    print(f"Error: {e}")
    continue  # Continue processing other items
```

#### Data Processing Pattern
```python
# Batch processing with progress tracking
for item in data_items:
    try:
        result = process_item(item)
        if result == 1:
            success_count += 1
    except Exception as e:
        log_error(e, item)
        continue

print(f"Successfully processed {success_count} items")
```

## ✅ IMPLEMENTED Dashboard Architecture

### Component Structure - OPERATIONAL
```
React Frontend (Material-UI)
    ↓ (HTTP/WebSocket)
FastAPI Backend
    ↓
Simple Orchestrator (Process Manager)
    ↓
Script Executor (subprocess)
    ↓
Database Monitor (SQLAlchemy 2.0)
    ↓
PostgreSQL Database
```

### Core Components - IMPLEMENTED ✅

#### 1. Process Manager (Simple Orchestrator)
- **✅ Responsibility**: Orchestrate script execution for all three HKEX scripts
- **✅ Pattern**: Async command pattern with real-time status tracking
- **✅ Implementation**: `dashboard/backend/app/services/simple_orchestrator.py`
- **✅ Features**: Start/stop processes, parameter validation, progress monitoring

#### 2. Database Monitor
- **✅ Responsibility**: Track record counts and data quality
- **✅ Pattern**: Direct SQLAlchemy 2.0 integration with existing schema
- **✅ Implementation**: Integrated with existing database patterns
- **✅ Features**: Real-time data access, connection pooling

#### 3. Status Tracker
- **✅ Responsibility**: Maintain process state and history
- **✅ Pattern**: In-memory state management with WebSocket broadcasting
- **✅ Implementation**: Active process tracking in orchestrator
- **✅ Features**: Real-time status updates, process history

#### 4. Notification System
- **✅ Responsibility**: Alert users of completion/errors
- **✅ Pattern**: WebSocket-based real-time communication
- **✅ Implementation**: `dashboard/backend/app/websocket/manager.py`
- **✅ Features**: Live updates, connection management, broadcasting

### Data Flow Patterns

#### Process Execution Flow
```
User Request → Validate → Queue → Execute → Monitor → Update Status → Notify
```

#### Monitoring Flow
```
Database Query → Cache → Transform → Display → Auto-refresh
```

#### Error Handling Flow
```
Error Detection → Log → Classify → Notify → Suggest Action → Track Resolution
```

## Integration Patterns

### Script Integration
- **Wrapper Functions**: Encapsulate existing scripts
- **Status Callbacks**: Inject progress reporting
- **Error Capture**: Redirect stdout/stderr for monitoring

### Database Integration
- **Read-only Monitoring**: Dashboard doesn't modify core data
- **Separate Tracking Tables**: Store dashboard-specific metadata
- **Connection Pooling**: Share connections efficiently

### Real-time Updates
- **Polling Strategy**: Regular database queries for status
- **WebSocket Events**: Push updates to connected clients
- **Caching Layer**: Reduce database load for frequent queries

## Critical Implementation Patterns

### Configuration Management
```python
# Environment-based configuration
import os
from dotenv import load_dotenv

load_dotenv()
DB_URL = os.getenv('WILL9700_DB')
LOG_LEVEL = int(os.getenv('LOG_LEVEL', 20))
```

### Logging Strategy
```python
# Structured logging for troubleshooting
import logging

logger = logging.getLogger(__name__)
logger.info("Process started", extra={
    "script": "UpdateIndexOption",
    "date": trade_date,
    "symbol": symbol
})
```

### Resource Management
```python
# Proper resource cleanup
try:
    with engine.connect() as conn:
        # Database operations
        pass
finally:
    engine.dispose()
```

## Performance Patterns

### Caching Strategy
- **Query Results**: Cache frequent database queries
- **Static Data**: Cache reference data (holidays, symbols)
- **User Sessions**: Cache user preferences and state

### Async Processing
- **Background Tasks**: Long-running processes in background
- **Progress Updates**: Non-blocking status updates
- **Concurrent Queries**: Parallel database operations where safe

### Memory Management
- **Chunked Processing**: Process large datasets in chunks
- **Connection Pooling**: Reuse database connections
- **Garbage Collection**: Explicit cleanup of large objects
