#%%
import math

def BlackScholes(callFlag, S, K, T, r, v):
    #S is underlyng spot price
    #K is the strike price
    #T is time left to expiry
    # r is the risk free rate
    # v is volatility
    #callFlag is true for Call Option, false for put

    d1 = None
    d2 = None

    d1=(math.log(S/K)+(r+v*v/2)*T)/(v* math.sqrt(T))
    d2=d1-v* math.sqrt(T)

    if callFlag:
        return S*CND(d1)-K* math.exp(-r*T)*CND(d2)
    else:
        return K* math.exp(-r*T)*CND(-d2)-S*CND(-d1)


def CND(X):
    L = None
    K = None
    w = None
    a1 = 0.31938153
    a2 = -0.356563782
    a3 = 1.781477937
    a4 = -1.821255978
    a5 = 1.330274429

    L = abs(X)
    K = 1.0 / (1.0 + 0.2316419 * L)
    w = 1.0 - 1.0 / math.sqrt(2.0 * math.pi) * math.exp(-L *L / 2) * (a1 * K + a2 * K *K + a3 * K ** 3 + a4 * K ** 4 + a5 * K ** 5)

    if X < 0.0:
        w= 1.0 - w
    return w


def implVolNR(S, K, r, time, option_price, callFlag):
    if S==0 or K==0 or time==0 or option_price==0:
        print( f"Zero Value input: S={S}, K={K}, r={r}, time={time}, option_price={option_price}")
        return 0.0
    if callFlag == True:
        # Call Option Price below Intrinsic Value
        if option_price <=  (S - K * math.exp(-time * r)):
            print(f"Call Option Price below Intrinsic Value: S={S}, K={K}, r={r}, time={time}, option_price={option_price}, {K * math.exp(-time * r)=}")
            return 0.0
    else:
        # Put Option Price below Intrinsic Value
        if option_price <=  (K * math.exp(-time * r) - S):
            return 0.0
    MAX_ITERATIONS = 100
    ACCURACY = 1.0e-0
    t_sqrt = math.sqrt(time)
    # sigma = (option_price / S) / (0.39894228 * t_sqrt) # find initial value
    sigma = 0.2
    for i in range(0, MAX_ITERATIONS):
        price = BlackScholes(callFlag, S, K,time, r,sigma)
        if round(price,2) ==0.00:
            print(f"Black Scholes Price is Zero: S={S}, K={K}, r={r}, time={time}, option_price={option_price}, sigma={sigma}")
            return 0.0
        #option_price call black scholes(S,K,r,sigma,time)
        diff = option_price - price
        if abs(diff) < ACCURACY:
            return sigma
        d1 = (math.log(S / K) + r * time) / (sigma * t_sqrt) + 0.5 * sigma * t_sqrt
        vega = S * t_sqrt * CND(d1)
        if vega == 0:
            return 0.0
        sigma = sigma + diff / vega
    print(f"Iter > 100: S={S}, K={K}, r={r}, time={time}, option_price={option_price}, sigma={sigma}")
    return sigma # math.nan # return error values

