@echo off
echo ========================================
echo   Docker WebSocket Conflict Fix
echo ========================================
echo.
echo This script will:
echo 1. Stop current Docker containers
echo 2. Remove containers and images to force rebuild
echo 3. Rebuild with websocket conflict fixes:
echo    - Renamed local websocket directory to websockets (eliminates shadowing)
echo    - Updated import statements
echo    - Compatible websocket packages: websockets==15.0.1 + websocket-client==1.8.0
echo    - Removed only conflicting python-socketio package
echo 4. Start development environment
echo.

pause

echo Step 1: Stopping current containers...
docker-compose -f docker-compose.dev.yml down

echo.
echo Step 2: Removing containers and images to force clean rebuild...
docker-compose -f docker-compose.dev.yml down --rmi all --volumes --remove-orphans

echo.
echo Step 3: Rebuilding with websocket conflict fixes...
echo (This includes directory rename, import fixes, and package exclusions)
docker-compose -f docker-compose.dev.yml build --no-cache

echo.
echo Step 4: Starting development environment...
docker-compose -f docker-compose.dev.yml up -d

echo.
echo ========================================
echo   Fix Complete!
echo ========================================
echo.
echo The Docker containers have been rebuilt with comprehensive websocket fixes:
echo - Local websocket directory renamed to websockets (eliminates shadowing)
echo - Import statements updated
echo - Compatible websocket packages installed (websockets==15.0.1 + websocket-client==1.8.0)
echo - Only conflicting python-socketio package removed
echo Both uvicorn WebSocket support AND Selenium should now work correctly!
echo.
echo To check if it's working:
echo   docker-compose -f docker-compose.dev.yml logs -f backend
echo.
echo To test the HKEX fetcher:
echo   docker exec -it hkex_backend_dev python -c "from scripts.hkex_fetcher import selenium_http_get; print('Selenium test:', selenium_http_get('https://httpbin.org/html').status_code)"
echo.
