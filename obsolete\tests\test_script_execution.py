#!/usr/bin/env python3
"""
Test script to debug the UpdateIndexOptionPostgres.py execution error
"""

import sys
import os
import subprocess
import asyncio

# Set up working directory
script_dir = r"O:\Github\MaxPain\MaxPain2024"
script_path = os.path.join(script_dir, "UpdateIndexOptionPostgres.py")

print(f"Testing script execution: {script_path}")
print(f"Working directory: {script_dir}")
print(f"Python executable: {sys.executable}")

# Test 1: Check if script file exists and is readable
print("\n=== Test 1: File accessibility ===")
if os.path.exists(script_path):
    print(f"✓ Script file exists: {script_path}")
    try:
        with open(script_path, 'r') as f:
            content = f.read(100)  # Read first 100 chars
        print(f"✓ Script file is readable (first 100 chars): {content[:50]}...")
    except Exception as e:
        print(f"✗ Error reading script file: {e}")
else:
    print(f"✗ Script file does not exist: {script_path}")

# Test 2: Check Storacle module availability
print("\n=== Test 2: Storacle module import ===")
try:
    sys.path.insert(0, script_dir)
    import Storacle
    print("✓ Storacle module imported successfully")
    print(f"  - Available functions: {[attr for attr in dir(Storacle) if not attr.startswith('_')]}")
except Exception as e:
    print(f"✗ Error importing Storacle: {e}")
    print(f"  - Current Python path: {sys.path[:3]}")

# Test 3: Test environment variables
print("\n=== Test 3: Environment variables ===")
from dotenv import load_dotenv
load_dotenv()

required_vars = ['LOG_LEVEL', 'SQL_ECHO', 'platform', 'WILL9700_DB']
for var in required_vars:
    value = os.environ.get(var)
    if value:
        print(f"✓ {var}: {value}")
    else:
        print(f"✗ {var}: Not set")

# Test 4: Try to run the script with --dry-run
print("\n=== Test 4: Dry run execution ===")
try:
    cmd = [sys.executable, script_path, '--dry-run', '--date', '2024-12-20']
    print(f"Command: {' '.join(cmd)}")
    print(f"Working directory: {script_dir}")
    
    result = subprocess.run(
        cmd,
        cwd=script_dir,
        capture_output=True,
        text=True,
        timeout=30
    )
    
    print(f"Return code: {result.returncode}")
    print(f"STDOUT:\n{result.stdout}")
    if result.stderr:
        print(f"STDERR:\n{result.stderr}")
        
except subprocess.TimeoutExpired:
    print("✗ Script execution timed out after 30 seconds")
except Exception as e:
    print(f"✗ Error running script: {e}")

# Test 5: Try async subprocess (same as orchestrator)
print("\n=== Test 5: Async subprocess execution ===")

async def test_async_subprocess():
    try:
        cmd = [sys.executable, script_path, '--dry-run', '--date', '2024-12-20']
        print(f"Async command: {' '.join(cmd)}")
        
        process = await asyncio.create_subprocess_exec(
            *cmd,
            cwd=script_dir,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        # Wait with timeout
        try:
            stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=30.0)
            print(f"Async return code: {process.returncode}")
            print(f"Async STDOUT:\n{stdout.decode()}")
            if stderr:
                print(f"Async STDERR:\n{stderr.decode()}")
        except asyncio.TimeoutError:
            print("✗ Async script execution timed out")
            process.kill()
            await process.wait()
            
    except Exception as e:
        print(f"✗ Error in async execution: {e}")
        import traceback
        traceback.print_exc()

# Set Windows event loop policy if needed
if os.name == 'nt':
    asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

asyncio.run(test_async_subprocess())

print("\n=== Test Complete ===")
