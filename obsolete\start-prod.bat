@echo off
REM HKEX Dashboard Production Startup Script for Windows

echo Starting HKEX Dashboard in Production Mode...

REM Check if .env file exists
if not exist .env (
    echo Creating .env file from .env.production...
    copy .env.production .env
    echo Please update .env file with your production configuration.
)

REM Create necessary directories
if not exist logs mkdir logs
if not exist nginx\ssl mkdir nginx\ssl

REM Start production environment
echo Starting production containers...
docker-compose up -d --build

REM Check service status
echo Checking service status...
timeout /t 10 /nobreak >nul
docker-compose ps

echo Production environment started!
echo Application: http://localhost
echo Health Check: http://localhost/health

REM Show logs
echo Showing recent logs...
docker-compose logs --tail=20
pause
