{"version": 3, "file": "static/css/main.233b3b67.css", "mappings": "AAAA,KAKE,kCAAmC,CACnC,iCAAkC,CAJlC,mIAEY,CAHZ,QAMF,CAEA,KACE,uEAEF,CAEA,EACE,qBACF", "sources": ["index.css"], "sourcesContent": ["body {\r\n  margin: 0;\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\r\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\r\n    sans-serif;\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n}\r\n\r\ncode {\r\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\r\n    monospace;\r\n}\r\n\r\n* {\r\n  box-sizing: border-box;\r\n}\r\n\r\n/* The following CSS block is intentionally commented out to prevent style conflicts. */\r\n/*\r\n.custom-log-box {\r\n  background-color: #f0f0f0 !important;\r\n  color: #333333 !important;\r\n  padding: 8px;\r\n}\r\n\r\n.custom-log-box > div,\r\n.custom-log-box > div span,\r\n.custom-log-box > div div {\r\n  background-color: transparent !important;\r\n  color: #333333 !important;\r\n  font-family: 'monospace' !important;\r\n}\r\n*/\r\n"], "names": [], "sourceRoot": ""}