import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  CardHeader,
  Grid,
  LinearProgress,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon
} from '@mui/material';
import {
  CheckCircle,
  Warning,
  Error,
  TrendingUp
} from '@mui/icons-material';
import { useQuery } from '@tanstack/react-query';
import { apiService } from '../services/api';

const DataQuality: React.FC = () => {
  const { data: qualityData, isLoading } = useQuery({
    queryKey: ['dataQuality'],
    queryFn: () => apiService.getDataQualityChecks(),
    refetchInterval: 60000, // Refresh every minute
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pass':
        return <CheckCircle color="success" />;
      case 'warning':
        return <Warning color="warning" />;
      case 'error':
        return <Error color="error" />;
      default:
        return <CheckCircle />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pass':
        return 'success';
      case 'warning':
        return 'warning';
      case 'error':
        return 'error';
      default:
        return 'default';
    }
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Data Quality Monitoring
      </Typography>

      <Grid container spacing={3}>
        {/* Overall Score */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardHeader title="Overall Quality Score" />
            <CardContent>
              <Box textAlign="center">
                <Typography variant="h2" component="div" color="primary" gutterBottom>
                  {qualityData?.overall_score.toFixed(1) || '0.0'}%
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={qualityData?.overall_score || 0}
                  sx={{ height: 10, borderRadius: 5 }}
                />                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  {(qualityData?.overall_score ?? 0) >= 95 ? 'Excellent' :
                   (qualityData?.overall_score ?? 0) >= 85 ? 'Good' :
                   (qualityData?.overall_score ?? 0) >= 70 ? 'Fair' : 'Poor'}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Quality Metrics */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardHeader title="Quality Metrics Summary" />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={4}>
                  <Box textAlign="center">
                    <Typography variant="h4" color="success.main">
                      {qualityData?.checks.filter(c => c.status === 'pass').length || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Passed
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={4}>
                  <Box textAlign="center">
                    <Typography variant="h4" color="warning.main">
                      {qualityData?.checks.filter(c => c.status === 'warning').length || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Warnings
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={4}>
                  <Box textAlign="center">
                    <Typography variant="h4" color="error.main">
                      {qualityData?.checks.filter(c => c.status === 'error').length || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Errors
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Detailed Checks */}
        <Grid item xs={12}>
          <Card>
            <CardHeader title="Quality Check Details" />
            <CardContent>
              {isLoading ? (
                <LinearProgress />
              ) : (
                <List>
                  {qualityData?.checks.map((check, index) => (
                    <ListItem key={index} divider>
                      <ListItemIcon>
                        {getStatusIcon(check.status)}
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Box display="flex" alignItems="center" gap={1}>
                            <Typography variant="subtitle1">
                              {check.check_type.replace('_', ' ').toUpperCase()}
                            </Typography>
                            <Chip
                              label={check.table_name}
                              size="small"
                              variant="outlined"
                            />
                            <Chip
                              label={check.status.toUpperCase()}
                              size="small"
                              color={getStatusColor(check.status) as any}
                            />
                            {check.score && (
                              <Chip
                                label={`${check.score.toFixed(1)}%`}
                                size="small"
                                variant="outlined"
                              />
                            )}
                          </Box>
                        }
                        secondary={check.message}
                      />
                    </ListItem>
                  ))}
                </List>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Trend Chart Placeholder */}
        <Grid item xs={12}>
          <Card>
            <CardHeader 
              title="Quality Trends" 
              action={<TrendingUp />}
            />
            <CardContent>
              <Typography color="text.secondary">
                Quality trend charts will be implemented here showing historical data quality scores.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default DataQuality;
