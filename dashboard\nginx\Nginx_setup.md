# Nginx Setup Guide

This guide explains how Nginx is configured and managed in this project, especially with the move to a template-based configuration for flexible, environment-driven deployments.

---

## 1. Overview

Nginx acts as a reverse proxy for both the frontend (React) and backend (FastAPI) services, handling API, WebSocket, and static file routing. To support dynamic port and host configuration, we use a template-based approach for generating the Nginx configuration at container startup.

---

## 2. Template-Based Configuration

### Why Use a Template?

- **Flexibility:** Allows backend and frontend ports/hosts to be set via environment variables.
- **Consistency:** Ensures the running Nginx config always matches your deployment environment.
- **Automation:** No need to manually edit `nginx.conf` for each environment.

### How It Works

1. **Template File:**  
   `nginx.conf.template` contains placeholders like `${BACKEND_PORT}` and `${FRONTEND_PORT}`.

2. **Startup Script:**  
   At container startup, a script runs:
   ```sh
   envsubst < /etc/nginx/nginx.conf.template > /etc/nginx/nginx.conf
   nginx -g 'daemon off;'
   ```
   This substitutes environment variables into the template, generating the actual config used by Nginx.

3. **Environment Variables:**  
   Set in your `.env` file and passed to the Nginx container via `docker-compose.yml`:
   ```env
   FRONTEND_PORT=3080
   BACKEND_PORT=8004
   ```
   ```yaml
   environment:
     - FRONTEND_PORT=${FRONTEND_PORT}
     - BACKEND_PORT=${BACKEND_PORT}
   ```

---

## 3. File Structure

- `dashboard/nginx/nginx.conf.template`  
  The source-of-truth template for Nginx configuration.

- `dashboard/nginx/nginx.conf`  
  **Generated at runtime.** Not edited directly.

- `dashboard/nginx/start-nginx.sh`  
  Startup script that runs `envsubst` and launches Nginx.

- `dashboard/nginx/Dockerfile`  
  Custom Dockerfile to copy the template and script, and set the entrypoint.

---

## 4. Making Changes

- **To update Nginx config:**  
  Edit `nginx.conf.template` only.
- **To change ports/hosts:**  
  Update your `.env` file and rebuild/restart containers.

---

## 5. Removing Old Configs

- The static `nginx.conf` file in the repo is **no longer used** and can be deleted or archived to avoid confusion.

---

## 6. Example Template Snippet

```nginx
upstream backend {
    server backend:${BACKEND_PORT};
}
upstream frontend {
    server frontend:80;
}
server {
    listen 80;
    ...
}
```

---

## 7. Troubleshooting

- **Config not updating?**  
  Make sure you rebuild the Nginx container after changing `.env` or the template.
- **Ports not matching?**  
  Ensure both `.env` and `docker-compose.yml` are in sync and passed to Nginx.
- **Static `nginx.conf` still present?**  
  Remove it to avoid confusion.

---

## 8. Summary

- All Nginx configuration is now dynamic and environment-driven.
- Only `nginx.conf.template` should be edited.
- The actual `nginx.conf` is generated at container startup.

For any changes, update the template and environment variables, then rebuild and restart your containers.
