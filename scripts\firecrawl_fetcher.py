"""
Firecrawl API integration for fetching web content as fallback method.

This module provides a fallback mechanism for fetching web content when direct HTTP requests fail.
It uses the Firecrawl API service to bypass bot detection and access restrictions.

Dependencies:
    pip install firecrawl-py

Environment Variables:
    FIRECRAWL_API_KEY: Your Firecrawl API key

Usage:
    from firecrawl_fetcher import fetch_with_firecrawl
    
    response = fetch_with_firecrawl(url, api_key)
    if response and response.status_code == 200:
        content = response.content
"""

import os
import time
from typing import Optional


class MockResponse:
    """Mock response object to maintain compatibility with requests.Response"""
    
    def __init__(self, content: bytes, status_code: int = 200, url: str = ""):
        self.content = content
        self.text = content.decode('utf-8', errors='ignore')
        self.status_code = status_code
        self.url = url
        self.headers = {'Content-Type': 'text/html'}
    
    def __bool__(self):
        return self.status_code == 200


def fetch_with_firecrawl(url: str, api_key: Optional[str] = None, timeout: int = 60) -> Optional[MockResponse]:
    """
    Fetch web content using Firecrawl API as a fallback method.
    
    Args:
        url: URL to fetch
        api_key: Firecrawl API key (if None, will try to get from environment)
        timeout: Request timeout in seconds
        
    Returns:
        MockResponse object with content, or None if failed
    """
    try:
        from firecrawl import FirecrawlApp
    except ImportError:
        print("❌ Firecrawl not available. Install with: pip install firecrawl-py")
        return None
    
    # Get API key from parameter or environment
    if api_key is None:
        api_key = os.getenv('FIRECRAWL_API_KEY')
    
    if not api_key:
        print("❌ Firecrawl API key not provided. Set FIRECRAWL_API_KEY environment variable.")
        return None
    
    try:
        print(f"🔥 Attempting Firecrawl fetch: {url}")
        
        # Initialize Firecrawl app
        app = FirecrawlApp(api_key=api_key)
        
        # Perform the scrape using simple v1 API format
        start_time = time.time()

        try:
            # Try the simplest possible call first
            result = app.scrape_url(url)
            elapsed_time = time.time() - start_time

            # Check if we got a valid response
            if result:
                # Try to get content from the response (prioritize HTML, fallback to markdown)
                html_content = None

                # Check for common attributes in ScrapeResponse
                if hasattr(result, 'html') and result.html:
                    html_content = result.html
                elif hasattr(result, 'rawHtml') and result.rawHtml:
                    html_content = result.rawHtml
                elif hasattr(result, 'content') and result.content:
                    html_content = result.content
                elif hasattr(result, 'data') and result.data:
                    if hasattr(result.data, 'html') and result.data.html:
                        html_content = result.data.html
                    elif hasattr(result.data, 'content') and result.data.content:
                        html_content = result.data.content
                else:
                    # Try other common attribute names as fallback (including markdown)
                    for attr_name in ['markdown', 'text', 'raw_html', 'html_content']:
                        if hasattr(result, attr_name):
                            attr_value = getattr(result, attr_name)
                            if attr_value:
                                html_content = attr_value
                                break

                # If we found HTML content, return it
                if html_content:
                    # Convert to bytes for compatibility
                    if isinstance(html_content, str):
                        content_bytes = html_content.encode('utf-8')
                    else:
                        content_bytes = html_content

                    print(f"✅ Firecrawl fetch successful: {url}")
                    print(f"   Content length: {len(content_bytes)} bytes")
                    print(f"   Fetch time: {elapsed_time:.2f} seconds")

                    # Return mock response object
                    return MockResponse(content_bytes, 200, url)
                else:
                    print(f"❌ Firecrawl fetch failed: No HTML content found in response for {url}")
                    return None
            else:
                print(f"❌ Firecrawl fetch failed: No response received for {url}")
                return None

        except Exception as inner_e:
            elapsed_time = time.time() - start_time
            print(f"❌ Firecrawl fetch error during scraping: {str(inner_e)}")
            return None
            
    except Exception as e:
        print(f"❌ Firecrawl fetch error for {url}: {str(e)}")
        
        # Handle specific Firecrawl errors
        if "rate limit" in str(e).lower():
            print("   Rate limit exceeded. Consider upgrading your Firecrawl plan.")
        elif "credits" in str(e).lower():
            print("   Insufficient credits. Check your Firecrawl account balance.")
        elif "timeout" in str(e).lower():
            print("   Request timeout. The page may be taking too long to load.")
        
        return None


def test_firecrawl_connection(api_key: Optional[str] = None) -> bool:
    """
    Test Firecrawl API connection and credentials.
    
    Args:
        api_key: Firecrawl API key (if None, will try to get from environment)
        
    Returns:
        True if connection successful, False otherwise
    """
    try:
        from firecrawl import FirecrawlApp
    except ImportError:
        print("❌ Firecrawl not available. Install with: pip install firecrawl-py")
        return False
    
    # Get API key from parameter or environment
    if api_key is None:
        api_key = os.getenv('FIRECRAWL_API_KEY')
    
    if not api_key:
        print("❌ Firecrawl API key not provided. Set FIRECRAWL_API_KEY environment variable.")
        return False
    
    try:
        print("🔥 Testing Firecrawl API connection...")
        
        # Initialize Firecrawl app
        app = FirecrawlApp(api_key=api_key)
        
        # Test with a simple, reliable URL
        test_url = "https://httpbin.org/html"
        result = app.scrape_url(test_url)
        
        # Debug: Print the actual result structure
        print(f"Debug: Test result type: {type(result)}")

        if result and (hasattr(result, 'html') or hasattr(result, 'content') or hasattr(result, 'data')):
            print("✅ Firecrawl API connection successful")
            return True
        else:
            print("❌ Firecrawl API test failed: No content returned")
            if result:
                attrs = [attr for attr in dir(result) if not attr.startswith('_')]
                print(f"Debug: Available attributes: {attrs}")
            return False
            
    except Exception as e:
        print(f"❌ Firecrawl API test failed: {str(e)}")
        return False


def get_firecrawl_status(api_key: Optional[str] = None) -> dict:
    """
    Get Firecrawl account status and usage information.
    
    Args:
        api_key: Firecrawl API key (if None, will try to get from environment)
        
    Returns:
        Dictionary with account status information
    """
    try:
        from firecrawl import FirecrawlApp
    except ImportError:
        return {"error": "Firecrawl not available. Install with: pip install firecrawl-py"}
    
    # Get API key from parameter or environment
    if api_key is None:
        api_key = os.getenv('FIRECRAWL_API_KEY')
    
    if not api_key:
        return {"error": "Firecrawl API key not provided"}
    
    try:
        # Initialize Firecrawl app
        app = FirecrawlApp(api_key=api_key)
        
        # Note: Actual status endpoint may vary depending on Firecrawl API version
        # This is a placeholder - check Firecrawl documentation for actual status methods
        return {
            "status": "connected",
            "api_key_valid": True,
            "message": "Firecrawl API is available"
        }
        
    except Exception as e:
        return {
            "status": "error",
            "api_key_valid": False,
            "error": str(e)
        }


if __name__ == "__main__":
    # Test the Firecrawl integration
    print("Testing Firecrawl integration...")
    
    # Test connection
    if test_firecrawl_connection():
        print("✅ Firecrawl is ready to use")
        
        # Test with a sample URL
        test_url = "https://httpbin.org/html"
        response = fetch_with_firecrawl(test_url)
        
        if response and response.status_code == 200:
            print(f"✅ Sample fetch successful: {len(response.content)} bytes")
        else:
            print("❌ Sample fetch failed")
    else:
        print("❌ Firecrawl connection test failed")
