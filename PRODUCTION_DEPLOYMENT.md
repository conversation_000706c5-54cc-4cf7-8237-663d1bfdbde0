# Production Deployment Guide

## Overview
The production configuration is now ready for deployment with the following improvements:

### Key Production Features
1. **Nginx Reverse Proxy**: Routes traffic between frontend and backend services with dynamic configuration
2. **WebSocket Support**: Full WebSocket connectivity with proper proxy configuration
3. **Flexible Port Configuration**: Environment-based backend port configuration via .env variables
4. **Multi-worker FastAPI**: Uses 4 workers for better performance
5. **Optimized Docker Images**: Multi-stage builds for smaller images
6. **Proper Networking**: Separate networks for frontend and backend
7. **Enhanced Security**: Security headers and rate limiting
8. **Logging**: Centralized logging configuration
9. **Health Checks**: Built-in health monitoring
10. **Dynamic Nginx Templates**: Template-based nginx configuration with environment variable substitution

## Services Architecture

### System Architecture Overview

```mermaid
flowchart TD
    %% External Layer
    Internet(["`🌐 **Internet Users**`"]) 
    
    %% Load Balancer Layer
    Internet --> Nginx["`🔧 **Nginx Reverse Proxy**
    - Port 80/443
    - Rate Limiting
    - SSL Termination
    - Static File Serving`"]
    
    %% Application Layer
    Nginx --> Frontend["`⚛️ **React Frontend**
    - TypeScript
    - Tailwind CSS
    - Static Build
    - SPA Routing`"]
    
    Nginx --> Backend["`🚀 **FastAPI Backend**
    - Python 3.11+
    - 4 Uvicorn Workers
    - SQLAlchemy ORM
    - RESTful APIs`"]
    
    %% Async Processing Layer
    Backend --> CeleryWorker["`⚙️ **Celery Worker**
    - Async Task Processing
    - 2 Worker Concurrency
    - Background Jobs`"]
    
    Backend --> CeleryBeat["`⏰ **Celery Beat**
    - Task Scheduler
    - Periodic Jobs
    - Cron-like Scheduling`"]
    
    %% Data Layer
    Backend --> Redis["`🗄️ **Redis Cache**
    - In-Memory Storage
    - Celery Message Broker
    - Session Store`"]
    
    Backend --> PostgreSQL["`🐘 **PostgreSQL**
    - Primary Database
    - ACID Compliance
    - Relational Data`"]
    
    CeleryWorker --> Redis
    CeleryBeat --> Redis
    
    %% Storage Layer
    Backend --> Logs["`📋 **Application Logs**
    - Centralized Logging
    - Error Tracking
    - Debug Information`"]
    
    CeleryWorker --> Output["`📄 **HTML Output**
    - Generated Reports
    - Data Exports
    - File Storage`"]
    
    %% Docker Network Isolation
    subgraph Docker["`🐳 **Docker Environment**`"]
        subgraph FrontendNet["`🌐 **Frontend Network**`"]
            Frontend
            Nginx
        end
        
        subgraph BackendNet["`🔧 **Backend Network**`"]
            Backend
            CeleryWorker
            CeleryBeat
            Redis
        end
        
        subgraph External["`🌍 **External Services**`"]
            PostgreSQL
            Logs
            Output
        end
    end
    
    %% Styling
    classDef internetStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px
    classDef proxyStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef frontendStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef backendStyle fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef dataStyle fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef storageStyle fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef networkStyle fill:#f5f5f5,stroke:#616161,stroke-width:1px,stroke-dasharray: 5 5
    
    class Internet internetStyle
    class Nginx proxyStyle
    class Frontend frontendStyle
    class Backend,CeleryWorker,CeleryBeat backendStyle
    class Redis,PostgreSQL dataStyle
    class Logs,Output storageStyle
    class FrontendNet,BackendNet,External networkStyle
```

### Technology Stack Diagram

```mermaid
flowchart LR
    %% Frontend Technologies
    subgraph Frontend["`🎨 **Frontend Stack**`"]
        React["`⚛️ **React 18**
        - Component-based UI
        - Virtual DOM
        - Hooks & Context`"]
        
        TypeScript["`📘 **TypeScript**
        - Type Safety
        - IntelliSense
        - Compile-time Checks`"]
        
        Tailwind["`🎨 **Tailwind CSS**
        - Utility-first CSS
        - Responsive Design
        - Custom Components`"]
        
        React --> TypeScript
        TypeScript --> Tailwind
    end
    
    %% Backend Technologies
    subgraph Backend["`⚙️ **Backend Stack**`"]
        FastAPI["`🚀 **FastAPI**
        - Async/Await Support
        - Auto API Documentation
        - Pydantic Validation`"]
        
        Python["`🐍 **Python 3.11+**
        - Latest Language Features
        - Performance Improvements
        - Type Hints`"]
        
        SQLAlchemy["`🗄️ **SQLAlchemy**
        - ORM & Core
        - Database Migrations
        - Query Builder`"]
        
        Uvicorn["`⚡ **Uvicorn**
        - ASGI Server
        - High Performance
        - WebSocket Support`"]
        
        Python --> FastAPI
        FastAPI --> SQLAlchemy
        FastAPI --> Uvicorn
    end
    
    %% Data & Cache Layer
    subgraph Data["`💾 **Data Layer**`"]
        PostgreSQL["`🐘 **PostgreSQL**
        - ACID Transactions
        - JSON Support
        - Full-text Search`"]
        
        Redis["`🔴 **Redis**
        - In-Memory Cache
        - Pub/Sub Messaging
        - Data Structures`"]
        
        Pandas["`🐼 **Pandas**
        - Data Analysis
        - DataFrame Operations
        - CSV/Excel Processing`"]
        
        NumPy["`🔢 **NumPy**
        - Numerical Computing
        - Array Operations
        - Mathematical Functions`"]
        
        PostgreSQL --> Redis
        Pandas --> NumPy
    end
    
    %% Infrastructure Layer
    subgraph Infrastructure["`🏗️ **Infrastructure**`"]
        Docker["`🐳 **Docker**
        - Containerization
        - Consistent Environments
        - Easy Deployment`"]
        
        DockerCompose["`📋 **Docker Compose**
        - Multi-container Apps
        - Service Orchestration
        - Development Workflow`"]
        
        Nginx["`🌐 **Nginx**
        - Reverse Proxy
        - Load Balancing
        - Static File Serving`"]
        
        Celery["`⚙️ **Celery**
        - Distributed Task Queue
        - Async Processing
        - Scheduled Jobs`"]
        
        Docker --> DockerCompose
        Docker --> Nginx
        Docker --> Celery
    end
    
    %% Connections between stacks
    Frontend --> Backend
    Backend --> Data
    Backend --> Infrastructure
    
    %% Styling
    classDef frontendColor fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef backendColor fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef dataColor fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef infraColor fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    
    class React,TypeScript,Tailwind frontendColor
    class FastAPI,Python,SQLAlchemy,Uvicorn backendColor
    class PostgreSQL,Redis,Pandas,NumPy dataColor
    class Docker,DockerCompose,Nginx,Celery infraColor
```

### Request Flow Diagram

```mermaid
flowchart TD
    Start(["`👤 **User Request**`"]) --> Route{"`🔀 **Nginx Routing**`"}
    
    Route -->|"/ (Root)"| StaticFiles["`📁 **Serve React App**
    - index.html
    - Static Assets
    - Client-side Routing`"]
    
    Route -->|"/api/*"| API["`🔌 **FastAPI Backend**
    - REST Endpoints
    - WebSocket Support
    - Auto Documentation`"]
    
    Route -->|"/docs"| Docs["`📚 **API Documentation**
    - Swagger UI
    - Interactive Testing
    - Schema Validation`"]
    
    Route -->|"/health"| Health["`💚 **Health Check**
    - Service Status
    - Database Connectivity
    - Redis Connectivity`"]
    
    API --> Auth{"`🔐 **Authentication**`"}
    Auth -->|Valid| Process["`⚡ **Process Request**
    - Business Logic
    - Data Validation
    - Response Generation`"]
    Auth -->|Invalid| Error401["`❌ **401 Unauthorized**`"]
    
    Process --> DBQuery{"`📊 **Database Required?**`"}
    DBQuery -->|Yes| Database["`🐘 **PostgreSQL Query**
    - CRUD Operations
    - Complex Queries
    - Transactions`"]
    DBQuery -->|No| Cache{"`💨 **Cache Check**`"}
    
    Database --> Cache
    Cache -->|Hit| FastResponse["`⚡ **Cached Response**`"]
    Cache -->|Miss| SlowQuery["`🔄 **Database Query**`"]
    SlowQuery --> UpdateCache["`💾 **Update Cache**`"]
    
    Process --> Async{"`⏳ **Async Task?**`"}
    Async -->|Yes| CeleryTask["`⚙️ **Queue Celery Task**
    - Background Processing
    - Email Sending
    - Report Generation`"]
    Async -->|No| SyncResponse["`📤 **Synchronous Response**`"]
    
    CeleryTask --> TaskQueue["`📋 **Redis Task Queue**`"]
    TaskQueue --> Worker["`👷 **Celery Worker**
    - Process Task
    - Update Status
    - Store Results`"]
    
    StaticFiles --> Response["`📱 **Client Response**`"]
    FastResponse --> Response
    UpdateCache --> Response
    SyncResponse --> Response
    Docs --> Response
    Health --> Response
    Error401 --> Response
    
    Worker --> Notification["`🔔 **Task Completion**
    - WebSocket Update
    - Email Notification
    - Status Update`"]
    
    %% Styling
    classDef userStyle fill:#e1f5fe,stroke:#0277bd,stroke-width:3px
    classDef routingStyle fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef processStyle fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef dataStyle fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef asyncStyle fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef responseStyle fill:#f1f8e9,stroke:#689f38,stroke-width:2px
    classDef errorStyle fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    
    class Start userStyle
    class Route,Auth,DBQuery,Cache,Async routingStyle
    class API,Process,SlowQuery,UpdateCache processStyle
    class Database,TaskQueue dataStyle
    class CeleryTask,Worker asyncStyle
    class StaticFiles,Docs,Health,FastResponse,SyncResponse,Response,Notification responseStyle
    class Error401 errorStyle
```

### Detailed Component Flow
```
Internet → Nginx (Port 80) → Frontend (Port 80) / Backend (Port 8000)
                           → Redis (Internal)
                           → Celery Worker (Internal)
                           → Celery Beat (Internal)
```

## Quick Start

### 1. Production Deployment
```bash
# Linux/Mac
./start-app.sh prod

# Windows
run.bat prod
```

### 2. Development Mode
```bash
# Linux/Mac
./start-app.sh dev

# Windows
run.bat dev
```

## Environment Configuration

### Backend Port Configuration
The system now supports flexible backend port configuration via environment variables:

1. **Set Backend Port in .env file:**
```bash
# .env file
BACKEND_PORT=8004
```

2. **How it works:**
- Backend reads `BACKEND_PORT` from environment (defaults to 8000 if not set)
- Docker Compose uses the environment variable for service configuration
- Nginx template system dynamically configures proxy settings

### Dynamic Nginx Configuration
The nginx configuration uses a template system for dynamic port assignment:

**Files involved:**
- `dashboard/nginx/nginx.conf.template` - Template with `${BACKEND_PORT}` placeholder
- `dashboard/nginx/start-nginx.sh` - Script that substitutes environment variables
- `dashboard/nginx/Dockerfile` - Custom nginx container with envsubst support

**Template substitution process:**
```bash
# start-nginx.sh performs this substitution:
envsubst '${BACKEND_PORT}' < /etc/nginx/nginx.conf.template > /etc/nginx/nginx.conf
nginx -g 'daemon off;'
```

## WebSocket Configuration

### WebSocket Support Features
The system provides comprehensive WebSocket support:

1. **Dual WebSocket Endpoints:**
   - `/ws` - Primary WebSocket endpoint
   - `/ws/` - Alternative endpoint for compatibility

2. **Nginx WebSocket Proxy:**
   - Proper WebSocket headers (`Upgrade`, `Connection`)
   - HTTP version 1.1 support
   - Dynamic backend port resolution

3. **Connection Management:**
   - Automatic connection retry logic
   - Cross-origin support via CORS
   - Real-time data streaming capability

### WebSocket Endpoint Implementation
```python
# Backend WebSocket endpoints
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    # Primary WebSocket handler
    pass

@app.websocket("/ws/")
async def websocket_endpoint_alt(websocket: WebSocket):
    # Alternative endpoint - delegates to primary handler
    return await websocket_endpoint(websocket)
```

### Nginx WebSocket Configuration
```nginx
# Dynamic WebSocket proxy configuration
upstream backend {
    server backend:${BACKEND_PORT};
}

location /ws {
    proxy_pass http://backend;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
```

## Production Configuration

### Environment Variables
Copy and configure the environment template:
```bash
cp .env.prod.template .env.prod
# Edit .env.prod with your production values
```

### Required Directories
Ensure these directories exist on your host system:
- `C:\output\MaxPain\logs` - Application logs
- `C:\output\MaxPain` - HTML output files

### Database Configuration
Update the `DATABASE_URL` in docker-compose.yml or .env.prod file with your production database credentials.

## Monitoring and Maintenance

### View Logs
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f backend
docker-compose logs -f nginx
```

### Health Checks
- Application: http://localhost/health
- API Documentation: http://localhost/docs

### Stop Services
```bash
docker-compose down
```

### Update and Rebuild
```bash
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

## Security Considerations

### 1. Environment Variables
- Set proper secret keys in production
- Use environment-specific configurations
- Never commit sensitive data to version control

### 2. Network Security
- Frontend and backend are on separate networks
- Only nginx exposes public ports
- Redis and database are internal only

### 3. Rate Limiting
- API endpoints: 10 requests per second
- WebSocket endpoints: 5 requests per second
- Configurable burst limits

## Performance Optimization

### 1. Backend
- 4 FastAPI workers for concurrent request handling
- Optimized Celery worker concurrency (2 workers)
- Proper resource allocation

### 2. Frontend
- Static file caching (1 year)
- Gzip compression enabled
- Optimized build process

### 3. Database
- Connection pooling via SQLAlchemy
- Persistent Redis data storage

## Troubleshooting

### Common Issues
1. **Port conflicts**: Ensure ports 80 and 443 are available
2. **Volume permissions**: Check Windows volume mounting permissions
3. **Database connectivity**: Verify database server is accessible
4. **Log directories**: Ensure output directories exist

### WebSocket Troubleshooting
**Issue: WebSocket connection failed to 'ws://localhost/ws'**

**Symptoms:**
- Frontend shows "WebSocket connection failed"
- Browser console shows connection refused errors
- Real-time features not working

**Solutions:**
1. **Check Backend Port Configuration:**
```bash
# Verify BACKEND_PORT in .env file
cat .env | grep BACKEND_PORT

# Check if backend container is using correct port
docker-compose ps backend
```

2. **Verify Nginx Configuration:**
```bash
# Check if nginx template was processed correctly
docker-compose exec nginx cat /etc/nginx/nginx.conf

# Look for correct backend port in upstream config
docker-compose exec nginx grep -A 5 "upstream backend" /etc/nginx/nginx.conf
```

3. **Test WebSocket Endpoint Directly:**
```bash
# Test backend WebSocket endpoint directly
curl -i -N -H "Connection: Upgrade" -H "Upgrade: websocket" \
     -H "Sec-WebSocket-Version: 13" -H "Sec-WebSocket-Key: test" \
     http://localhost:8004/ws
```

4. **Check Container Logs:**
```bash
# Check nginx logs for WebSocket errors
docker-compose logs nginx | grep -i websocket

# Check backend logs for WebSocket connections
docker-compose logs backend | grep -i websocket
```

**Issue: nginx template not processing environment variables**

**Symptoms:**
- nginx.conf contains `${BACKEND_PORT}` literal text
- nginx returns 502 Bad Gateway errors

**Solutions:**
1. **Rebuild nginx container:**
```bash
docker-compose build --no-cache nginx
docker-compose up -d nginx
```

2. **Verify envsubst availability:**
```bash
# Check if envsubst is available in nginx container
docker-compose exec nginx which envsubst
```

3. **Manual template processing:**
```bash
# Process template manually
docker-compose exec nginx sh -c 'envsubst '\''${BACKEND_PORT}'\'' < /etc/nginx/nginx.conf.template > /etc/nginx/nginx.conf && nginx -s reload'
```

### Debug Commands
```bash
# Check container status
docker-compose ps

# Check networks
docker network ls

# Inspect specific service
docker-compose logs backend

# Enter container for debugging
docker-compose exec backend bash
```

## SSL/HTTPS Setup (Optional)

To enable HTTPS, update nginx configuration and mount SSL certificates:

1. Add SSL certificates to `./certs/` directory
2. Update `nginx.conf` for HTTPS
3. Expose port 443 in docker-compose.yml

## Scaling

For high-traffic deployments:
1. Increase backend worker count
2. Add more Celery workers
3. Use external Redis cluster
4. Implement load balancing
5. Use container orchestration (Docker Swarm/Kubernetes)
