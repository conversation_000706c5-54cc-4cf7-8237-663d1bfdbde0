import React, { useState, useEffect } from 'react';
import {
  ThemeProvider,
  createTheme,
  CssBaseline,
  Container,
  Typography,
  Box,
  Card,
  CardContent,
  Alert,
  Grid,
  Chip,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText
} from '@mui/material';
import { Cable, CableOutlined, Dashboard, PlayArrow, BarChart, History } from '@mui/icons-material';
import { useWebSocket } from './hooks/useSimpleWebSocket';
import { getWebSocketUrl, updateFavicon } from './config/environment';
import ProcessHistory from './components/ProcessHistory';
import RealTimeLogViewer from './components/RealTimeLogViewer';
import ProcessStarter from './components/ProcessStarter';
import { calculateDuration } from './utils/duration';

const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
});

const App: React.FC = () => {
  const [apiStatus, setApiStatus] = useState<'loading' | 'connected' | 'error'>('loading');
  const [apiData, setApiData] = useState<any>(null);
  const [formattedProcessTypes, setFormattedProcessTypes] = useState<Record<string, any> | null>(null); // For ProcessStarter
  const [activeProcesses, setActiveProcesses] = useState<any[]>([]);
  const [currentTab, setCurrentTab] = useState(0);
  const [refreshKey, setRefreshKey] = useState(0); // For forcing re-renders  // WebSocket connection
  const { lastMessage, readyState, messages } = useWebSocket(getWebSocketUrl());

  const getWebSocketStatus = () => {
    switch (readyState) {
      case WebSocket.CONNECTING:
        return { status: 'connecting', text: 'Connecting...', color: 'info' as const };
      case WebSocket.OPEN:
        return { status: 'connected', text: 'Connected', color: 'success' as const };
      case WebSocket.CLOSING:
        return { status: 'closing', text: 'Closing...', color: 'warning' as const };
      case WebSocket.CLOSED:
        return { status: 'closed', text: 'Disconnected', color: 'error' as const };
      default:
        return { status: 'unknown', text: 'Unknown', color: 'default' as const };
    }
  };
  useEffect(() => {
    // Initialize environment-specific favicon
    updateFavicon();
    
    // Test API connection
    fetch('/api/v1/')
      .then(res => res.json())
      .then(data => {
        setApiData(data);
        setApiStatus('connected');
      })
      .catch(err => {
        console.error('API connection failed:', err);
        setApiStatus('error');
      });    // Fetch process types
    fetch('/api/v1/processes/types')
      .then(res => res.json())
      .then(data => {
        if (data && Array.isArray(data.process_types)) {
          const transformedTypes = data.process_types.reduce((acc: Record<string, any>, pType: any) => {
            // Check if pType is a valid object and has a value property before using it
            if (pType && typeof pType === 'object' && pType.value) {
              acc[pType.value] = pType;
            } else {
              console.warn('Invalid or incomplete process type object found and skipped:', pType);
            }
            return acc;
          }, {});
          setFormattedProcessTypes(transformedTypes);
        } else {
          console.error('Fetched process types data is not in the expected format:', data);
          setFormattedProcessTypes({}); // Set to empty object or handle error appropriately
        }
      })
      .catch(err => {
        console.error('Failed to fetch process types:', err);
        setFormattedProcessTypes({}); // Set to empty object or handle error appropriately
      });
  }, []);  // Handle WebSocket messages
  useEffect(() => {
    if (lastMessage) {
      // Log the raw WebSocket message
      console.log('App.tsx: Received WebSocket message:', JSON.stringify(lastMessage, null, 2));
        if (lastMessage.type === 'process_update' && lastMessage.data) {
        setActiveProcesses(prev => {
          const existingIndex = prev.findIndex(p => p.task_id === lastMessage.data.task_id);
          let incomingData = { ...lastMessage.data };
          
          const taskId = incomingData.task_id;
          if (!taskId || taskId === 'undefined' || taskId === 'null' || taskId === '(...)' || taskId.toString().trim() === '') {
            console.warn(`App.tsx: Filtering out ghost process with invalid task_id:`, taskId);
            return prev; 
          }
          
          // Normalize datetime fields: backend sends ISO strings
          if (incomingData.completed_at && !incomingData.end_time) {
            incomingData.end_time = incomingData.completed_at;
            console.log(`App.tsx: Normalized completed_at to end_time: ${incomingData.end_time}`);
          }
          if (incomingData.started_at && !incomingData.start_time) {
            incomingData.start_time = incomingData.started_at;
            console.log(`App.tsx: Normalized started_at to start_time: ${incomingData.start_time}`);
          }

          // Ensure end_time is present for terminal states, even if it's from updated_at via orchestrator logic
          const isTerminalState = incomingData.status === 'completed' || 
                                incomingData.status === 'failed' || 
                                incomingData.status === 'error' || 
                                incomingData.status === 'cancelled';

          if (isTerminalState && !incomingData.end_time && incomingData.updated_at) {
            // This case should ideally be handled by the backend ensuring end_time is set.
            // If frontend has to do this, it means backend might not have set it.
            console.warn(`App.tsx: Process ${incomingData.task_id} in terminal state '${incomingData.status}' but missing 'end_time'. Using 'updated_at' as fallback: ${incomingData.updated_at}`);
            incomingData.end_time = incomingData.updated_at; 
          } else if (isTerminalState && !incomingData.end_time) {
            // If still no end_time, and it's terminal, this is an issue.
            // For robustness, set to current time, but log an error.
            const now = new Date().toISOString();
            console.error(`App.tsx: CRITICAL - Process ${incomingData.task_id} in terminal state '${incomingData.status}' but 'end_time' is missing and 'updated_at' was not available or used. Setting to current time: ${now}`);
            incomingData.end_time = now;
          }
          
          if (existingIndex !== -1) { // Process exists, update it
            const updatedProcesses = [...prev];
            const oldProcessData = updatedProcesses[existingIndex];
            const newProcessData = { ...oldProcessData, ...incomingData };
            updatedProcesses[existingIndex] = newProcessData;
            
            console.log(`App.tsx: Updating existing process ${incomingData.task_id}.`);
            console.log('  Status:', newProcessData.status, 'End time:', newProcessData.end_time);
            // Use isTerminalState here
            if (isTerminalState) {
              console.log(`App.tsx: Process ${newProcessData.task_id} is ${newProcessData.status}. Scheduling removal in 30s.`);
              setTimeout(() => {
                setActiveProcesses(current => {
                  console.log(`App.tsx: Removing completed/terminal process ${newProcessData.task_id} from active list.`);
                  return current.filter(p => p.task_id !== newProcessData.task_id);
                });
              }, 30000); // Keep completed/terminal processes visible for 30 seconds
            }
            return updatedProcesses;
          } else { // New process from WebSocket
            // More robust duplicate check - check both task_id and combination of fields
            const isDuplicate = prev.some(p => 
              p.task_id === incomingData.task_id || 
              (p.process_type === incomingData.process_type && 
               p.start_time === incomingData.start_time &&
               Math.abs(new Date(p.start_time || 0).getTime() - new Date(incomingData.start_time || 0).getTime()) < 2000) // Within 2 seconds
            );
            
            if (isDuplicate) {
              console.warn(`App.tsx: Duplicate process detected on WebSocket add, skipping.`, {
                taskId: incomingData.task_id,
                processType: incomingData.process_type,
                startTime: incomingData.start_time
              });
              return prev;
            }
            console.log(`App.tsx: Adding new process from WebSocket ${incomingData.task_id}:`, JSON.stringify(incomingData, null, 2));
            return [...prev, incomingData];
          }
        });
      }
    }
  }, [lastMessage]);

  const startProcess = async (processType: string, parameters: Record<string, any> = {}) => {
    try {
      const response = await fetch('/api/v1/processes/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          process: processType, // Changed from process_type to process
          parameters
        })
      });

      if (!response.ok) {
        let errorDetail = response.statusText;
        let rawResponseText = ''; // Variable to store raw response
        try {
          const responseCloneForText = response.clone();
          rawResponseText = await responseCloneForText.text();
          const errorData = await response.json();
          console.error(`Detailed ${response.status} error from backend (JSON) in App.tsx:`, JSON.stringify(errorData, null, 2));
          if (errorData && errorData.detail) {
            if (Array.isArray(errorData.detail)) {
              errorDetail = errorData.detail.map((err: any) => {
                const loc = err.loc ? err.loc.join(' -> ') : 'N/A';
                return `Field: '${loc}', Message: ${err.msg} (type: ${err.type})`;
              }).join('; ');
            } else if (typeof errorData.detail === 'string') {
              errorDetail = errorData.detail;
            } else {
              errorDetail = JSON.stringify(errorData.detail);
            }
          }
        } catch (e) {
          console.error(`Could not parse error JSON from ${response.status} response in App.tsx. Raw response text:`, rawResponseText, "Error during JSON parsing:", e);
          if (rawResponseText) {
            errorDetail = rawResponseText;
          }
        }
        throw new Error(`Failed to start process in App.tsx: ${response.status} - ${errorDetail}`);
      }      const data = await response.json();
      console.log('Process started:', data);
      
      // Longer delay to ensure WebSocket updates arrive first, then add only if not already present
      setTimeout(() => {
        setActiveProcesses(prev => {
          // Enhanced duplicate detection - check multiple criteria
          const isDuplicate = prev.some(p => 
            p.task_id === data.task_id || 
            (p.process_type === processType && 
             p.status === 'starting' &&
             // Check if there's a very recent process of the same type
             new Date().getTime() - new Date(p.start_time || 0).getTime() < 5000) // Within 5 seconds
          );
          
          if (isDuplicate) {
            console.log(`App.tsx: Process ${data.task_id} already exists or duplicate detected, skipping manual add`);
            return prev;
          }
          
          console.log(`App.tsx: Adding started process ${data.task_id} to activeProcesses`);
          return [...prev, {
            task_id: data.task_id,
            process_type: processType,
            status: 'starting',
            start_time: new Date().toISOString(),
            parameters
          }];
        });
      }, 200); // Increased delay to 200ms to allow WebSocket message to arrive first
    } catch (err) {
      console.error('Failed to start process:', err);
      throw err;
    }
  };
  // Periodic cleanup of very old processes (failsafe)
  useEffect(() => {
    const cleanup = setInterval(() => {
      setActiveProcesses(prev => {
        const now = new Date().getTime();
        const cleaned = prev.filter(p => {
          // Remove processes older than 30 minutes to prevent memory leaks
          const startTime = new Date(p.start_time || 0).getTime();
          const age = now - startTime;
          if (age > 30 * 60 * 1000) { // 30 minutes
            console.log(`App.tsx: Cleaning up old process ${p.task_id} (age: ${Math.round(age / 60000)}m)`);
            return false;
          }
          return true;
        });
        return cleaned.length !== prev.length ? cleaned : prev;
      });
    }, 60000); // Check every minute

    return () => clearInterval(cleanup);
  }, []);

  // Periodic refresh for real-time duration updates
  useEffect(() => {
    const refreshInterval = setInterval(() => {
      // Force a re-render by updating the refresh key
      setRefreshKey(prev => prev + 1);
    }, 1000); // Update durations every second

    return () => clearInterval(refreshInterval);
  }, []);

  const wsStatus = getWebSocketStatus();

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Container maxWidth="xl">
        <Box sx={{ my: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            HKEX Data Processing Dashboard
          </Typography>
          
          {/* Connection Status */}
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Backend API Status
                  </Typography>
                  {apiStatus === 'loading' && (
                    <Alert severity="info">Connecting to backend...</Alert>
                  )}
                  {apiStatus === 'connected' && (
                    <Alert severity="success">
                      Connected to backend API v{apiData?.version}
                    </Alert>
                  )}
                  {apiStatus === 'error' && (
                    <Alert severity="error">
                      Failed to connect to backend API
                    </Alert>
                  )}
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    WebSocket Status
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {wsStatus.status === 'connected' ? <Cable /> : <CableOutlined />}
                    <Chip 
                      label={wsStatus.text} 
                      color={wsStatus.color}
                      variant={wsStatus.status === 'connected' ? 'filled' : 'outlined'}
                    />
                  </Box>
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    Messages received: {messages.length}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Navigation Tabs */}
<Card sx={{ mb: 3 }}>            <Tabs
              value={currentTab}
              onChange={(_, newValue) => setCurrentTab(newValue)}
              variant="fullWidth"
            >
              <Tab icon={<History />} label="Process History" />
              <Tab icon={<PlayArrow />} label="Process Control" />
              <Tab icon={<BarChart />} label="Monitoring" />
            </Tabs>
          </Card>

          {/* Tab Content */}

          {currentTab === 1 && formattedProcessTypes && ( // Use formattedProcessTypes
            <ProcessStarter 
              processTypes={formattedProcessTypes} 
              onStartProcess={startProcess} 
              // You might want to pass onProcessStarted if needed, e.g., for notifications
              // pythonInterpreter="path/to/your/python" // Optional: if you want to override ProcessStarter's default
            />
          )}
          {currentTab === 2 && (
            <Grid container spacing={3}>              {/* Active Processes Summary */}
              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                      <Typography variant="h6">
                        Process Monitoring ({activeProcesses.length} active)
                      </Typography>
                      <Box sx={{ display: 'flex', gap: 1 }}>                        <Chip 
                          label="Debug Info" 
                          size="small" 
                          variant="outlined"
                          onClick={() => {
                            console.log('Current activeProcesses state:', JSON.stringify(activeProcesses, null, 2));
                            console.log('WebSocket readyState:', readyState);
                            console.log('Messages received:', messages.length);
                            // Debug duration calculations
                            activeProcesses.forEach(p => {
                              console.log(`Process ${p.task_id}:`, {
                                status: p.status,
                                start_time: p.start_time,
                                end_time: p.end_time,
                                // Corrected to use endTime
                                duration: calculateDuration({ startTime: p.start_time, endTime: p.end_time, status: p.status })
                              });
                            });
                          }}
                        />
                        <Chip 
                          label="Clear All" 
                          size="small" 
                          color="warning"
                          variant="outlined"
                          onClick={() => {
                            console.log('Manually clearing all active processes');
                            setActiveProcesses([]);
                          }}
                        />
                      </Box>
                    </Box>                    {activeProcesses.length === 0 ? (
                      <Alert severity="info">No active processes</Alert>
                    ) : (
                      <List>                        {activeProcesses
                          .filter(process => {
                            // Filter out ghost processes with invalid task_ids
                            const taskId = process.task_id;
                            return taskId && 
                                   taskId !== 'undefined' && 
                                   taskId !== 'null' && 
                                   taskId !== '(...)' && 
                                   taskId.toString().trim() !== '';
                          })
                          .map((process) => (
                          <ListItem key={`${process.task_id}-${refreshKey}`} divider>
                            <ListItemText
                              primary={`${process.process_type} (${process.task_id ? process.task_id.slice(-8) : '...'})`}
                              secondaryTypographyProps={{ component: 'div' }} // Allow multi-line secondary text
                              secondary={
                                <>
                                  <div>Status: {process.status}</div>
                                  <div>Started: {process.start_time ? new Date(process.start_time).toLocaleString() : 'N/A'}</div>
                                  {process.end_time && <div>Ended: {new Date(process.end_time).toLocaleString()}</div>}
                                  <div>Duration: {calculateDuration({ startTime: process.start_time, endTime: process.end_time, status: process.status })}</div>
                                </>
                              }
                            />
                            <Chip 
                              label={process.status} 
                              color={
                                process.status === 'completed' ? 'success' :
                                process.status === 'failed' || process.status === 'error' ? 'error' :
                                process.status === 'running' ? 'primary' :
                                process.status === 'starting' ? 'info' :
                                'default'
                              }
                              size="small"
                              sx={{ ml: 2 }}
                            />
                          </ListItem>
                        ))}
                      </List>
                    )}
                  </CardContent>
                </Card>
              </Grid>

              {/* Real-time Log Viewer */}
              <Grid item xs={12}>
                <RealTimeLogViewer processes={activeProcesses} />
              </Grid>
            </Grid>
          )}

{currentTab === 0 && (
            <ProcessHistory />
          )}
        </Box>
      </Container>
    </ThemeProvider>
  );
};

export default App;
