"""
Yahoo Finance Utilities Module

This module provides utilities for interacting with Yahoo Finance API
with proper caching, session management, and rate limiting.
It also includes an in-memory cache for ticker data to minimize API calls.
"""

import time
import random
import logging
import os
from functools import wraps
from pathlib import Path
from typing import Dict, List, Optional, Union, Tuple

import pandas as pd
import yfinance as yf
from yfinance.exceptions import YFRateLimitError
import requests

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('yahoo_utils')

# Global session object
_SESSION = None

# Global in-memory cache for ticker data
# Structure: {(ticker, start_date, end_date, interval): DataFrame}
_TICKER_CACHE: Dict[Tuple[str, str, str, str], pd.DataFrame] = {}

# Constants
MAX_RETRIES = 5
BASE_DELAY = 2  # Base delay in seconds
MAX_DELAY = 120  # Maximum delay in seconds
JITTER = 0.5  # Random jitter factor

def get_session():
    """Get or create a shared session for Yahoo Finance API calls."""
    global _SESSION
    if _SESSION is None:
        _SESSION = requests.Session()
        # Set a custom user agent to be more respectful
        _SESSION.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        # Configure cache if available
        try:
            import requests_cache
            cache_dir = Path.home() / '.cache' / 'yfinance'
            os.makedirs(cache_dir, exist_ok=True)
            requests_cache.install_cache(
                str(cache_dir / 'yfinance_cache'),
                backend='sqlite',
                expire_after=3600  # Cache for 1 hour
            )
            logger.info(f"Requests cache configured at {cache_dir}")
        except ImportError:
            logger.warning("requests_cache not installed. Consider installing for better performance.")

        # Configure rate limiter if available
        try:
            from requests_ratelimiter import LimiterAdapter
            from requests.adapters import HTTPAdapter

            # Configure rate limiter - 2 requests per second max
            rate_limiter = LimiterAdapter(
                per_second=2,
                burst=3,
                adapter=HTTPAdapter(max_retries=3)
            )
            _SESSION.mount('https://', rate_limiter)
            logger.info("Rate limiter configured")
        except ImportError:
            logger.warning("requests_ratelimiter not installed. Consider installing for better rate limiting.")

    return _SESSION

def with_retry(func):
    """Decorator to add retry logic with exponential backoff."""
    @wraps(func)
    def wrapper(*args, **kwargs):
        retries = 0
        while retries < MAX_RETRIES:
            try:
                return func(*args, **kwargs)
            except YFRateLimitError as e:
                retries += 1
                if retries >= MAX_RETRIES:
                    logger.error(f"Max retries ({MAX_RETRIES}) exceeded: {e}")
                    raise

                # Calculate delay with exponential backoff and jitter
                delay = min(BASE_DELAY * (2 ** (retries - 1)), MAX_DELAY)
                jitter_amount = random.uniform(-JITTER, JITTER)
                delay = delay * (1 + jitter_amount)

                logger.warning(f"Rate limit hit. Retrying in {delay:.2f} seconds... (Attempt {retries}/{MAX_RETRIES})")
                time.sleep(delay)
            except Exception as e:
                logger.error(f"Error in {func.__name__}: {e}")
                raise
    return wrapper

@with_retry
def download_ticker_data(ticker, start=None, end=None, period="max", interval="1d", use_cache=True, **kwargs):
    """
    Download data for a single ticker with retry logic and in-memory caching.

    Args:
        ticker (str): Ticker symbol
        start (str, optional): Start date
        end (str, optional): End date
        period (str, optional): Period (default: "max")
        interval (str, optional): Interval (default: "1d")
        use_cache (bool, optional): Whether to use the in-memory cache (default: True)
        **kwargs: Additional arguments to pass to yf.download

    Returns:
        pd.DataFrame: DataFrame with ticker data
    """
    # Check in-memory cache first if enabled
    if use_cache:
        cached_data = get_cached_data(ticker, start, end, interval)
        if cached_data is not None:
            return cached_data

    session = kwargs.pop('session', get_session())
    logger.info(f"Downloading data for {ticker} from {start} to {end}")

    data = yf.download(
        ticker,
        start=start,
        end=end,
        period=period,
        interval=interval,
        session=session,
        **kwargs
    )

    # Store in cache if enabled
    if use_cache and not data.empty:
        cache_data(ticker, data, start, end, interval)

    return data

@with_retry
def download_multiple_tickers(tickers, start=None, end=None, period="max", interval="1d", use_cache=False, **kwargs):
    """
    Download data for multiple tickers in a single batch with retry logic.

    Note: This function doesn't use the in-memory cache by default because it's more efficient
    to use batch_download_and_cache for that purpose. Set use_cache=True to enable caching.

    Args:
        tickers (list): List of ticker symbols
        start (str, optional): Start date
        end (str, optional): End date
        period (str, optional): Period (default: "max")
        interval (str, optional): Interval (default: "1d")
        use_cache (bool, optional): Whether to use and update the in-memory cache (default: False)
        **kwargs: Additional arguments to pass to yf.download

    Returns:
        pd.DataFrame: DataFrame with data for all tickers
    """
    session = kwargs.pop('session', get_session())
    logger.info(f"Batch downloading data for {len(tickers)} tickers from {start} to {end}")

    data = yf.download(
        tickers,
        start=start,
        end=end,
        period=period,
        interval=interval,
        group_by='ticker',
        session=session,
        **kwargs
    )

    # Store in cache if enabled
    if use_cache and not data.empty and len(tickers) > 1:
        for ticker in tickers:
            if ticker in data:
                ticker_data = data[ticker].copy()
                cache_data(ticker, ticker_data, start, end, interval)
    elif use_cache and not data.empty and len(tickers) == 1:
        cache_data(tickers[0], data, start, end, interval)

    return data

@with_retry
def get_ticker_object(ticker_symbol):
    """
    Get a Ticker object with the shared session.

    Args:
        ticker_symbol (str): Ticker symbol

    Returns:
        yf.Ticker: Ticker object
    """
    session = get_session()
    return yf.Ticker(ticker_symbol, session=session)

def configure_cache(cache_dir=None):
    """
    Configure the yfinance cache.

    Args:
        cache_dir (str, optional): Cache directory. If None, uses default.
    """
    if cache_dir is None:
        cache_dir = str(Path.home() / '.cache' / 'yfinance')

    os.makedirs(cache_dir, exist_ok=True)
    yf.set_tz_cache_location(cache_dir)
    logger.info(f"YFinance cache configured at {cache_dir}")

def get_cached_data(ticker: str, start=None, end=None, interval="1d") -> Optional[pd.DataFrame]:
    """
    Get data from the in-memory cache if available.

    Args:
        ticker (str): Ticker symbol
        start (str, optional): Start date
        end (str, optional): End date
        interval (str, optional): Interval (default: "1d")

    Returns:
        Optional[pd.DataFrame]: DataFrame with ticker data if in cache, None otherwise
    """
    cache_key = (ticker, str(start), str(end), interval)
    if cache_key in _TICKER_CACHE:
        logger.info(f"Cache hit for {ticker} from {start} to {end}")
        return _TICKER_CACHE[cache_key].copy()
    return None

def cache_data(ticker: str, data: pd.DataFrame, start=None, end=None, interval="1d") -> None:
    """
    Store data in the in-memory cache.

    Args:
        ticker (str): Ticker symbol
        data (pd.DataFrame): DataFrame with ticker data
        start (str, optional): Start date
        end (str, optional): End date
        interval (str, optional): Interval (default: "1d")
    """
    cache_key = (ticker, str(start), str(end), interval)
    _TICKER_CACHE[cache_key] = data.copy()
    logger.info(f"Cached data for {ticker} from {start} to {end}")

def clear_cache() -> None:
    """Clear the in-memory cache."""
    global _TICKER_CACHE
    _TICKER_CACHE = {}
    logger.info("In-memory cache cleared")

def batch_download_and_cache(tickers: List[str], start=None, end=None, interval="1d", **kwargs) -> Dict[str, pd.DataFrame]:
    """
    Download data for multiple tickers in a single batch and cache the results.

    Args:
        tickers (list): List of ticker symbols
        start (str, optional): Start date
        end (str, optional): End date
        interval (str, optional): Interval (default: "1d")
        **kwargs: Additional arguments to pass to yf.download

    Returns:
        Dict[str, pd.DataFrame]: Dictionary mapping ticker symbols to DataFrames
    """
    # Check which tickers are already in cache
    to_download = []
    result = {}

    for ticker in tickers:
        cached_data = get_cached_data(ticker, start, end, interval)
        if cached_data is not None:
            result[ticker] = cached_data
        else:
            to_download.append(ticker)

    if not to_download:
        logger.info("All tickers found in cache, no download needed")
        return result

    # Download missing tickers
    logger.info(f"Downloading data for {len(to_download)} tickers: {to_download}")

    try:
        batch_data = download_multiple_tickers(to_download, start=start, end=end, interval=interval, **kwargs)

        # Process and cache each ticker's data
        for ticker in to_download:
            if len(to_download) > 1:
                # Multi-ticker result has hierarchical columns
                if ticker in batch_data:
                    ticker_data = batch_data[ticker].copy()
                    cache_data(ticker, ticker_data, start, end, interval)
                    result[ticker] = ticker_data
                else:
                    logger.warning(f"No data returned for {ticker}")
            else:
                # Single ticker result has flat columns
                ticker_data = batch_data.copy()
                cache_data(ticker, ticker_data, start, end, interval)
                result[ticker] = ticker_data

    except Exception as e:
        logger.error(f"Error in batch download: {e}")
        # Try individual downloads as fallback
        for ticker in to_download:
            try:
                ticker_data = download_ticker_data(ticker, start=start, end=end, interval=interval, **kwargs)
                cache_data(ticker, ticker_data, start, end, interval)
                result[ticker] = ticker_data
            except Exception as e:
                logger.error(f"Error downloading {ticker}: {e}")

    return result

# Initialize the module
configure_cache()
