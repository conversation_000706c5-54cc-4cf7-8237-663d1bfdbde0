#!/usr/bin/env python3
"""
Quick Backend Test - Test backend functionality without starting full server
"""

import sys
import os
from pathlib import Path

# Add the backend app to Python path
backend_path = Path(__file__).parent / "dashboard" / "backend"
sys.path.insert(0, str(backend_path))

def test_backend_components():
    """Test backend components can be imported and initialized"""
    print("🔧 Backend Component Test")
    print("=" * 40)
    
    results = []
    
    # Test 1: Main app import
    print("\n1. Testing FastAPI app import...")
    try:
        from app.main import app
        print("   ✅ FastAPI app imported successfully")
        print(f"   ✅ App type: {type(app)}")
        results.append(True)
    except Exception as e:
        print(f"   ❌ Failed to import app: {e}")
        results.append(False)
    
    # Test 2: Orchestrator import
    print("\n2. Testing orchestrator import...")
    try:
        from app.services.simple_orchestrator import orchestrator
        print("   ✅ Orchestrator imported successfully")
        print(f"   ✅ Orchestrator type: {type(orchestrator)}")
        results.append(True)
    except Exception as e:
        print(f"   ❌ Failed to import orchestrator: {e}")
        results.append(False)
    
    # Test 3: Process types
    print("\n3. Testing process configuration...")
    try:
        from app.services.simple_orchestrator import orchestrator
        process_types = orchestrator.get_process_types()
        print(f"   ✅ Found {len(process_types)} process types:")
        for name, config in process_types.items():
            print(f"      - {name}: {config['description']}")
        results.append(True)
    except Exception as e:
        print(f"   ❌ Failed to get process types: {e}")
        results.append(False)
    
    # Test 4: API routes
    print("\n4. Testing API routes...")
    try:
        from app.api.routes.processes import router
        print(f"   ✅ Process router imported: {router}")
        results.append(True)
    except Exception as e:
        print(f"   ❌ Failed to import routes: {e}")
        results.append(False)
    
    # Test 5: WebSocket manager
    print("\n5. Testing WebSocket manager...")
    try:
        from app.ws_manager.manager import WebSocketManager
        manager = WebSocketManager()
        print(f"   ✅ WebSocket manager created: {type(manager)}")
        results.append(True)
    except Exception as e:
        print(f"   ❌ Failed to create WebSocket manager: {e}")
        results.append(False)
    
    return results

def test_script_execution():
    """Test that core scripts can be imported"""
    print("\n🐍 Script Import Test")
    print("=" * 40)
    
    results = []
    scripts_dir = Path("o:/Github/MaxPain/MaxPain2024")
    
    # Test core scripts
    scripts = [
        "UpdateIndexOptionPostgres.py",
        "UpdateStockOptionReportPostgres.py",
        "copyViewMultiDB.py"
    ]
    
    for script in scripts:
        print(f"\nTesting {script}...")
        script_path = scripts_dir / script
          if script_path.exists():
            try:
                # Try to parse the script (basic syntax check) with UTF-8 encoding
                with open(script_path, 'r', encoding='utf-8') as f:
                    compile(f.read(), script_path, 'exec')
                print(f"   ✅ {script} syntax is valid")
                results.append(True)
            except UnicodeDecodeError:
                try:
                    # Fallback to cp950 encoding
                    with open(script_path, 'r', encoding='cp950') as f:
                        compile(f.read(), script_path, 'exec')
                    print(f"   ✅ {script} syntax is valid (cp950 encoding)")
                    results.append(True)
                except Exception as e:
                    print(f"   ❌ {script} encoding issue: {e}")
                    results.append(False)
            except SyntaxError as e:
                print(f"   ❌ {script} has syntax error: {e}")
                results.append(False)
            except Exception as e:
                print(f"   ⚠️ {script} check failed: {e}")
                results.append(False)
        else:
            print(f"   ❌ {script} not found")
            results.append(False)
    
    return results

def main():
    """Main test function"""
    print("🚀 HKEX Dashboard Quick Backend Test")
    print("=" * 60)
    
    # Run backend component tests
    backend_results = test_backend_components()
    
    # Run script tests
    script_results = test_script_execution()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    backend_passed = sum(backend_results)
    backend_total = len(backend_results)
    
    script_passed = sum(script_results)
    script_total = len(script_results)
    
    total_passed = backend_passed + script_passed
    total_tests = backend_total + script_total
    
    print(f"Backend Tests: {backend_passed}/{backend_total}")
    print(f"Script Tests: {script_passed}/{script_total}")
    print(f"Overall: {total_passed}/{total_tests}")
    
    if total_passed == total_tests:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Backend components are working properly")
        print("✅ Core scripts are syntactically valid")
        print("\nReady for full system testing!")
        return True
    else:
        print("\n⚠️ Some tests failed")
        print("Review the issues above before proceeding")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
