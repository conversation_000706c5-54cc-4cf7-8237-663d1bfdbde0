#!/usr/bin/env python3
"""
Test imports and basic functionality
"""

import os
import sys

# Test 1: Basic imports
print("=== IMPORT TEST ===")
try:
    import asyncio
    if os.name == 'nt':
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    print("✓ asyncio with Windows policy")
except Exception as e:
    print(f"✗ asyncio error: {e}")

# Test 2: Backend imports
print("\n=== BACKEND IMPORT TEST ===")
try:
    sys.path.insert(0, r"O:\Github\MaxPain\MaxPain2024\dashboard\backend\app")
    from app.services.simple_orchestrator import SimpleOrchestrator
    print("✓ SimpleOrchestrator imported")
    
    orchestrator = SimpleOrchestrator()
    print("✓ Orchestrator instantiated")
    
    processes = orchestrator.list_active_processes()
    print(f"✓ Active processes: {len(processes)}")
    
    configs = list(orchestrator.process_configs.keys())
    print(f"✓ Available process types: {configs}")
    
except Exception as e:
    print(f"✗ Backend import error: {e}")
    import traceback
    traceback.print_exc()

# Test 3: Wrapper script exists
print("\n=== WRAPPER SCRIPT TEST ===")
wrapper_path = r"O:\Github\MaxPain\MaxPain2024\UpdateIndexOptionPostgres_wrapper.py"
if os.path.exists(wrapper_path):
    print(f"✓ Wrapper script exists: {wrapper_path}")
else:
    print(f"✗ Wrapper script missing: {wrapper_path}")

# Test 4: Original script exists
original_path = r"O:\Github\MaxPain\MaxPain2024\UpdateIndexOptionPostgres.py"
if os.path.exists(original_path):
    print(f"✓ Original script exists: {original_path}")
else:
    print(f"✗ Original script missing: {original_path}")

print("\n=== TEST COMPLETE ===")
