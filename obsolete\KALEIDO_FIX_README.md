# Kaleido Image Export Fix for SROC_FS.py

## Problem
The original `SROC_FS.py` script was getting stuck at line 245 when trying to save Plotly charts as PNG images using kaleido:
```python
fig.write_image( f'{d1_path}/{d1}_{ticker}.png')
```

## Solution
Implemented a robust image saving function with multiple fallback methods to ensure charts are always saved, even if kaleido fails.

## Changes Made

### 1. Added Robust Image Saving Function
- **Location**: Lines 20-118 in `SROC_FS.py`
- **Function**: `save_plotly_figure(fig, file_path_without_ext, ticker)`
- **Features**:
  - Multiple export methods with automatic fallback
  - 30-second timeout to prevent hanging
  - Cross-platform compatibility (Windows/Linux/macOS)
  - Detailed logging of success/failure

### 2. Export Methods (in order of preference)
1. **PNG with kaleido** (preferred - static image)
2. **PNG with explicit kaleido engine** (alternative kaleido approach)
3. **HTML with CDN plotly.js** (interactive, smaller file size)
4. **Self-contained HTML** (interactive, larger file size, works offline)

### 3. Replaced Original Code
**Before:**
```python
fig.write_image( f'{d1_path}/{d1}_{ticker}.png')   
print( f'{d1_path}/{d1}_{ticker}.png')
```

**After:**
```python
# Save figure using robust method with fallbacks
try:
    saved_path = save_plotly_figure(fig, f'{d1_path}/{d1}_{ticker}', ticker)
    print(f"Chart saved successfully: {saved_path}")
except Exception as e:
    print(f"Failed to save chart for {ticker}: {e}")
    # Continue with next ticker even if chart save fails
```

## Testing

### Run the Test Script
```bash
python scripts/test_kaleido.py
```

This will:
- Test kaleido installation
- Create a sample chart
- Attempt PNG export
- Provide troubleshooting guidance if issues occur

### Expected Output
**If kaleido works:**
```
✓ Plotly imported successfully
✓ Kaleido imported successfully (version: 0.2.1)
✓ Test figure created successfully
✓ Image exported successfully: test_output/kaleido_test_20241220_143022.png (45231 bytes)
🎉 All tests passed! Kaleido is working correctly.
```

**If kaleido fails:**
```
✗ Failed to export image: TimeoutError: Image generation timed out after 30 seconds
❌ Kaleido tests failed.
[Troubleshooting guide displayed]
```

## Troubleshooting Kaleido Issues

### Quick Fixes
```bash
# Reinstall kaleido
pip uninstall kaleido
pip install kaleido

# Force reinstall
pip install --force-reinstall kaleido

# Use conda (if applicable)
conda install -c conda-forge python-kaleido
```

### Common Issues
1. **Windows Defender blocking kaleido executable**
   - Add Python/kaleido to antivirus exceptions
   
2. **Permission issues**
   - Run as administrator (Windows)
   - Check write permissions to output directory
   
3. **Missing system dependencies**
   - Windows: Visual C++ Redistributable
   - Linux: Additional system libraries
   - macOS: Xcode command line tools

### Alternative Solutions
If kaleido continues to fail, the script will automatically fall back to HTML export:
- **Interactive charts** that can be opened in any web browser
- **Smaller file sizes** when using CDN version
- **Always works** regardless of kaleido issues

## Benefits of the New Approach

1. **Reliability**: Script never gets stuck - always produces output
2. **Flexibility**: Multiple export formats available
3. **Debugging**: Clear error messages and logging
4. **Compatibility**: Works across different operating systems
5. **Graceful degradation**: Falls back to HTML if PNG fails

## File Outputs

### If PNG export works:
- `{d1_path}/{d1}_{ticker}.png` - Static PNG image (1920x1080)

### If PNG fails, HTML fallback:
- `{d1_path}/{d1}_{ticker}.html` - Interactive HTML chart

Both formats contain the same chart data and visualizations.

## Running the Fixed Script

The script now runs more reliably:
```bash
python scripts/SROC_FS.py
```

You'll see output like:
```
Attempting to save 0001.HK chart using PNG with kaleido...
✓ Successfully saved: /path/to/output/241220_0001.HK.png
Chart saved successfully: /path/to/output/241220_0001.HK.png
```

Or if kaleido fails:
```
Attempting to save 0001.HK chart using PNG with kaleido...
✗ Failed to save with PNG with kaleido: TimeoutError: Image generation timed out after 30 seconds
Attempting to save 0001.HK chart using HTML with CDN plotly.js...
✓ Successfully saved: /path/to/output/241220_0001.HK.html
Chart saved successfully: /path/to/output/241220_0001.HK.html
```
