"""
Test the updated hkex_parser with proper error handling
"""
import datetime as dt
from hkex_parser import parse_stock_option_report_file

def test_parse_with_nonexistent_file():
    """Test parsing with the problematic file that was mentioned"""
    
    # Test the original problematic file path
    file_path = r"C:\output\MaxPain\hkex\htio250718.htm"
    trade_date = dt.datetime(2025, 7, 18)
    
    print(f"Testing parse_stock_option_report_file with: {file_path}")
    print("=" * 60)
    
    try:
        parsed_data, summary_counts = parse_stock_option_report_file(
            file_path, 
            None,  # symb_list = None (all symbols)
            trade_date
        )
        
        print(f"✅ Parse completed successfully")
        print(f"Parsed data records: {len(parsed_data)}")
        print(f"Summary counts: {len(summary_counts)}")
        
        if len(parsed_data) == 0:
            print("⚠️ No data extracted - this is expected if file doesn't exist or contains errors")
        else:
            print("✅ Data was successfully extracted")
            
    except Exception as e:
        print(f"❌ Parse failed with exception: {e}")
        print(f"Exception type: {type(e).__name__}")

if __name__ == "__main__":
    test_parse_with_nonexistent_file()
