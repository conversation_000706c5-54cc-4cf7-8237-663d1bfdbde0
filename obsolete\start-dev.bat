@echo off
REM HKEX Dashboard Development Startup Script for Windows

echo Starting HKEX Dashboard in Development Mode...

REM Check if .env file exists
if not exist .env (
    echo Creating .env file from .env.example...
    copy .env.example .env
    echo Please update .env file with your configuration before running again.
    pause
    exit /b 1
)

REM Create logs directory
if not exist logs mkdir logs

REM Start development environment
echo Starting development containers...
docker-compose -f docker-compose.dev.yml up --build

echo Development environment started!
echo Frontend: http://localhost:3000
echo Backend API: http://localhost:8000
echo API Docs: http://localhost:8000/docs
pause
