#%%
"""
HKEX Data Fetcher Module

This module handles ONLY the fetching and saving of HKEX reports:
- HTTP session management with robust retry logic
- HKEX report downloading (daily, weekly, HTI, historical)
- File saving to hkex folder
- Connection testing utilities

The fetcher knows nothing about report content or parsing.
It only downloads HTML files and saves them locally.

Debug Mode:
Set HKEX_DEBUG=true in .env file to enable connection testing and debugging.
"""

import requests as rq
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import time
import os
import random
import pandas as pd
import datetime as dt
from dotenv import load_dotenv

# Selenium imports are handled within the selenium_http_get function
# to keep them localized and avoid ImportErrors if Selenium is not used.
# from selenium import webdriver
# from selenium.webdriver.chrome.service import Service as ChromeService
# from webdriver_manager.chrome import ChromeDriverManager
# from selenium.common.exceptions import TimeoutException, WebDriverException

# Load environment variables
load_dotenv()

# Firecrawl fallback import (optional)
try:
    from firecrawl_fetcher import fetch_with_firecrawl
    FIRECRAWL_AVAILABLE = True
    print("🔥 Firecrawl fallback available")
except ImportError:
    FIRECRAWL_AVAILABLE = False
    print("⚠️  Firecrawl fallback not available (install with: pip install firecrawl-py)")

# Check debug mode
def is_debug_mode():
    """Check if debug mode is enabled via environment variable"""
    debug_env = os.getenv('HKEX_DEBUG', 'false').lower()
    return debug_env in ('true', '1', 'yes', 'on')


# Global session instance
_http_session = None


def _is_error_page_content(content):
    """
    Check if the downloaded content is an error page instead of actual HKEX report
    
    Args:
        content: bytes content from HTTP response
        
    Returns:
        bool: True if content appears to be an error page
    """
    try:
        # Try to decode as text
        text_content = content.decode('utf-8', errors='ignore').lower()
        
        # Check for common error indicators
        error_indicators = [
            "this site can't be reached",
            "err_http2_protocol_error", 
            "temporarily down",
            "moved permanently",
            "404 not found",
            "403 forbidden",
            "500 internal server error",
            "502 bad gateway",
            "503 service unavailable",
            "504 gateway timeout",
            "connection timed out",
            "dns probe finished",
            "no internet connection",
            "webpage not available",
            "unable to connect"
        ]
        
        # Check if any error indicators are present
        for indicator in error_indicators:
            if indicator in text_content:
                print(f"❌ Error page indicator found: {indicator}")
                return True
                
        # Check if it looks like a Chrome/browser error page
        if all(phrase in text_content for phrase in ["<title>", "chrome", "error"]):
            print("❌ Detected Chrome error page")
            return True
            
        # Check if content is too small to be a real report (reports are usually large)
        if len(content) < 1000:
            print("❌ Content too small to be a real report")
            return True
            
        # Check for HKEX report indicators (if present, it's likely a real report)
        hkex_indicators = [
            "trading day of the exchange",
            "hong kong exchanges",
            "derivatives market",
            "option",
            "call",
            "put"
        ]
        
        has_hkex_content = any(indicator in text_content for indicator in hkex_indicators)
        if has_hkex_content:
            print("✅ HKEX content indicators found - appears to be real report")
            return False
            
        # If no HKEX indicators and content is just generic HTML, probably an error
        if "<html" in text_content and not has_hkex_content:
            print("❌ Generic HTML without HKEX content - likely error page")
            return True
            
        return False
        
    except Exception as e:
        print(f"❌ Error checking content: {e}")
        return True  # Assume error if we can't check


def create_robust_session():
    """Create a requests session with retry logic and proper timeouts"""
    session = rq.Session()

    # Define retry strategy
    try:
        # Try newer urllib3 parameter name first
        retry_strategy = Retry(
            total=3,  # Total number of retries
            status_forcelist=[429, 500, 502, 503, 504],  # HTTP status codes to retry on
            allowed_methods=["HEAD", "GET", "OPTIONS"],  # HTTP methods to retry
            backoff_factor=1  # Backoff factor for retries (1, 2, 4 seconds)
        )
    except TypeError:
        # Fall back to older urllib3 parameter name
        retry_strategy = Retry(
            total=3,  # Total number of retries
            status_forcelist=[429, 500, 502, 503, 504],  # HTTP status codes to retry on
            method_whitelist=["HEAD", "GET", "OPTIONS"],  # HTTP methods to retry (older urllib3)
            backoff_factor=1  # Backoff factor for retries (1, 2, 4 seconds)
        )

    # Mount adapter with retry strategy
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)

    # Set default headers to mimic a real browser more closely
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'en-US,en;q=0.9,zh-HK;q=0.8,zh;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Cache-Control': 'max-age=0',
        'DNT': '1',
    })

    return session


def get_http_session():
    """Get or create the global HTTP session"""
    global _http_session
    if _http_session is None:
        _http_session = create_robust_session()
    return _http_session


def safe_http_get(url, timeout=30, max_retries=3, delay_between_retries=2):
    """
    Safely make HTTP GET request with timeout and retry logic

    Args:
        url: URL to fetch
        timeout: Request timeout in seconds
        max_retries: Maximum number of retries
        delay_between_retries: Delay between retries in seconds

    Returns:
        requests.Response object or None if failed
    """
    session = get_http_session()

    # Add a small random delay to avoid looking like a bot
    initial_delay = random.uniform(0.5, 2.0)
    time.sleep(initial_delay)

    for attempt in range(max_retries + 1):
        try:
            print(f"Attempting to fetch: {url} (attempt {attempt + 1}/{max_retries + 1})")

            # Add referer header for HKEX requests to look more legitimate
            headers = {}
            if 'hkex.com.hk' in url:
                headers['Referer'] = 'https://www.hkex.com.hk/eng/stat/dmstat/dmstat.htm'
                headers['Origin'] = 'https://www.hkex.com.hk'

            response = session.get(url, timeout=timeout, headers=headers)

            if response.status_code == 200:
                print(f"Successfully fetched: {url}")
                return response
            elif response.status_code == 403:
                print(f"Access forbidden (403) for: {url} - possible bot detection")
                if attempt < max_retries:
                    # Longer delay for 403 errors
                    longer_delay = delay_between_retries * (attempt + 2)
                    print(f"Waiting {longer_delay} seconds before retry...")
                    time.sleep(longer_delay)
                    continue
                return response
            elif response.status_code == 404:
                print(f"File not found (404) for: {url}")
                # Don't retry for 404 errors
                return response
            else:
                print(f"HTTP {response.status_code} for: {url}")

        except (rq.exceptions.Timeout, rq.exceptions.ConnectionError, rq.exceptions.RequestException) as e:
            print(f"Request failed (attempt {attempt + 1}): {str(e)}")
            if attempt < max_retries:
                # Add exponential backoff with jitter
                backoff_delay = delay_between_retries * (2 ** attempt) + random.uniform(0, 1)
                print(f"Retrying in {backoff_delay:.1f} seconds...")
                time.sleep(backoff_delay)
            else:
                print(f"Max retries exceeded for: {url}")
                return None
        except Exception as e:
            print(f"Unexpected error: {str(e)}")
            return None

    return None


def single_attempt_http_get(url, timeout=30):
    """
    True single attempt HTTP GET without any retries for fast fallback

    Args:
        url: URL to fetch
        timeout: Request timeout in seconds

    Returns:
        requests.Response object or None if failed
    """
    # Create a new session without retry strategy for true single attempt
    session = rq.Session()

    # Set basic headers without retry adapter
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9,zh-HK;q=0.8,zh;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'DNT': '1'
    })

    # Add a small random delay to avoid looking like a bot
    initial_delay = random.uniform(0.5, 1.5)
    time.sleep(initial_delay)

    try:
        print(f"Attempting single HTTP fetch: {url}")

        # Enhanced headers for HKEX requests
        headers = {}
        if 'hkex.com.hk' in url:
            headers.update({
                'Referer': 'https://www.hkex.com.hk/eng/stat/dmstat/dmstat.htm',
                'Origin': 'https://www.hkex.com.hk',
                'Host': 'www.hkex.com.hk',
                'Pragma': 'no-cache',
                'Cache-Control': 'no-cache'
            })

        # Use separate connect and read timeouts
        timeout_tuple = (10, timeout)  # (connect_timeout, read_timeout)

        response = session.get(url, timeout=timeout_tuple, headers=headers, stream=False)

        if response.status_code == 200:
            print(f"✅ Single HTTP attempt succeeded: {url}")
            return response
        else:
            print(f"❌ Single HTTP attempt failed with status {response.status_code}: {url}")
            return response

    except Exception as e:
        print(f"❌ Single HTTP attempt failed: {str(e)}")
        return None
    finally:
        # Close the session to free resources
        session.close()


# --- Selenium Fallback ---
# Mock Response class to mimic requests.Response
class MockResponse:
    def __init__(self, content, status_code, url, error=None):
        self.content = content
        self.status_code = status_code
        self.url = url
        self.text = content.decode('utf-8') if content else ""
        self.error = error # Store any exception that occurred

    def json(self):
        # Basic JSON decoding, assuming text is valid JSON
        import json
        return json.loads(self.text)

    def raise_for_status(self):
        if 400 <= self.status_code < 600:
            raise rq.exceptions.HTTPError(f"{self.status_code} Client/Server Error for url: {self.url}", response=self)


def selenium_http_get(url, timeout=30):
    """
    Fetch page content using Selenium WebDriver (Chrome).

    Args:
        url (str): The URL to fetch.
        timeout (int): Page load timeout in seconds.

    Returns:
        MockResponse: A mock response object similar to requests.Response,
                      or None if a critical WebDriver error occurs.
    """
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.service import Service as ChromeService
        from selenium.webdriver.chrome.options import Options as ChromeOptions
        from webdriver_manager.chrome import ChromeDriverManager
        from selenium.common.exceptions import TimeoutException, WebDriverException
    except ImportError as e:
        print(f"❌ Selenium import failed: {e}")
        print("Selenium dependencies not available. Returning error response.")
        return MockResponse(b"", 500, url, error=f"Selenium import error: {e}")

    print(f"Attempting Selenium fetch: {url}")

    chrome_options = ChromeOptions()
    chrome_options.add_argument("--headless")  # Run headless
    chrome_options.add_argument("--no-sandbox") # Bypass OS security model, REQUIRED for Docker/sandboxed envs
    chrome_options.add_argument("--disable-dev-shm-usage") # Overcome limited resource problems
    chrome_options.add_argument("--disable-gpu") # Disable GPU acceleration
    chrome_options.add_argument("--disable-extensions") # Disable extensions
    chrome_options.add_argument("--disable-plugins") # Disable plugins
    chrome_options.add_argument("--disable-images") # Disable images for faster loading
    chrome_options.add_argument("--disable-web-security") # Disable web security for testing
    chrome_options.add_argument("--allow-running-insecure-content") # Allow insecure content
    chrome_options.add_argument("--disable-features=VizDisplayCompositor") # Disable compositor
    chrome_options.add_argument("--window-size=1920,1080") # Set window size
    chrome_options.add_argument("user-agent=Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

    driver = None
    try:
        # Use webdriver-manager to automatically download and manage chromedriver
        # Set cache directory to avoid permission issues in Docker
        import os
        cache_dir = os.path.expanduser("~/.wdm")
        os.makedirs(cache_dir, exist_ok=True)

        service = ChromeService(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)

        driver.set_page_load_timeout(timeout)
        driver.get(url)

        # Wait a bit for any dynamic content to load (optional, adjust as needed)
        # time.sleep(random.uniform(1, 3)) # Consider if needed

        page_source = driver.page_source
        content = page_source.encode('utf-8')

        print(f"✅ Selenium fetch succeeded: {url}")
        return MockResponse(content, 200, url)

    except TimeoutException:
        print(f"❌ Selenium fetch timed out: {url}")
        return MockResponse(b"", 504, url, error="TimeoutException") # Gateway Timeout
    except WebDriverException as e:
        print(f"❌ Selenium WebDriverException: {str(e)}")
        # More specific error handling could be added here if needed
        if "net::ERR_NAME_NOT_RESOLVED" in str(e) or "net::ERR_CONNECTION_REFUSED" in str(e):
            return MockResponse(b"", 404, url, error=str(e)) # Treat as Not Found for network errors
        return MockResponse(b"", 500, url, error=str(e)) # Internal Server Error for other WebDriver issues
    except Exception as e:
        print(f"❌ Unexpected error in selenium_http_get: {str(e)}")
        return MockResponse(b"", 500, url, error=str(e)) # Internal Server Error
    finally:
        if driver:
            driver.quit()


def enhanced_http_get(url, timeout=30):
    """
    Enhanced HTTP GET with advanced anti-bot measures and multiple retry strategies

    This method uses sophisticated techniques to bypass anti-bot detection
    """
    import time
    import random

    # Try multiple strategies in sequence with enhanced anti-bot measures
    strategies = [
        _enhanced_http_strategy_stealth,   # NEW: Stealth mode with perfect headers
        _enhanced_http_strategy_4,         # Slow and steady approach
        _enhanced_http_strategy_2,         # Session with cookies
        _enhanced_http_strategy_3,         # Multiple user agents
        _enhanced_http_strategy_1,         # Basic enhanced headers
    ]

    for i, strategy in enumerate(strategies, 1):
        print(f"Attempting enhanced HTTP fetch (strategy {i}/{len(strategies)}): {url}")

        try:
            response = strategy(url, timeout)
            if response and response.status_code == 200:
                print(f"✅ Enhanced HTTP fetch succeeded with strategy {i}: {url}")
                return response
            elif response:
                print(f"⚠️ Enhanced HTTP strategy {i} failed with status {response.status_code}: {url}")
                # Continue to next strategy
            else:
                print(f"⚠️ Enhanced HTTP strategy {i} failed (no response): {url}")

        except Exception as e:
            print(f"⚠️ Enhanced HTTP strategy {i} failed: {str(e)}")

        # Add increasing delay between strategies for better bot avoidance
        if i < len(strategies):
            delay = random.uniform(3, 7) + (i * 2)  # Increasing delay
            print(f"Waiting {delay:.1f}s before trying next strategy...")
            time.sleep(delay)

    print(f"❌ All enhanced HTTP strategies failed for: {url}")
    return None


def _enhanced_http_strategy_stealth(url, timeout=30):
    """NEW Strategy: Ultra-stealth mode with perfect browser mimicking"""
    import requests as rq
    session = rq.Session()
    print("Strategy Stealth: Ultra-stealth mode with perfect browser mimicking")
    
    # Visit main page first to get cookies and establish session
    if 'hkex.com.hk' in url:
        try:
            print("Pre-establishing session with HKEX main page...")
            main_response = session.get('https://www.hkex.com.hk/', timeout=10)
            print(f"Main page visit: {main_response.status_code}")
            time.sleep(random.uniform(2, 4))  # Human-like delay
        except:
            pass  # Continue even if pre-visit fails

    # Ultra-realistic headers
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'en-US,en;q=0.9,zh-HK;q=0.8,zh-CN;q=0.7,zh-TW;q=0.6,zh;q=0.5',
        'Accept-Encoding': 'gzip, deflate',  # Remove br to force HTTP/1.1
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-User': '?1',
        'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'Cache-Control': 'max-age=0',
        'DNT': '1'
    }

    if 'hkex.com.hk' in url:
        headers.update({
            'Referer': 'https://www.hkex.com.hk/eng/stat/dmstat/dmstat.htm',
            'Origin': 'https://www.hkex.com.hk',
            'Host': 'www.hkex.com.hk'
        })

    import time, random
    # Longer human-like delay
    time.sleep(random.uniform(8, 15))

    try:
        # Use longer timeouts and more patience
        return session.get(
            url, 
            headers=headers, 
            timeout=(20, timeout + 10), 
            allow_redirects=True, 
            stream=False,
            verify=True
        )
    finally:
        session.close()


def _enhanced_http_strategy_1(url, timeout=30):
    """Strategy 1: Basic enhanced headers with session"""
    session = get_http_session()

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9,zh-HK;q=0.8,zh;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'DNT': '1'
    }

    if 'hkex.com.hk' in url:
        headers.update({
            'Referer': 'https://www.hkex.com.hk/eng/stat/dmstat/dmstat.htm',
            'Origin': 'https://www.hkex.com.hk',
            'Host': 'www.hkex.com.hk'
        })

    import time, random
    time.sleep(random.uniform(1, 3))

    return session.get(url, headers=headers, timeout=(15, timeout), allow_redirects=True)


def _enhanced_http_strategy_2(url, timeout=30):
    """Strategy 2: Fresh session with cookies and different headers"""
    import requests as rq
    session = rq.Session()

    # Set up session with cookies
    if 'hkex.com.hk' in url:
        # First visit the main page to get cookies
        try:
            main_response = session.get('https://www.hkex.com.hk/', timeout=10)
            print(f"Pre-visit to main page: {main_response.status_code}")
        except:
            pass  # Continue even if pre-visit fails

    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-GB,en;q=0.9,zh-HK;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-User': '?1'
    }

    if 'hkex.com.hk' in url:
        headers.update({
            'Referer': 'https://www.hkex.com.hk/eng/stat/dmstat/dmstat.htm',
            'Host': 'www.hkex.com.hk'
        })

    import time, random
    time.sleep(random.uniform(2, 4))

    try:
        return session.get(url, headers=headers, timeout=(20, timeout), allow_redirects=True)
    finally:
        session.close()


def _enhanced_http_strategy_3(url, timeout=30):
    """Strategy 3: Random user agent with mobile simulation"""
    import requests as rq
    session = rq.Session()

    user_agents = [
        'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPad; CPU OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/120.0'
    ]

    import random
    selected_ua = random.choice(user_agents)

    headers = {
        'User-Agent': selected_ua,
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
    }

    if 'hkex.com.hk' in url:
        headers['Referer'] = 'https://www.hkex.com.hk/'

    import time
    time.sleep(random.uniform(3, 6))

    try:
        return session.get(url, headers=headers, timeout=(25, timeout), allow_redirects=True)
    finally:
        session.close()


def _enhanced_http_strategy_4(url, timeout=30):
    """Strategy 4: Slow and steady with maximum human-like behavior"""
    import requests as rq
    session = rq.Session()
    print("Strategy 4: Slow and steady with maximum human-like behavior")
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Microsoft Edge";v="120"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"'
    }

    if 'hkex.com.hk' in url:
        headers.update({
            'Referer': 'https://www.hkex.com.hk/eng/stat/dmstat/dmstat.htm',
            'Host': 'www.hkex.com.hk',
            'Sec-Fetch-Site': 'same-origin'
        })

    import time, random
    time.sleep(random.uniform(5, 8))  # Longer delay

    try:
        return session.get(url, headers=headers, timeout=(30, timeout), allow_redirects=True, stream=False)
    finally:
        session.close()


def _http11_forced_get(url, timeout=30):
    """
    Force HTTP/1.1 connection to bypass HTTP/2 protocol errors
    
    Args:
        url: URL to fetch
        timeout: Request timeout in seconds
        
    Returns:
        requests.Response object or None if failed
    """
    import requests as rq
    from requests.adapters import HTTPAdapter
    from urllib3.util.retry import Retry
    import urllib3
    
    print("🔧 Forcing HTTP/1.1 connection to bypass HTTP/2 protocol errors...")
    
    # Create session with HTTP/1.1 forced
    session = rq.Session()
    
    # Force HTTP/1.1 by disabling HTTP/2
    class HTTP11Adapter(HTTPAdapter):
        def init_poolmanager(self, *args, **kwargs):
            # Force HTTP/1.1 by setting http_version
            kwargs['assert_hostname'] = False
            return super().init_poolmanager(*args, **kwargs)
    
    # Mount the adapter
    adapter = HTTP11Adapter()
    session.mount('http://', adapter)
    session.mount('https://', adapter)
    
    # Disable HTTP/2 at urllib3 level
    session.mount('https://', HTTPAdapter())
    
    # Headers optimized for HTTP/1.1
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9,zh-HK;q=0.8,zh;q=0.7',
        'Accept-Encoding': 'gzip, deflate',  # Remove 'br' (Brotli) which is HTTP/2 specific
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Cache-Control': 'max-age=0',
        'DNT': '1'
    }
    
    if 'hkex.com.hk' in url:
        headers.update({
            'Referer': 'https://www.hkex.com.hk/eng/stat/dmstat/dmstat.htm',
            'Origin': 'https://www.hkex.com.hk',
            'Host': 'www.hkex.com.hk'
        })
    
    import time, random
    time.sleep(random.uniform(2, 4))
    
    try:
        # Use HTTP/1.1 specific settings
        response = session.get(
            url, 
            headers=headers, 
            timeout=(15, timeout),
            allow_redirects=True,
            stream=False,
            verify=True
        )
        
        if response.status_code == 200:
            print(f"✅ HTTP/1.1 forced connection succeeded: {url}")
            return response
        else:
            print(f"⚠️ HTTP/1.1 failed with status {response.status_code}: {url}")
            return response
            
    except Exception as e:
        print(f"❌ HTTP/1.1 forced connection failed: {str(e)}")
        return None
    finally:
        session.close()


def selenium_stealth_get(url, timeout=30):
    """
    Enhanced Selenium fetch with stealth mode to avoid bot detection
    
    Args:
        url: URL to fetch
        timeout: Request timeout in seconds
        
    Returns:
        MockResponse object or None if failed
    """
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.service import Service as ChromeService
        from selenium.webdriver.chrome.options import Options as ChromeOptions
        from webdriver_manager.chrome import ChromeDriverManager
        from selenium.common.exceptions import TimeoutException, WebDriverException
    except ImportError as e:
        print(f"❌ Selenium import failed: {e}")
        return MockResponse(b"", 500, url, error=f"Selenium import error: {e}")

    print(f"🥷 Attempting Selenium stealth fetch: {url}")

    chrome_options = ChromeOptions()
    
    # Stealth mode options
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-plugins")
    chrome_options.add_argument("--disable-images")
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--allow-running-insecure-content")
    chrome_options.add_argument("--disable-features=VizDisplayCompositor")
    chrome_options.add_argument("--window-size=1920,1080")
    
    # Anti-bot detection measures
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument("--disable-dev-tools")
    chrome_options.add_argument("--no-first-run")
    chrome_options.add_argument("--disable-default-apps")
    chrome_options.add_argument("--disable-infobars")
    
    # Realistic user agent for Hong Kong region
    chrome_options.add_argument("user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
    
    # Force HTTP/1.1 to avoid HTTP/2 protocol errors
    chrome_options.add_argument("--disable-http2")
    chrome_options.add_argument("--disable-quic")

    driver = None
    try:
        service = ChromeService(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # Execute script to hide automation indicators
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        # Set realistic screen resolution and other properties
        driver.execute_script("""
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en', 'zh-HK', 'zh']
            });
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5]
            });
        """)

        driver.set_page_load_timeout(timeout)
        
        # Add random delay to mimic human behavior
        import time, random
        time.sleep(random.uniform(1, 3))
        
        driver.get(url)
        
        # Wait a bit more for dynamic content
        time.sleep(random.uniform(2, 4))

        page_source = driver.page_source
        content = page_source.encode('utf-8')

        print(f"✅ Selenium stealth fetch succeeded: {url}")
        return MockResponse(content, 200, url)

    except TimeoutException:
        print(f"❌ Selenium stealth fetch timed out: {url}")
        return MockResponse(b"", 504, url, error="TimeoutException")
    except WebDriverException as e:
        print(f"❌ Selenium stealth WebDriverException: {str(e)}")
        if "net::ERR_NAME_NOT_RESOLVED" in str(e) or "net::ERR_CONNECTION_REFUSED" in str(e):
            return MockResponse(b"", 404, url, error=str(e))
        return MockResponse(b"", 500, url, error=str(e))
    except Exception as e:
        print(f"❌ Unexpected error in selenium_stealth_get: {str(e)}")
        return MockResponse(b"", 500, url, error=str(e))
    finally:
        if driver:
            driver.quit()


def safe_http_get_with_firecrawl_fallback(url, timeout=30):
    """
    Enhanced HTTP GET with a sequence of fallbacks:
    1. Enhanced HTTP GET with anti-bot measures
    2. HTTP/1.1 forced connection (bypasses HTTP/2 issues)
    3. Selenium with stealth mode
    4. Firecrawl-based GET (if available)

    Args:
        url: URL to fetch
        timeout: Request timeout in seconds for all methods

    Returns:
        requests.Response object or MockResponse object, or None if all methods fail
    """
    print(f"🔄 Attempting fetch for: {url} with anti-bot measures...")

    # 1. Try Enhanced HTTP GET with anti-bot measures - FIRST PRIORITY
    print("1️⃣ Trying enhanced HTTP method with anti-bot measures...")
    enhanced_response = enhanced_http_get(url, timeout)
    if enhanced_response and enhanced_response.status_code == 200 and not _is_error_page_content(enhanced_response.content):
        print(f"✅ Enhanced HTTP method succeeded for: {url}")
        return enhanced_response
    elif enhanced_response:
        print(f"⚠️ Enhanced HTTP failed with status {enhanced_response.status_code} for: {url}")
    else:
        print(f"⚠️ Enhanced HTTP failed (no response) for: {url}")

    # 2. Try HTTP/1.1 forced connection (bypasses HTTP/2 protocol errors) - SECOND PRIORITY
    print("2️⃣ Trying HTTP/1.1 forced connection...")
    http11_response = _http11_forced_get(url, timeout)
    if http11_response and http11_response.status_code == 200 and not _is_error_page_content(http11_response.content):
        print(f"✅ HTTP/1.1 method succeeded for: {url}")
        return http11_response
    elif http11_response:
        print(f"⚠️ HTTP/1.1 failed with status {http11_response.status_code} for: {url}")
    else:
        print(f"⚠️ HTTP/1.1 failed (no response) for: {url}")

    # 3. Try Selenium with stealth mode - THIRD PRIORITY
    print(f"3️⃣ Switching to Selenium stealth method for: {url}...")
    selenium_response = selenium_stealth_get(url, timeout)
    if selenium_response and selenium_response.status_code == 200 and not _is_error_page_content(selenium_response.content):
        print(f"✅ Selenium stealth method succeeded for: {url}")
        return selenium_response
    elif selenium_response and selenium_response.status_code == 500 and hasattr(selenium_response, 'error') and 'import' in str(selenium_response.error).lower():
        print(f"⚠️ Selenium method unavailable due to import issues, trying Firecrawl...")
    elif selenium_response:
        print(f"⚠️ Selenium stealth method failed with status {selenium_response.status_code} for: {url}")
    else:
        print(f"⚠️ Selenium stealth method failed (no response object) for: {url}")

    # 4. Try Firecrawl fallback (if available and other methods failed) - LAST RESORT
    if FIRECRAWL_AVAILABLE:
        print(f"4️⃣ Switching to Firecrawl fallback for: {url}...")
        try:
            api_key = os.getenv('FIRECRAWL_API_KEY')
            if not api_key:
                print("⚠️ Firecrawl API key not found in environment variables (FIRECRAWL_API_KEY). Skipping Firecrawl.")
            else:
                firecrawl_response = fetch_with_firecrawl(url, api_key)
                if firecrawl_response and firecrawl_response.status_code == 200:
                    print(f"✅ Firecrawl fallback succeeded for: {url}")
                    return firecrawl_response
                elif firecrawl_response:
                    print(f"❌ Firecrawl fallback failed with status {firecrawl_response.status_code} for: {url}")
                else:
                    print(f"❌ Firecrawl fallback failed (no response) for: {url}")
        except Exception as e:
            print(f"❌ Firecrawl fallback error for {url}: {e}")
    else:
        print(f"ℹ️ Firecrawl fallback not available or skipped for: {url}")

    print(f"❌ All fetch methods failed for: {url}")
    # Return the last failed response if available for error context
    if enhanced_response: return enhanced_response
    if http11_response: return http11_response
    if selenium_response: return selenium_response
    return None


def test_hkex_connection():
    """Test connection to HKEX website"""
    test_url = "https://www.hkex.com.hk"
    print(f"Testing connection to HKEX website: {test_url}")

    response = safe_http_get(test_url, timeout=10, max_retries=2, delay_between_retries=1)
    if response and response.status_code == 200:
        print("✓ HKEX website is accessible")
        return True
    else:
        print("✗ HKEX website is not accessible")
        return False


def test_specific_report_url(url):
    """Test access to a specific HKEX report URL (respects debug mode)"""
    if not is_debug_mode():
        print(f"⏭️  Skipping specific URL test (debug mode disabled): {url}")
        return True

    print(f"🐛 DEBUG MODE: Testing specific report URL: {url}")

    response = safe_http_get(url, timeout=45, max_retries=2, delay_between_retries=3)
    if response:
        if response.status_code == 200:
            print(f"✓ Report accessible: {url}")
            print(f"Content length: {len(response.content)} bytes")
            return True
        elif response.status_code == 403:
            print(f"✗ Access forbidden (403): {url} - Bot detection likely")
            return False
        elif response.status_code == 404:
            print(f"✗ Report not found (404): {url}")
            return False
        else:
            print(f"✗ HTTP {response.status_code}: {url}")
            return False
    else:
        print(f"✗ No response received: {url}")
        return False


def fetch_daily_report(symb, trade_date, pathname):
    """
    Fetch daily option report from HKEX website and save to local file.

    Args:
        symb: Symbol (HSI, HHI, MHI, HTI)
        trade_date: Trading date
        pathname: Base path for saving files

    Returns:
        tuple: (file_path, success) where success is True/False
    """
    t = (symb + 'o' + trade_date.strftime("%y%m%d")).lower()
    url = 'https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/' + t + '.htm'

    # Create hkex directory if it doesn't exist
    os.makedirs(f'{pathname}hkex', exist_ok=True)

    # Use enhanced HTTP session with Firecrawl fallback
    response = safe_http_get_with_firecrawl_fallback(url, timeout=30)

    if response and response.status_code == 200:
        # Check if content is actually an error page
        if _is_error_page_content(response.content):
            print(f'❌ Error page detected (not real report): {url}')
            return None, False
            
        # Save to local file in hkex folder
        file_path = f'{pathname}hkex/{t}.htm'
        with open(file_path, 'wb') as out_f:
            out_f.write(response.content)
        print(f'Saved: {url} to {file_path}')
        return file_path, True
    elif response:
        print(f'Failed to fetch: {response.status_code}, {url}')
        if response.status_code == 403:
            print("Access forbidden - possible bot detection")
        elif response.status_code == 404:
            print("Report not found - may not be available yet")
        return None, False
    else:
        print(f'Failed to fetch (no response): {url}')
        return None, False


def fetch_weekly_report(symb, trade_date, pathname):
    """
    Fetch weekly option report from HKEX website and save to local file.

    Args:
        symb: Symbol (HSI, HHI, HTI)
        trade_date: Trading date
        pathname: Base path for saving files

    Returns:
        tuple: (file_path, success) where success is True/False
    """
    t = (symb + 'wo' + trade_date.strftime("%y%m%d")).lower()
    url = 'https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/' + t + '.htm'

    # Create hkex directory if it doesn't exist
    os.makedirs(f'{pathname}hkex', exist_ok=True)

    # Use enhanced HTTP session with Firecrawl fallback
    response = safe_http_get_with_firecrawl_fallback(url, timeout=30)

    if response and response.status_code == 200:
        # Check if content is actually an error page
        if _is_error_page_content(response.content):
            print(f'❌ Error page detected (not real report): {url}')
            return None, False
            
        # Save to local file in hkex folder
        file_path = f'{pathname}hkex/{t}.htm'
        with open(file_path, 'wb') as out_f:
            out_f.write(response.content)
        print(f'Saved: {url} to {file_path}')
        return file_path, True
    elif response:
        print(f'Failed to fetch: {response.status_code}, {url}')
        if response.status_code == 403:
            print("Access forbidden - possible bot detection")
        elif response.status_code == 404:
            print("Report not found - may not be available yet")
        return None, False
    else:
        print(f'Failed to fetch (no response): {url}')
        return None, False


def fetch_hti_report(symb, trade_date, pathname):
    """
    Fetch HTI option report from HKEX website and save to local file.

    Args:
        symb: Symbol (HTI)
        trade_date: Trading date
        pathname: Base path for saving files

    Returns:
        tuple: (file_path, success) where success is True/False
    """
    t = (symb + 'o' + trade_date.strftime("%y%m%d")).lower()
    url = 'https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/' + t + '.htm'

    # Create hkex directory if it doesn't exist
    os.makedirs(f'{pathname}hkex', exist_ok=True)

    # Use robust HTTP session instead of direct requests
    response = safe_http_get(url, timeout=30, max_retries=3, delay_between_retries=2)

    if response and response.status_code == 200:
        # Save to local file in hkex folder
        file_path = f'{pathname}hkex/{t}.htm'
        with open(file_path, 'wb') as out_f:
            out_f.write(response.content)
        print(f'Saved: {url} to {file_path}')
        return file_path, True
    elif response:
        print(f'Failed to fetch: {response.status_code}, {url}')
        return None, False
    else:
        print(f'Failed to fetch (no response): {url}')
        return None, False


def fetch_stock_option_report(symb_list, trade_date, pathname):
    """
    Fetch stock option report from HKEX website and save to local file.

    Stock option reports use the dqeYYMMDD.htm URL pattern and contain
    data for all symbols in a single file.

    Args:
        symb_list: List of symbols to process (or None for all symbols)
        trade_date: Trading date
        pathname: Base path for saving files

    Returns:
        tuple: (file_path, success) where success is True/False
    """
    # Stock option reports use dqe prefix + date format
    t = ('dqe' + trade_date.strftime("%y%m%d")).lower()
    url = 'https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/' + t + '.htm'

    # Create html directory if it doesn't exist (stock options use html folder)
    os.makedirs(f'{pathname}html', exist_ok=True)

    # Use enhanced HTTP session with Firecrawl fallback for stock options
    response = safe_http_get_with_firecrawl_fallback(url, timeout=10)

    if response and response.status_code == 200:
        # Check if content is actually an error page
        if _is_error_page_content(response.content):
            print(f'❌ Error page detected (not real report): {url}')
            return None, False
            
        # Save to local file in html folder (following stock options convention)
        file_path = f'{pathname}html/{t}.htm'
        with open(file_path, 'wb') as out_f:
            out_f.write(response.content)
        print(f'Saved: {url} to {file_path}')
        return file_path, True
    elif response:
        print(f'Failed to fetch: {response.status_code}, {url}')
        if response.status_code == 403:
            print("Access forbidden - possible bot detection")
        elif response.status_code == 404:
            print("Report not found - may not be available yet")
        return None, False
    else:
        print(f'Failed to fetch (no response): {url}')
        return None, False


def fetch_historical_reports(symb, start_date, end_date, pathname):
    """
    Fetch historical HKEX reports for bulk processing.

    Args:
        symb: Symbol to fetch (or None for all symbols)
        start_date: Start date for fetching
        end_date: End date for fetching
        pathname: Base path for saving files

    Returns:
        dict: Summary of fetched files by symbol and date
    """
    if start_date is None:
        start_date = dt.date(2023, 10, 1)
    if end_date is None:
        end_date = dt.date.today()

    bdaterange = pd.bdate_range(start_date, end_date)
    print(f"Fetching historical reports from {start_date} to {end_date}")
    print(f"Business dates: {len(bdaterange)} days")

    # Create base directory
    os.makedirs(f'{pathname}hkex', exist_ok=True)

    if symb is None:
        all_symb = ['HSI', 'HHI', 'MHI', 'HTI']
    else:
        if symb in ['HSI', 'HHI', 'MHI', 'HTI']:
            all_symb = [symb]
        else:
            print('Unknown Symbol')
            return {}

    print(f'Fetching reports for symbols: {all_symb}')

    fetch_summary = {}

    # Process dates in descending order (most recent first)
    for trade_date in sorted(bdaterange, reverse=True):
        for symbol in all_symb:
            if symbol not in fetch_summary:
                fetch_summary[symbol] = {'success': 0, 'failed': 0}

            file_path, success = fetch_daily_report(symbol, trade_date, pathname)

            if success:
                fetch_summary[symbol]['success'] += 1
            else:
                fetch_summary[symbol]['failed'] += 1

    # Print summary
    print("\nFetch Summary:")
    for symbol, stats in fetch_summary.items():
        total = stats['success'] + stats['failed']
        success_rate = (stats['success'] / total * 100) if total > 0 else 0
        print(f"{symbol}: {stats['success']}/{total} files ({success_rate:.1f}% success)")

    return fetch_summary


def check_environment():
    """Check if required environment variables are set"""
    required_vars = ['out_path', 'WILL9700_DB']
    missing_vars = []

    for var in required_vars:
        if os.getenv(var) is None:
            missing_vars.append(var)

    if missing_vars:
        print(f"✗ Missing environment variables: {missing_vars}")
        return False
    else:
        print("✓ All required environment variables are set")
        return True
#%%
# Test a known problematic URL (or a live one that might fail with direct requests)
# This will now try single_attempt, then selenium, then firecrawl.
if __name__ == "__main__":
    print("Running hkex_fetcher.py tests...")

    # Test connection (uses safe_http_get which has its own retries)
    # test_hkex_connection() # This uses the old safe_http_get, might need update or separate test for new fallback logic

    # Test the new fallback mechanism with a specific URL
    # Replace with a URL that is known to be problematic for simple GET requests
    # or a live URL from HKEX.
    test_url_fallback = "https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/hsio240719.htm" # Example, might be 404 if old
    # A more reliable test would be against the main page or a known current report page.
    # test_url_fallback = "https://www.hkex.com.hk/eng/marketstat/hksccm/cceqrpt_result.htm" # Example of a page that might require JS

    print(f"\nTesting safe_http_get_with_firecrawl_fallback for URL: {test_url_fallback}")
    response = safe_http_get_with_firecrawl_fallback(test_url_fallback, timeout=20)

    if response and response.status_code == 200:
        print(f"\n✅ Successfully fetched URL (using fallback if needed): {test_url_fallback}")
        print(f"Content length: {len(response.content)} bytes")
        # print(f"First 100 chars: {response.text[:100]}")
    elif response:
        print(f"\n❌ Failed to fetch URL with fallback. Status: {response.status_code}, Error: {getattr(response, 'error', 'N/A')}")
    else:
        print(f"\n❌ Failed to fetch URL with fallback. No response object returned.")

    # You can also test specific report fetching functions if needed, as they use safe_http_get_with_firecrawl_fallback
    # For example:
    # test_date = dt.date.today() - dt.timedelta(days=1) # yesterday, adjust if weekend/holiday
    # fetch_daily_report("HSI", test_date, "./output_test/") # Ensure ./output_test/ exists

    # Test the original specific report URL function if still relevant (uses safe_http_get)
    # print("\nTesting original test_specific_report_url (uses safe_http_get without new fallback):")
    # test_specific_report_url("https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/dqe250617.htm")


    # It's good practice to test the selenium_http_get directly as well
    print(f"\nTesting selenium_http_get directly for URL: {test_url_fallback}")
    sel_response = selenium_http_get(test_url_fallback, timeout=20)
    if sel_response and sel_response.status_code == 200:
        print(f"✅ Selenium direct fetch successful for {test_url_fallback}. Length: {len(sel_response.content)}")
    elif sel_response:
        print(f"❌ Selenium direct fetch failed. Status: {sel_response.status_code}, Error: {sel_response.error}")
    else:
        print(f"❌ Selenium direct fetch failed. No response.")

print("Completed hkex_fetcher.py tests.")
# %%
