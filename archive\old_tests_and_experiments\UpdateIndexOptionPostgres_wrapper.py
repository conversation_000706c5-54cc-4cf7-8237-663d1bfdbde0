#!/usr/bin/env python3
"""
Wrapper script for UpdateIndexOptionPostgres.py
This handles path setup and error catching
"""

import sys
import os
from pathlib import Path

def main():
    """Main wrapper function"""
    # Set up the script directory
    script_dir = Path(__file__).parent
    
    # Add script directory to Python path for imports
    if str(script_dir) not in sys.path:
        sys.path.insert(0, str(script_dir))
    
    # Change to script directory
    original_cwd = os.getcwd()
    os.chdir(script_dir)
    
    try:
        print(f"Wrapper: Working directory set to {script_dir}")
        print(f"Wrapper: Python path includes {script_dir}")
        print(f"Wrapper: Arguments: {sys.argv[1:]}")
        
        # Import and run the actual script
        print("Wrapper: Importing UpdateIndexOptionPostgres...")
        
        # Test Storacle import first
        try:
            import Storacle
            print("Wrapper: ✓ Storacle imported successfully")
        except Exception as e:
            print(f"Wrapper: ✗ Error importing Storacle: {e}")
            raise
        
        # Import the main script
        import UpdateIndexOptionPostgres
        
        print("Wrapper: ✓ UpdateIndexOptionPostgres imported successfully")
        print("Wrapper: Starting main execution...")
        
        # Run the main function
        UpdateIndexOptionPostgres.main()
        
        print("Wrapper: ✓ Execution completed successfully")
        
    except SystemExit as e:
        print(f"Wrapper: Script exited with code: {e.code}")
        sys.exit(e.code)
    except Exception as e:
        print(f"Wrapper: Error during execution: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    finally:
        # Restore original working directory
        os.chdir(original_cwd)

if __name__ == "__main__":
    main()
