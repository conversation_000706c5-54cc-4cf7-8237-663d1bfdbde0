import React, { useState } from 'react';
import {
  <PERSON>,
  Typography,
  Card,
  CardContent,
  CardHeader,
  Grid,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Alert,
  Snackbar
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { PlayArrow, Stop } from '@mui/icons-material';
import { useMutation, useQuery } from '@tanstack/react-query';
import { apiService } from '../services/api';
import ProcessStatusCard from '../components/ProcessStatusCard';

const ProcessControl: React.FC = () => {
  const [selectedProcess, setSelectedProcess] = useState('');
  const [startDate, setStartDate] = useState<Date | null>(new Date());
  const [endDate, setEndDate] = useState<Date | null>(new Date());
  const [symbols, setSymbols] = useState('HSI,HHI,HTI,MHI');
  const [notification, setNotification] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'info';
  }>({ open: false, message: '', severity: 'info' });

  const { data: processTypes } = useQuery({
    queryKey: ['processTypes'],
    queryFn: () => apiService.getProcessTypes(),
  });

  const { data: activeProcesses, refetch: refetchProcesses } = useQuery({
    queryKey: ['activeProcesses'],
    queryFn: () => apiService.getActiveProcesses(),
    refetchInterval: 5000,
  });

  const startProcessMutation = useMutation({
    mutationFn: (params: { process: string; parameters: any }) =>
      apiService.startProcess(params),
    onSuccess: (data) => {
      setNotification({
        open: true,
        message: `Process started successfully. Task ID: ${data.task_id}`,
        severity: 'success',
      });
      refetchProcesses();
    },
    onError: (error: any) => {
      setNotification({
        open: true,
        message: `Failed to start process: ${error.message}`,
        severity: 'error',
      });
    },
  });

  const handleStartProcess = () => {
    if (!selectedProcess) {
      setNotification({
        open: true,
        message: 'Please select a process type',
        severity: 'error',
      });
      return;
    }

    const parameters: any = {};
    
    if (startDate) {
      parameters.start_date = startDate.toISOString().split('T')[0];
    }
    
    if (endDate) {
      parameters.end_date = endDate.toISOString().split('T')[0];
    }
    
    if (symbols.trim()) {
      parameters.symbols = symbols.split(',').map(s => s.trim());
    }

    startProcessMutation.mutate({
      process: selectedProcess,
      parameters,
    });
  };

  const handleCloseNotification = () => {
    setNotification(prev => ({ ...prev, open: false }));
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box>
        <Typography variant="h4" gutterBottom>
          Process Control
        </Typography>

        <Grid container spacing={3}>
          {/* Process Configuration */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardHeader title="Start New Process" />
              <CardContent>
                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <FormControl fullWidth>
                      <InputLabel>Process Type</InputLabel>
                      <Select
                        value={selectedProcess}
                        onChange={(e) => setSelectedProcess(e.target.value)}
                        label="Process Type"
                      >
                        {processTypes?.process_types.map((type: any) => (
                          <MenuItem key={type.value} value={type.value}>
                            {type.label}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>                  <Grid item xs={6}>
                    <DatePicker
                      label="Start Date"
                      value={startDate}
                      onChange={(newValue) => setStartDate(newValue)}
                      slots={{
                        textField: TextField
                      }}
                      slotProps={{
                        textField: { fullWidth: true }
                      }}
                    />
                  </Grid>

                  <Grid item xs={6}>
                    <DatePicker
                      label="End Date"
                      value={endDate}
                      onChange={(newValue) => setEndDate(newValue)}
                      slots={{
                        textField: TextField
                      }}
                      slotProps={{
                        textField: { fullWidth: true }
                      }}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Symbols (comma-separated)"
                      value={symbols}
                      onChange={(e) => setSymbols(e.target.value)}
                      helperText="Leave empty to process all symbols"
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <Button
                      fullWidth
                      variant="contained"
                      size="large"
                      startIcon={<PlayArrow />}
                      onClick={handleStartProcess}
                      disabled={startProcessMutation.isPending}
                    >
                      {startProcessMutation.isPending ? 'Starting...' : 'Start Process'}
                    </Button>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Process Description */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardHeader title="Process Information" />
              <CardContent>
                {selectedProcess && processTypes ? (
                  <Box>
                    <Typography variant="h6" gutterBottom>
                      {processTypes.process_types.find((t: any) => t.value === selectedProcess)?.label}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {processTypes.process_types.find((t: any) => t.value === selectedProcess)?.description}
                    </Typography>
                  </Box>
                ) : (
                  <Typography color="text.secondary">
                    Select a process type to see details
                  </Typography>
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* Active Processes */}
          <Grid item xs={12}>
            <Card>
              <CardHeader title="Running Processes" />
              <CardContent>
                {activeProcesses && activeProcesses.length > 0 ? (
                  <Grid container spacing={2}>
                    {activeProcesses.map((process: any) => (
                      <Grid item xs={12} md={6} key={process.task_id}>
                        <ProcessStatusCard
                          process={{
                            task_id: process.task_id,
                            process: process.name,
                            status: 'running',
                            progress: Math.random() * 100,
                            message: 'Processing...',
                            records_processed: Math.floor(Math.random() * 10000)
                          }}
                          onStop={() => {
                            // Implement stop functionality
                            console.log('Stop process:', process.task_id);
                          }}
                          onRefresh={() => {
                            refetchProcesses();
                          }}
                        />
                      </Grid>
                    ))}
                  </Grid>
                ) : (
                  <Typography color="text.secondary">
                    No processes currently running
                  </Typography>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Notification Snackbar */}
        <Snackbar
          open={notification.open}
          autoHideDuration={6000}
          onClose={handleCloseNotification}
        >
          <Alert
            onClose={handleCloseNotification}
            severity={notification.severity}
            sx={{ width: '100%' }}
          >
            {notification.message}
          </Alert>
        </Snackbar>
      </Box>
    </LocalizationProvider>
  );
};

export default ProcessControl;
