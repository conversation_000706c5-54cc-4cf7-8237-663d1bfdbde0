#!/usr/bin/env python3
"""
Simple test to verify Windows subprocess fix is working
"""

import requests
import json
import time
import sys

def test_subprocess_execution():
    """Test subprocess execution directly"""
    print("🔧 Testing Windows Subprocess Fix")
    print("=" * 50)
    
    # Test with a simple command that should work
    process_data = {
        "process": "update_index_options",
        "parameters": {
            "dry_run": True,
            "txn_date": "2025-05-24"
        }
    }
    
    try:
        # Start the process
        print("Starting process...")
        response = requests.post(
            "http://localhost:8001/api/v1/processes/start",
            json=process_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code != 200:
            print(f"❌ Process start failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
        
        result = response.json()
        task_id = result.get("task_id")
        print(f"✅ Process started: {task_id}")
        
        # Check status immediately
        time.sleep(2)
        status_response = requests.get(f"http://localhost:8001/api/v1/processes/{task_id}/status")
        
        if status_response.status_code == 200:
            status_data = status_response.json()
            status = status_data.get("status", "unknown")
            error = status_data.get("error", "")
            message = status_data.get("message", "")
            
            print(f"Status: {status}")
            print(f"Message: {message}")
            
            if "NotImplementedError" in error or "NotImplementedError" in message:
                print("❌ Still getting NotImplementedError - Windows fix not working")
                return False
            elif status == "failed" and not error and not message:
                print("✅ Windows subprocess fix working! (No NotImplementedError)")
                print("⚠️  Process failed for other reasons (probably script issues)")
                return True
            elif status in ["running", "completed"]:
                print("✅ Windows subprocess fix working and process running!")
                return True
            else:
                print(f"ℹ️  Status: {status}, investigating...")
                return True
                
        return False
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def test_health():
    """Test basic health"""
    try:
        response = requests.get("http://localhost:8001/health")
        if response.status_code == 200:
            print("✅ Backend server healthy")
            return True
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Backend not accessible: {e}")
        return False

def main():
    print("🚀 Quick Windows Subprocess Fix Test")
    print("=" * 50)
    
    if not test_health():
        print("Backend server not running")
        return False
    
    success = test_subprocess_execution()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 WINDOWS SUBPROCESS FIX IS WORKING!")
        print("✅ No NotImplementedError detected")
        print("✅ Process execution infrastructure functional")
    else:
        print("❌ Windows subprocess fix needs more work")
    
    return success

if __name__ == "__main__":
    main()
