#!/usr/bin/env python3
"""
Hello World Test Script for HKEX Dashboard
A simple test script to validate the dynamic script configuration system.
"""

import argparse
import time
import sys
from datetime import datetime


def main():
    """Main function to handle command line execution"""
    parser = argparse.ArgumentParser(description='Hello World Test Script for HKEX Dashboard')
    parser.add_argument('--message', type=str, default='Hello World!',
                       help='Custom message to display (default: Hello World!)')
    parser.add_argument('--delay', type=int, default=2,
                       help='Delay between progress messages in seconds (default: 2)')
    parser.add_argument('--steps', type=int, default=5,
                       help='Number of progress steps to simulate (default: 5)')
    parser.add_argument('--dry-run', action='store_true',
                       help='Run in dry-run mode (preview only)')
    
    args = parser.parse_args()
    
    print(f"=== Hello World Script Started ===")
    print(f"Timestamp: {datetime.now().isoformat()}")
    print(f"Message: {args.message}")
    print(f"Delay: {args.delay} seconds")
    print(f"Steps: {args.steps}")
    print(f"Dry run: {args.dry_run}")
    print("")
    
    if args.dry_run:
        print("DRY RUN MODE - No actual processing will occur")
        print("This would simulate the following steps:")
        for i in range(1, args.steps + 1):
            print(f"  Step {i}: Processing step {i} of {args.steps}")
        print(f"  Final: Display message '{args.message}'")
        print("DRY RUN COMPLETED")
        return
    
    # Simulate processing steps
    for i in range(1, args.steps + 1):
        print(f"Step {i}/{args.steps}: Processing step {i}...")
        time.sleep(args.delay)
        
        # Simulate different types of progress messages
        if i == 1:
            print("Initializing system...")
        elif i == 2:
            print("Connecting to services...")
        elif i == 3:
            print("Processing data...")
        elif i == args.steps - 1:
            print("Finalizing results...")
        else:
            print(f"Working on step {i}...")
    
    # Final message
    print("")
    print(f"🎉 {args.message}")
    print("")
    print("=== Hello World Script Completed Successfully ===")
    print(f"Processed {args.steps} steps with {args.delay}s delay each")
    print(f"Total execution time: ~{args.steps * args.delay} seconds")


if __name__ == "__main__":
    """
    Entry point for the Hello World test script.
    
    This script is designed to test the HKEX Dashboard's dynamic script
    configuration system. It provides a simple, controllable test case
    for validating script orchestration, parameter passing, and progress monitoring.
    
    Usage:
        python hello_world.py
        python hello_world.py --message "Custom Hello!" --steps 3 --delay 1
        python hello_world.py --dry-run
    
    Features:
        - Configurable message, steps, and timing
        - Dry-run mode for testing
        - Progress simulation with realistic output
        - Proper argument parsing and error handling
    """
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️  Script interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error in hello_world script: {e}")
        print(f"Error type: {type(e).__name__}")
        sys.exit(1)
