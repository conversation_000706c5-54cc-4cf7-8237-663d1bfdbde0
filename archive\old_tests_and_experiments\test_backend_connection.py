#!/usr/bin/env python3
"""
Test script to verify backend connection
"""

import requests
import json
import sys

def test_backend_connection():
    base_url = "http://localhost:8000"
    
    # Test endpoints
    endpoints = [
        "/",
        "/health", 
        "/docs",
        "/api/v1/processes"
    ]
    
    print(f"Testing backend connection to {base_url}")
    print("=" * 50)
    
    for endpoint in endpoints:
        url = f"{base_url}{endpoint}"
        try:
            print(f"Testing {url}...")
            response = requests.get(url, timeout=5)
            print(f"  Status: {response.status_code}")
            
            if endpoint == "/health":
                try:
                    data = response.json()
                    print(f"  Response: {json.dumps(data, indent=2)}")
                except:
                    print(f"  Response: {response.text[:100]}...")
                    
        except requests.exceptions.ConnectionError:
            print(f"  ❌ Connection failed - server not running")
        except requests.exceptions.Timeout:
            print(f"  ❌ Connection timeout")
        except Exception as e:
            print(f"  ❌ Error: {e}")
        print()

if __name__ == "__main__":
    test_backend_connection()
