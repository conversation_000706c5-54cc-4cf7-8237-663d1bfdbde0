#!/usr/bin/env python3
"""
Test script to verify the HKEX module refactoring works correctly.

This script tests the basic functionality of the new hkex_fetcher module
to ensure the separation of business logic from file I/O is working properly.
"""

import os
import sys
from datetime import date, timedelta

# Add the scripts directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from hkex_fetcher import (
        test_hkex_connection,
        test_specific_report_url,
        check_environment,
        fetch_daily_report,
        fetch_weekly_report,
        fetch_hti_report
    )
    print("✓ Successfully imported HKEX fetcher functions")
except ImportError as e:
    print(f"✗ Failed to import HKEX fetcher functions: {e}")
    sys.exit(1)

try:
    from hkex_parser import (
        parse_daily_report_file,
        parse_weekly_report_file,
        parse_hti_report_file
    )
    print("✓ Successfully imported HKEX parser functions")
except ImportError as e:
    print(f"✗ Failed to import HKEX parser functions: {e}")
    sys.exit(1)

try:
    from hkex_processor import (
        process_daily_option_data,
        process_weekly_option_data,
        process_hti_option_data
    )
    print("✓ Successfully imported HKEX processor functions")
except ImportError as e:
    print(f"✗ Failed to import HKEX processor functions: {e}")
    sys.exit(1)

try:
    from hkex_pipeline import HKEXPipeline
    print("✓ Successfully imported HKEX pipeline")
except ImportError as e:
    print(f"✗ Failed to import HKEX pipeline: {e}")
    sys.exit(1)

try:
    from UpdateIndexOptionPostgres import (
        getDailyMarketReport,
        getDailyWOReport,
        getDailyHTIReport,
        getHistReport
    )
    print("✓ Successfully imported main processing functions")
except ImportError as e:
    print(f"✗ Failed to import main processing functions: {e}")
    sys.exit(1)

try:
    from Storacle import getPrice, updatePrice
    print("✓ Successfully imported market data functions from Storacle")
except ImportError as e:
    print(f"✗ Failed to import market data functions from Storacle: {e}")
    sys.exit(1)

try:
    from OptionPricing import d1, d2, call_price, put_price, gamma, charm, calculate_implied_volatility
    print("✓ Successfully imported option pricing functions from OptionPricing")
except ImportError as e:
    print(f"✗ Failed to import option pricing functions from OptionPricing: {e}")
    sys.exit(1)

def test_environment():
    """Test environment setup"""
    print("\n=== Testing Environment ===")
    return check_environment()

def test_connection():
    """Test HKEX website connectivity"""
    print("\n=== Testing HKEX Connection ===")
    return test_hkex_connection()

def test_specific_url():
    """Test specific report URL access"""
    print("\n=== Testing Specific Report URL ===")
    # Test with a recent date
    test_date = date.today() - timedelta(days=1)
    test_url = f"https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/hsio{test_date.strftime('%y%m%d')}.htm"
    return test_specific_report_url(test_url)

def test_module_integration():
    """Test that the modules work together"""
    print("\n=== Testing Module Integration ===")

    # Test that functions are callable (without actually making HTTP requests)
    try:
        # These should be callable without errors
        assert callable(getDailyMarketReport)
        assert callable(getDailyWOReport)
        assert callable(getDailyHTIReport)
        assert callable(getHistReport)
        print("✓ All main processing functions are callable")
        return True
    except Exception as e:
        print(f"✗ Module integration test failed: {e}")
        return False

def test_pipeline_architecture():
    """Test the new pipeline architecture"""
    print("\n=== Testing Pipeline Architecture ===")

    try:
        # Mock functions for testing
        def mock_save_data(data, report_type):
            return len(data)  # Mock successful save

        # Test pipeline creation with debug mode enabled
        pipeline_debug = HKEXPipeline("./test/", mock_save_data, debug_mode=True)
        print("✓ Pipeline instance created successfully (debug mode)")

        # Test pipeline creation with debug mode disabled
        pipeline_prod = HKEXPipeline("./test/", mock_save_data, debug_mode=False)
        print("✓ Pipeline instance created successfully (production mode)")

        # Test health check in debug mode
        health_results_debug = pipeline_debug.run_health_check()
        print(f"✓ Debug mode health check completed")

        # Test health check in production mode (may fail connection test)
        health_results_prod = pipeline_prod.run_health_check()
        print(f"✓ Production mode health check completed")

        return True

    except Exception as e:
        print(f"✗ Pipeline architecture test failed: {e}")
        return False

def test_debug_mode():
    """Test debug mode functionality"""
    print("\n=== Testing Debug Mode ===")

    try:
        import os

        # Test environment variable detection
        original_debug = os.getenv('HKEX_DEBUG')

        # Test with debug enabled
        os.environ['HKEX_DEBUG'] = 'true'
        from hkex_fetcher import is_debug_mode
        assert is_debug_mode() == True
        print("✓ Debug mode detection works (enabled)")

        # Test with debug disabled
        os.environ['HKEX_DEBUG'] = 'false'
        # Need to reload the module to pick up new env var
        import importlib
        import hkex_fetcher
        importlib.reload(hkex_fetcher)
        from hkex_fetcher import is_debug_mode
        assert is_debug_mode() == False
        print("✓ Debug mode detection works (disabled)")

        # Restore original value
        if original_debug is not None:
            os.environ['HKEX_DEBUG'] = original_debug
        elif 'HKEX_DEBUG' in os.environ:
            del os.environ['HKEX_DEBUG']

        return True

    except Exception as e:
        print(f"✗ Debug mode test failed: {e}")
        return False

def test_market_data_functions():
    """Test market data functions in Storacle"""
    print("\n=== Testing Market Data Functions ===")

    try:
        # Test that market data functions are callable
        assert callable(getPrice)
        assert callable(updatePrice)
        print("✓ Market data functions are callable")

        # Test function signatures (without actually calling them)
        import inspect

        # Check getPrice signature
        getPrice_sig = inspect.signature(getPrice)
        assert len(getPrice_sig.parameters) == 2
        print("✓ getPrice has correct signature")

        # Check updatePrice signature
        updatePrice_sig = inspect.signature(updatePrice)
        assert len(updatePrice_sig.parameters) == 1
        print("✓ updatePrice has correct signature")

        return True

    except Exception as e:
        print(f"✗ Market data functions test failed: {e}")
        return False

def test_option_pricing_functions():
    """Test option pricing functions in OptionPricing module"""
    print("\n=== Testing Option Pricing Functions ===")

    try:
        # Test that option pricing functions are callable
        assert callable(d1)
        assert callable(d2)
        assert callable(call_price)
        assert callable(put_price)
        assert callable(gamma)
        assert callable(charm)
        assert callable(calculate_implied_volatility)
        print("✓ Option pricing functions are callable")

        # Test basic calculation (without database dependencies)
        S = 24000  # Stock price
        K = 23600  # Strike price
        T = 0.0122  # Time to expiry
        sigma = 0.2  # Volatility

        d1_val = d1(K, S, sigma, T)
        assert isinstance(d1_val, float)
        print("✓ d1 calculation works")

        d2_val = d2(d1_val, sigma, T)
        assert isinstance(d2_val, float)
        print("✓ d2 calculation works")

        call_val = call_price(K, S, sigma, T)
        assert isinstance(call_val, float) and call_val > 0
        print("✓ call_price calculation works")

        put_val = put_price(K, S, sigma, T)
        assert isinstance(put_val, float) and put_val > 0
        print("✓ put_price calculation works")

        gamma_val = gamma(d1_val, S, sigma, T)
        assert isinstance(gamma_val, float)
        print("✓ gamma calculation works")

        charm_val = charm(d1_val, sigma, T)
        assert isinstance(charm_val, float)
        print("✓ charm calculation works")

        # Test implied volatility
        iv = calculate_implied_volatility(call_val, S, K, T, 'call')
        assert isinstance(iv, float) and 0 < iv < 5  # Reasonable IV range
        print("✓ implied volatility calculation works")

        return True

    except Exception as e:
        print(f"✗ Option pricing functions test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("HKEX Module Refactoring Test")
    print("=" * 40)
    
    tests = [
        ("Environment Check", test_environment),
        ("Debug Mode", test_debug_mode),
        ("Market Data Functions", test_market_data_functions),
        ("Option Pricing Functions", test_option_pricing_functions),
        ("HKEX Connection", test_connection),
        ("Specific URL Test", test_specific_url),
        ("Module Integration", test_module_integration),
        ("Pipeline Architecture", test_pipeline_architecture)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 40)
    print("TEST SUMMARY")
    print("=" * 40)
    
    passed = 0
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        print("\n🎉 All tests passed! HKEX module refactoring is successful.")
        return 0
    else:
        print(f"\n⚠️  {len(results) - passed} test(s) failed. Please check the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
