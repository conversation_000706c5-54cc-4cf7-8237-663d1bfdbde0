#!/usr/bin/env python3
"""
Test script to verify database persistence is working
"""
import sys
import os
import asyncio
from datetime import datetime, timezone

# Add the backend app to the path
sys.path.append('dashboard/backend')

async def test_database_persistence():
    """Test that processes are being saved to the database"""
    try:
        # Import the required modules
        from app.core.database import SessionLocal
        from app.services.process_history_service import ProcessHistoryService
        from app.models.dashboard import ProcessExecution
        
        print("✅ Successfully imported database modules")
        
        # Test database connection
        with SessionLocal() as db:
            # Check if we can query the process_executions table
            count = db.query(ProcessExecution).count()
            print(f"✅ Database connection successful. Found {count} existing process records")
            
            # Get recent processes
            recent_processes = ProcessHistoryService.get_process_history(db, limit=5)
            print(f"✅ Retrieved {len(recent_processes)} recent processes")
            
            for process in recent_processes:
                print(f"  - {process.task_id}: {process.process_name} ({process.status})")
                
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_orchestrator_imports():
    """Test that the orchestrator can import ProcessHistoryService"""
    try:
        from app.services.simple_orchestrator import orchestrator
        print("✅ Successfully imported orchestrator")
        
        # Test that ProcessHistoryService is available
        from app.services.simple_orchestrator import ProcessHistoryService
        print("✅ ProcessHistoryService is available in orchestrator")
        
        return True
        
    except Exception as e:
        print(f"❌ Orchestrator import test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all tests"""
    print("🧪 Testing Database Persistence Implementation")
    print("=" * 50)
    
    # Test 1: Database connection and queries
    print("\n1. Testing database connection...")
    db_test = await test_database_persistence()
    
    # Test 2: Orchestrator imports
    print("\n2. Testing orchestrator imports...")
    orchestrator_test = await test_orchestrator_imports()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    print(f"  Database Connection: {'✅ PASS' if db_test else '❌ FAIL'}")
    print(f"  Orchestrator Imports: {'✅ PASS' if orchestrator_test else '❌ FAIL'}")
    
    if db_test and orchestrator_test:
        print("\n🎉 All tests passed! Database persistence should be working.")
    else:
        print("\n⚠️  Some tests failed. Check the errors above.")

if __name__ == "__main__":
    asyncio.run(main())
