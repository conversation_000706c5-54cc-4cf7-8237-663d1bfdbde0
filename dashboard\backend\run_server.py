#!/usr/bin/env python3
"""
Simple startup script for the HKEX Dashboard backend.
This script sets up the proper Python path and starts the server.
"""
import sys
import os
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Import and run the application
if __name__ == "__main__":
    from app.main import app
    import uvicorn
    
    print("Starting HKEX Dashboard Backend...")
    print(f"Backend directory: {backend_dir}")
    print(f"Python path: {sys.path}")
    
    uvicorn.run(
        app, 
        host="0.0.0.0", 
        port=8000, 
        reload=True,
        reload_dirs=[str(backend_dir / "app")]
    )