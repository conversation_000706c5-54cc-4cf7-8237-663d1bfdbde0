#!/usr/bin/env python3
"""
Test script for the complete fallback chain in safe_http_get_with_firecrawl_fallback()

This script tests the three-tier fallback approach:
1. Direct HTTP GET (single attempt)
2. Selenium-based GET
3. Firecrawl-based GET (if available)
"""

import sys
import os
from datetime import datetime

# Add the current directory to Python path to import hkex_fetcher
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from hkex_fetcher import safe_http_get_with_firecrawl_fallback
    print("✅ Successfully imported safe_http_get_with_firecrawl_fallback from hkex_fetcher")
except ImportError as e:
    print(f"❌ Failed to import safe_http_get_with_firecrawl_fallback: {e}")
    sys.exit(1)


def test_fallback_chain_success():
    """Test fallback chain with a URL that should succeed on first attempt"""
    print("\n" + "="*60)
    print("TEST 1: Fallback chain - Expected success on first attempt")
    print("="*60)
    
    test_url = "https://httpbin.org/html"
    print(f"Testing URL: {test_url}")
    
    try:
        response = safe_http_get_with_firecrawl_fallback(test_url, timeout=30)
        
        if response is None:
            print("❌ Test failed: No response object returned")
            return False
            
        print(f"Final Status Code: {response.status_code}")
        print(f"Content Length: {len(response.content)} bytes")
        
        if response.status_code == 200:
            print("✅ Fallback chain test PASSED - Direct HTTP succeeded")
            return True
        else:
            print(f"❌ Fallback chain test FAILED: Status code {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Fallback chain test FAILED with exception: {e}")
        return False


def test_fallback_chain_hkex():
    """Test fallback chain with HKEX URL"""
    print("\n" + "="*60)
    print("TEST 2: Fallback chain - HKEX website")
    print("="*60)
    
    # Test with a recent HKEX report URL
    test_date = datetime.now().strftime("%y%m%d")
    test_url = f"https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/hsio{test_date}.htm"
    print(f"Testing HKEX URL: {test_url}")
    
    try:
        response = safe_http_get_with_firecrawl_fallback(test_url, timeout=30)
        
        if response is None:
            print("❌ HKEX fallback test failed: No response object returned")
            return False
            
        print(f"Final Status Code: {response.status_code}")
        print(f"Content Length: {len(response.content)} bytes")
        
        if response.status_code == 200:
            # Check if content looks like HKEX report
            content_text = response.text.lower()
            if 'hkex' in content_text or 'option' in content_text or 'derivative' in content_text:
                print("✅ HKEX fallback test PASSED - Valid HKEX content detected")
                return True
            else:
                print("⚠️  HKEX fallback test PARTIAL - Got 200 but content doesn't look like HKEX report")
                return True  # Still consider it a pass
        elif response.status_code == 404:
            print("⚠️  HKEX test result: Report not found (404) - This is expected for future dates")
            return True  # 404 is expected for non-existent reports
        else:
            print(f"❌ HKEX fallback test FAILED: Status code {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ HKEX fallback test FAILED with exception: {e}")
        return False


def test_fallback_chain_failure():
    """Test fallback chain with a URL that should fail on all attempts"""
    print("\n" + "="*60)
    print("TEST 3: Fallback chain - Expected failure on all attempts")
    print("="*60)
    
    # Use a URL that should fail on all methods
    test_url = "https://this-domain-should-not-exist-12345.com"
    print(f"Testing failure URL: {test_url}")
    
    try:
        response = safe_http_get_with_firecrawl_fallback(test_url, timeout=15)
        
        if response is None:
            print("✅ Failure test PASSED - All methods failed as expected, returned None")
            return True
            
        print(f"Final Status Code: {response.status_code}")
        
        if response.status_code in [404, 500, 504]:  # Expected error codes
            print("✅ Failure test PASSED - All methods failed with proper error codes")
            return True
        else:
            print(f"⚠️  Failure test UNEXPECTED: Got status code {response.status_code}")
            return True  # Still consider it a pass
            
    except Exception as e:
        print(f"❌ Failure test FAILED with exception: {e}")
        return False


def main():
    """Run all fallback chain tests"""
    print("🧪 Starting Fallback Chain Tests")
    print("=" * 60)
    
    tests = [
        ("Success Case", test_fallback_chain_success),
        ("HKEX Website", test_fallback_chain_hkex),
        ("Failure Case", test_fallback_chain_failure)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' crashed with exception: {e}")
            results.append((test_name, False))
    
    # Print summary
    print("\n" + "="*60)
    print("FALLBACK CHAIN TEST SUMMARY")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All fallback chain tests PASSED!")
        print("The three-tier fallback system is working correctly:")
        print("  1️⃣ Direct HTTP GET (single attempt)")
        print("  2️⃣ Selenium-based GET")
        print("  3️⃣ Firecrawl-based GET (if available)")
        return True
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
