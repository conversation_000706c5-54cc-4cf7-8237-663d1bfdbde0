drop view t_stock_option_min_delta;
CREATE VIEW v_stock_option_min_delta AS
    SELECT 
        b.cmonth AS cmonth,
        b.txn_date AS txn_date,
        b.strike AS strike,
        b.call_delta AS call_delta,
        b.put_delta AS put_delta,
        b.abs_delta AS abs_delta,
        b.total_gamma AS total_gamma, 
        left(b.cmonth, 3) as symb, 
        substr(b.cmonth, 5,5) as month
    FROM
        ((SELECT 
            cmonth, txn_date, MIN(abs_delta) AS abs_delta
        FROM
            t_delta_all_strikes
        GROUP BY cmonth , txn_date) a
        JOIN t_delta_all_strikes b ON (((a.txn_date = b.txn_date)
            AND (a.cmonth = b.cmonth)
            AND (a.abs_delta = b.abs_delta))));



select * from t_delta_all_strikes where txn_date='2025-05-16' limit 10

"cmonth"	"txn_date_txt"	"strike"	"call_delta"	"put_delta"	"abs_delta"	"total_gamma"	"txn_date"
"A50.27JUN25"		11.50	48.25	1439.52	1487.77	265.98	"2025-05-16"
"A50.27JUN25"		12.00	79.69	1303.75	1383.44	386.38	"2025-05-16"
"A50.27JUN25"		12.50	152.90	1119.93	1272.83	598.20	"2025-05-16"
"A50.27JUN25"		13.00	273.90	889.05	1162.95	804.42	"2025-05-16"
"A50.27JUN25"		13.50	525.35	634.03	1159.38	1085.28	"2025-05-16"
"A50.27JUN25"		14.00	865.10	398.27	1263.37	1190.55	"2025-05-16"
"A50.27JUN25"		14.50	1296.23	212.13	1508.36	1152.14	"2025-05-16"
"A50.27JUN25"		15.00	1723.60	89.01	1812.61	933.76	"2025-05-16"
"A50.27JUN25"		17.00	2666.54	0.00	2666.54	222.65	"2025-05-16"
"A50.29MAY25"		12.00	13.07	1766.58	1779.65	208.61	"2025-05-16"