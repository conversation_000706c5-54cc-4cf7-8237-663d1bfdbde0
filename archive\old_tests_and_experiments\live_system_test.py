#!/usr/bin/env python3
"""
Live System Test for HKEX Dashboard
Tests the running backend and frontend servers
"""

import asyncio
import json
import time
import requests
import websockets
from datetime import datetime

class HKEXLiveSystemTest:
    def __init__(self):
        self.backend_url = "http://localhost:8000"
        self.frontend_url = "http://localhost:3000"
        self.websocket_url = "ws://localhost:8000/ws"
        self.test_results = []
        
    def log_test(self, test_name, success, message="", details=None):
        """Log test results"""
        result = {
            "test": test_name,
            "success": success,
            "message": message,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} | {test_name}: {message}")
        if details:
            print(f"       Details: {details}")
        
    def test_backend_health(self):
        """Test backend server health"""
        try:
            response = requests.get(f"{self.backend_url}/", timeout=5)
            if response.status_code == 200:
                self.log_test("Backend Health", True, "Backend server is responding")
                return True
            else:
                self.log_test("Backend Health", False, f"Backend returned status {response.status_code}")
                return False
        except requests.exceptions.ConnectionError:
            self.log_test("Backend Health", False, "Could not connect to backend server")
            return False
        except Exception as e:
            self.log_test("Backend Health", False, f"Backend test failed: {str(e)}")
            return False
    
    def test_frontend_health(self):
        """Test frontend server health"""
        try:
            response = requests.get(f"{self.frontend_url}", timeout=10)
            if response.status_code == 200:
                self.log_test("Frontend Health", True, "Frontend server is responding")
                return True
            else:
                self.log_test("Frontend Health", False, f"Frontend returned status {response.status_code}")
                return False
        except requests.exceptions.ConnectionError:
            self.log_test("Frontend Health", False, "Could not connect to frontend server (may still be starting)")
            return False
        except Exception as e:
            self.log_test("Frontend Health", False, f"Frontend test failed: {str(e)}")
            return False
    
    def test_api_endpoints(self):
        """Test API endpoints"""
        endpoints = [
            ("/health", "Health Check"),
            ("/api/processes", "Process List"),
            ("/api/process-logs", "Process Logs")
        ]
        
        for endpoint, name in endpoints:
            try:
                response = requests.get(f"{self.backend_url}{endpoint}", timeout=5)
                if response.status_code in [200, 404]:  # 404 is expected for some endpoints without data
                    self.log_test(f"API {name}", True, f"Endpoint {endpoint} responded")
                else:
                    self.log_test(f"API {name}", False, f"Endpoint {endpoint} returned {response.status_code}")
            except Exception as e:
                self.log_test(f"API {name}", False, f"Endpoint {endpoint} failed: {str(e)}")
    
    async def test_websocket_connection(self):
        """Test WebSocket connection"""
        try:
            # Test WebSocket connection
            async with websockets.connect(self.websocket_url) as websocket:
                self.log_test("WebSocket Connection", True, "WebSocket connection established")
                
                # Send a test message
                test_message = {"type": "ping", "data": "test"}
                await websocket.send(json.dumps(test_message))
                
                # Wait for response (with timeout)
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    self.log_test("WebSocket Message", True, "WebSocket communication successful", response)
                except asyncio.TimeoutError:
                    self.log_test("WebSocket Message", True, "WebSocket connection works (no response expected)")
                
                return True
                
        except Exception as e:
            self.log_test("WebSocket Connection", False, f"WebSocket test failed: {str(e)}")
            return False
    
    def test_process_management(self):
        """Test process management API"""
        try:
            # Test starting a simple process
            start_data = {
                "script_path": "echo",
                "args": ["Hello from HKEX Dashboard"],
                "working_directory": "C:\\"
            }
            
            response = requests.post(f"{self.backend_url}/api/start-process", 
                                   json=start_data, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                process_id = result.get("process_id")
                self.log_test("Process Start", True, f"Process started with ID: {process_id}")
                
                # Test getting process status
                time.sleep(1)
                status_response = requests.get(f"{self.backend_url}/api/processes", timeout=5)
                if status_response.status_code == 200:
                    self.log_test("Process Status", True, "Process status retrieved")
                else:
                    self.log_test("Process Status", False, f"Status check failed: {status_response.status_code}")
                    
            else:
                self.log_test("Process Start", False, f"Process start failed: {response.status_code}")
                
        except Exception as e:
            self.log_test("Process Management", False, f"Process management test failed: {str(e)}")
    
    def run_all_tests(self):
        """Run all live system tests"""
        print("=" * 60)
        print("🚀 HKEX Dashboard Live System Test")
        print("=" * 60)
        print(f"Backend URL: {self.backend_url}")
        print(f"Frontend URL: {self.frontend_url}")
        print(f"WebSocket URL: {self.websocket_url}")
        print("-" * 60)
        
        # Test backend
        backend_ok = self.test_backend_health()
        
        if backend_ok:
            self.test_api_endpoints()
            
            # Test WebSocket asynchronously
            try:
                asyncio.run(self.test_websocket_connection())
            except Exception as e:
                self.log_test("WebSocket Test", False, f"WebSocket test error: {str(e)}")
            
            # Test process management
            self.test_process_management()
        
        # Test frontend (may still be starting)
        self.test_frontend_health()
        
        # Generate summary
        self.generate_summary()
    
    def generate_summary(self):
        """Generate test summary"""
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ FAILED TESTS:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"   - {result['test']}: {result['message']}")
        
        # Save detailed results
        with open("live_test_results.json", "w") as f:
            json.dump(self.test_results, f, indent=2)
        
        print(f"\n📄 Detailed results saved to: live_test_results.json")
        
        if passed_tests == total_tests:
            print("\n🎉 ALL TESTS PASSED! System is ready for production use.")
        else:
            print(f"\n⚠️  {failed_tests} test(s) failed. Please check the issues above.")

if __name__ == "__main__":
    tester = HKEXLiveSystemTest()
    tester.run_all_tests()