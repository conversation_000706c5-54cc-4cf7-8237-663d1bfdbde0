"""
HKEX Report Processor Module

This module handles ONLY the processing and calculation of option data:
- Black-Scholes calculations (delta, gamma, charm)
- Option pricing calculations
- Risk metrics computation
- Data enrichment with calculated fields

The processor takes parsed raw data and adds calculated fields.
It knows nothing about HTML parsing or database operations.
"""

from scipy.stats import norm
from Storacle import getDay2Expiry, getWODay2Expiry, getPrice
from OptionPricing import d1, gamma, charm
import math


class HKEXOptionProcessor:
    """Processor for option calculations and risk metrics"""

    def __init__(self):
        """
        Initialize processor.

        Price lookup is handled by importing getPrice from Storacle module.
        """
        pass
    
    def process_daily_data(self, parsed_data, symb, trade_date):
        """
        Process daily option data with Black-Scholes calculations.

        Args:
            parsed_data: List of parsed option records
            symb: Symbol (HSI, HHI, MHI, HTI)
            trade_date: Trading date

        Returns:
            List of processed records with calculated fields
        """
        stock_price = getPrice(symb, trade_date)
        print(f'Processing daily data: {symb}, {trade_date}, stock_price={stock_price}')

        processed_data_list = []

        for record in parsed_data:
            processed_record = self._add_calculations(record, stock_price, 'daily')
            if processed_record:
                processed_data_list.append(processed_record)

        return processed_data_list
    
    def process_weekly_data(self, parsed_data, symb, trade_date):
        """
        Process weekly option data with Black-Scholes calculations.

        Args:
            parsed_data: List of parsed option records
            symb: Symbol (HSI, HHI, HTI)
            trade_date: Trading date

        Returns:
            List of processed records with calculated fields
        """
        stock_price = getPrice(symb, trade_date)
        print(f'Processing weekly data: {symb}, {trade_date}, stock_price={stock_price}')

        processed_data_list = []

        for record in parsed_data:
            processed_record = self._add_calculations(record, stock_price, 'weekly')
            if processed_record:
                processed_data_list.append(processed_record)

        return processed_data_list
    
    def process_hti_data(self, parsed_data, symb, trade_date):
        """
        Process HTI option data with Black-Scholes calculations.

        Args:
            parsed_data: List of parsed option records
            symb: Symbol (HTI)
            trade_date: Trading date

        Returns:
            List of processed records with calculated fields
        """
        stock_price = getPrice(symb, trade_date)
        print(f'Processing HTI data: {symb}, {trade_date}, stock_price={stock_price}')

        processed_data_list = []

        for record in parsed_data:
            processed_record = self._add_calculations(record, stock_price, 'hti')
            if processed_record:
                processed_data_list.append(processed_record)

        return processed_data_list

    def process_stock_option_data(self, parsed_data, trade_date):
        """
        Process stock option data with Black-Scholes calculations.

        Args:
            parsed_data: List of parsed stock option records
            trade_date: Trading date

        Returns:
            List of processed records with calculated fields
        """
        print(f'Processing stock option data for date: {trade_date}')

        processed_data_list = []

        for record in parsed_data:
            # Stock price is already included in the record from parsing
            processed_record = self._add_stock_option_calculations(record, 'stock_option')
            if processed_record:
                processed_data_list.append(processed_record)

        return processed_data_list

    def _add_calculations(self, record, stock_price, report_type):
        """
        Add Black-Scholes calculations to a record.
        
        Args:
            record: Parsed option record
            stock_price: Current stock price
            report_type: 'daily', 'weekly', or 'hti'
            
        Returns:
            Record with added calculated fields
        """
        try:
            # Create a copy of the record
            processed_record = record.copy()
            
            # Add stock price
            processed_record['stock_price'] = stock_price
            
            # Calculate time to expiry
            if report_type == 'weekly':
                time_to_expiry = getWODay2Expiry(record['contract_code'], record['txn_date']) / 247
            else:
                time_to_expiry = getDay2Expiry(record['contract_month'], record['txn_date']) / 247
            
            processed_record['time_to_expiry'] = time_to_expiry
            
            # Convert IV from percentage to decimal
            iv_decimal = record['iv_percent'] / 100.0
            processed_record['iv_decimal'] = iv_decimal
            
            # Calculate d1 for Black-Scholes with safeguards
            try:
                d = d1(record['strike_price'], stock_price, iv_decimal, time_to_expiry)
                processed_record['d1'] = d

                # Calculate delta
                cdelta = norm.cdf(d)
                if record['call_put'] == 'C':
                    delta = cdelta
                else:
                    delta = cdelta - 1  # Put delta

                processed_record['delta'] = delta

                # Calculate gamma with safeguards
                g = gamma(d, stock_price, iv_decimal, time_to_expiry)
                processed_record['gamma'] = g

                # Calculate charm (delta decay) with safeguards
                if report_type != 'weekly':  # Weekly options don't typically use charm
                    c = charm(d, iv_decimal, time_to_expiry)
                    processed_record['charm'] = c
                else:
                    processed_record['charm'] = 0

            except Exception as e:
                # Fallback values if calculations fail
                print(f"Warning: Greeks calculation failed for {record.get('inst_name', 'unknown')}: {e}")
                processed_record['d1'] = 0
                processed_record['delta'] = 0
                processed_record['gamma'] = 0
                processed_record['charm'] = 0
            
            # Calculate moneyness
            moneyness = record['strike_price'] / stock_price
            processed_record['moneyness'] = moneyness
            
            # Classify option type
            if moneyness > 1.02:
                option_type = 'OTM' if record['call_put'] == 'C' else 'ITM'
            elif moneyness < 0.98:
                option_type = 'ITM' if record['call_put'] == 'C' else 'OTM'
            else:
                option_type = 'ATM'
            
            processed_record['option_type'] = option_type
            
            # Calculate theoretical value (simplified Black-Scholes)
            theoretical_value = self._calculate_theoretical_value(
                record['strike_price'], stock_price, iv_decimal, 
                time_to_expiry, record['call_put']
            )
            processed_record['theoretical_value'] = theoretical_value
            
            # Calculate implied volatility rank (if we had historical IV data)
            # For now, just mark high/low IV
            if iv_decimal > 0.3:
                iv_rank = 'HIGH'
            elif iv_decimal < 0.15:
                iv_rank = 'LOW'
            else:
                iv_rank = 'MEDIUM'
            
            processed_record['iv_rank'] = iv_rank
            
            return processed_record
            
        except Exception as e:
            print(f"Error processing record {record.get('inst_name', 'unknown')}: {e}")
            return None
    
    def _calculate_theoretical_value(self, strike, spot, iv, time_to_expiry, call_put):
        """
        Calculate theoretical option value using Black-Scholes formula.

        Args:
            strike: Strike price
            spot: Current stock price
            iv: Implied volatility (decimal)
            time_to_expiry: Time to expiry in years
            call_put: 'C' for call, 'P' for put

        Returns:
            Theoretical option value
        """
        try:
            # Input validation and safeguards
            strike_safe = max(float(strike), 0.01)
            spot_safe = max(float(spot), 0.01)
            iv_safe = max(float(iv), 0.0001)  # Minimum 0.01% volatility
            time_safe = max(float(time_to_expiry), 0.0001)  # Minimum time

            if time_safe <= 0.0001:
                # Option has expired or very close to expiry
                if call_put == 'C':
                    return max(0, spot_safe - strike_safe)
                else:
                    return max(0, strike_safe - spot_safe)

            # Risk-free rate (simplified - using 0 for now)
            r = 0.0

            # Safeguard against division by zero in d1 calculation
            denominator = iv_safe * math.sqrt(time_safe)
            if denominator == 0:
                # Fallback to intrinsic value
                if call_put == 'C':
                    return max(0, spot_safe - strike_safe)
                else:
                    return max(0, strike_safe - spot_safe)

            # Calculate d1 and d2 with safeguards
            try:
                log_ratio = math.log(spot_safe / strike_safe)
            except (ValueError, ZeroDivisionError):
                # Fallback if log calculation fails
                if call_put == 'C':
                    return max(0, spot_safe - strike_safe)
                else:
                    return max(0, strike_safe - spot_safe)

            d1_val = (log_ratio + (r + 0.5 * iv_safe * iv_safe) * time_safe) / denominator
            d2_val = d1_val - iv_safe * math.sqrt(time_safe)

            if call_put == 'C':
                # Call option
                value = spot_safe * norm.cdf(d1_val) - strike_safe * math.exp(-r * time_safe) * norm.cdf(d2_val)
            else:
                # Put option
                value = strike_safe * math.exp(-r * time_safe) * norm.cdf(-d2_val) - spot_safe * norm.cdf(-d1_val)

            return max(0, value)

        except Exception as e:
            print(f"Error calculating theoretical value: {e}")
            # Return intrinsic value as fallback
            try:
                if call_put == 'C':
                    return max(0, float(spot) - float(strike))
                else:
                    return max(0, float(strike) - float(spot))
            except:
                return 0

    def _add_stock_option_calculations(self, record, report_type):
        """
        Add Black-Scholes calculations to a stock option record.

        Args:
            record: Parsed stock option record
            report_type: 'stock_option'

        Returns:
            Record with added calculated fields
        """
        try:
            # Create a copy of the record
            processed_record = record.copy()

            # Stock price is already in the record
            stock_price = record['stock_price']

            # Calculate time to expiry using getWODay2Expiry for stock options
            from Storacle import getWODay2Expiry
            time_to_expiry = getWODay2Expiry(record['contract_month'][0:2] + '-' + record['contract_month'][2:5] + '-' + record['contract_month'][-2:], record['txn_date']) / 247
            processed_record['time_to_expiry'] = time_to_expiry

            # Convert IV from percentage to decimal
            iv_decimal = record['iv_percent'] / 100.0
            processed_record['iv_decimal'] = iv_decimal

            # Calculate d1 for Black-Scholes with safeguards
            try:
                d = d1(record['strike_price'], stock_price, iv_decimal, time_to_expiry)
                processed_record['d1'] = d

                # Calculate delta
                from scipy.stats import norm
                cdelta = norm.cdf(d)
                if record['call_put'] == 'C':
                    delta = cdelta
                else:
                    delta = cdelta - 1  # Put delta

                processed_record['delta'] = delta

                # Calculate gamma with safeguards
                g = gamma(d, stock_price, iv_decimal, time_to_expiry)
                processed_record['gamma'] = g

                # Stock options don't typically use charm in the original script
                processed_record['charm'] = 0

            except Exception as e:
                # Fallback values if calculations fail
                print(f"Warning: Greeks calculation failed for {record.get('inst_name', 'unknown')}: {e}")
                processed_record['d1'] = 0
                processed_record['delta'] = 0
                processed_record['gamma'] = 0
                processed_record['charm'] = 0

            # Calculate moneyness
            moneyness = record['strike_price'] / stock_price
            processed_record['moneyness'] = moneyness

            # Classify option type
            if moneyness > 1.02:
                option_type = 'OTM' if record['call_put'] == 'C' else 'ITM'
            elif moneyness < 0.98:
                option_type = 'ITM' if record['call_put'] == 'C' else 'OTM'
            else:
                option_type = 'ATM'

            processed_record['option_type'] = option_type

            # Calculate theoretical value (simplified Black-Scholes)
            theoretical_value = self._calculate_theoretical_value(
                record['strike_price'], stock_price, iv_decimal,
                time_to_expiry, record['call_put']
            )
            processed_record['theoretical_value'] = theoretical_value

            # Calculate implied volatility rank (if we had historical IV data)
            # For now, just mark high/low IV
            if iv_decimal > 0.3:
                iv_rank = 'HIGH'
            elif iv_decimal < 0.15:
                iv_rank = 'LOW'
            else:
                iv_rank = 'MEDIUM'

            processed_record['iv_rank'] = iv_rank

            return processed_record

        except Exception as e:
            print(f"Error processing stock option record {record.get('inst_name', 'unknown')}: {e}")
            return None


# Convenience functions for external use
def process_daily_option_data(parsed_data, symb, trade_date):
    """
    Convenience function to process daily option data.

    Args:
        parsed_data: List of parsed option records
        symb: Symbol (HSI, HHI, MHI, HTI)
        trade_date: Trading date

    Returns:
        List of processed records with calculated fields
    """
    processor = HKEXOptionProcessor()
    return processor.process_daily_data(parsed_data, symb, trade_date)


def process_weekly_option_data(parsed_data, symb, trade_date):
    """
    Convenience function to process weekly option data.

    Args:
        parsed_data: List of parsed option records
        symb: Symbol (HSI, HHI, HTI)
        trade_date: Trading date

    Returns:
        List of processed records with calculated fields
    """
    processor = HKEXOptionProcessor()
    return processor.process_weekly_data(parsed_data, symb, trade_date)


def process_hti_option_data(parsed_data, symb, trade_date):
    """
    Convenience function to process HTI option data.

    Args:
        parsed_data: List of parsed option records
        symb: Symbol (HTI)
        trade_date: Trading date

    Returns:
        List of processed records with calculated fields
    """
    processor = HKEXOptionProcessor()
    return processor.process_hti_data(parsed_data, symb, trade_date)


def process_stock_option_data(parsed_data, trade_date):
    """
    Convenience function to process stock option data.

    Args:
        parsed_data: List of parsed stock option records
        trade_date: Trading date

    Returns:
        List of processed records with calculated fields
    """
    processor = HKEXOptionProcessor()
    return processor.process_stock_option_data(parsed_data, trade_date)
