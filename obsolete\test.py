#%%
# test.py
# pip install oracledb --upgrade --user
import os
import oracledb


try:
    un = os.environ.get('oracle_user')
    pw = os.environ.get('oracle_pw')
    cs = os.environ.get('oracle_storacle_cs')
    print("Default array size:", oracledb.defaults.arraysize)
    un='admin'
    pw='Jh7286@Storacle'
    cs = '(description= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1522)(host=adb.us-phoenix-1.oraclecloud.com))(connect_data=(service_name=kqnzrj2cjvj4tev_storacle_high.adb.oraclecloud.com))(security=(ssl_server_dn_match=yes)))'
    # (description= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1522)(host=adb.us-phoenix-1.oraclecloud.com))(connect_data=(service_name=kqnzrj2cjvj4tev_storacle_high.adb.oraclecloud.com))(security=(ssl_server_dn_match=yes)))'
    print(cs)
    with oracledb.connect(user=un, password=pw, dsn=cs) as connection:
        with connection.cursor() as cursor:
            sql = """select 'STORACLE', sysdate from dual"""
            for r in cursor.execute(sql):
                print(r)
except Exception as e:
    print(f"Error connecting to Oracle: {e}")

# try:
#     #powinvests
#     cs=os.getenv('oracle_powinvests_cs')
#     un='admin'
#     pw='Jh7286@Oracle'
#     cs='(description= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1522)(host=adb.us-phoenix-1.oraclecloud.com))(connect_data=(service_name=kqnzrj2cjvj4tev_powinvests_tp.adb.oraclecloud.com))(security=(ssl_server_dn_match=yes)))'
#     print(cs)
#     with oracledb.connect(user=un, password=pw, dsn=cs) as connection:
#         with connection.cursor() as cursor:
#             sql = """select 'POWINVESTS', sysdate from dual"""
#             for r in cursor.execute(sql):
#                 print(r)
# except Exception as e:
#     print(f"Error connecting to Oracle POWINVESTS: {e}")

# %%
