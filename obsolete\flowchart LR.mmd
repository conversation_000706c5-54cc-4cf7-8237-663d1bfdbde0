flowchart LR
    %% Frontend Technologies
    subgraph Frontend["`🎨 **Frontend Stack**`"]
        React["`⚛️ **React 18**
        - Component-based UI
        - Virtual DOM
        - Hooks & Context`"]
        
        TypeScript["`📘 **TypeScript**
        - Type Safety
        - IntelliSense
        - Compile-time Checks`"]
        
        Tailwind["`🎨 **Tailwind CSS**
        - Utility-first CSS
        - Responsive Design
        - Custom Components`"]
        
        React --> TypeScript
        TypeScript --> Tailwind
    end
    
    %% Backend Technologies
    subgraph Backend["`⚙️ **Backend Stack**`"]
        FastAPI["`🚀 **FastAPI**
        - Async/Await Support
        - Auto API Documentation
        - Pydantic Validation`"]
        
        Python["`🐍 **Python 3.11+**
        - Latest Language Features
        - Performance Improvements
        - Type Hints`"]
        
        SQLAlchemy["`🗄️ **SQLAlchemy**
        - ORM & Core
        - Database Migrations
        - Query Builder`"]
        
        Uvicorn["`⚡ **Uvicorn**
        - ASGI Server
        - High Performance
        - WebSocket Support`"]
        
        Python --> FastAPI
        FastAPI --> SQLAlchemy
        FastAPI --> Uvicorn
    end
    
    %% Data & Cache Layer
    subgraph Data["`💾 **Data Layer**`"]
        PostgreSQL["`🐘 **PostgreSQL**
        - ACID Transactions
        - JSON Support
        - Full-text Search`"]
        
        Redis["`🔴 **Redis**
        - In-Memory Cache
        - Pub/Sub Messaging
        - Data Structures`"]
        
        Pandas["`🐼 **Pandas**
        - Data Analysis
        - DataFrame Operations
        - CSV/Excel Processing`"]
        
        NumPy["`🔢 **NumPy**
        - Numerical Computing
        - Array Operations
        - Mathematical Functions`"]
        
        PostgreSQL --> Redis
        Pandas --> NumPy
    end
    
    %% Infrastructure Layer
    subgraph Infrastructure["`🏗️ **Infrastructure**`"]
        Docker["`🐳 **Docker**
        - Containerization
        - Consistent Environments
        - Easy Deployment`"]
        
        DockerCompose["`📋 **Docker Compose**
        - Multi-container Apps
        - Service Orchestration
        - Development Workflow`"]
        
        Nginx["`🌐 **Nginx**
        - Reverse Proxy
        - Load Balancing
        - Static File Serving`"]
        
        Celery["`⚙️ **Celery**
        - Distributed Task Queue
        - Async Processing
        - Scheduled Jobs`"]
        
        Docker --> DockerCompose
        Docker --> Nginx
        Docker --> Celery
    end
    
    %% Connections between stacks
    Frontend --> Backend
    Backend --> Data
    Backend --> Infrastructure
    
    %% Styling
    classDef frontendColor fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef backendColor fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef dataColor fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef infraColor fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    
    class React,TypeScript,Tailwind frontendColor
    class FastAPI,Python,SQLAlchemy,Uvicorn backendColor
    class PostgreSQL,Redis,Pandas,NumPy dataColor
    class Docker,DockerCompose,Nginx,Celery infraColor