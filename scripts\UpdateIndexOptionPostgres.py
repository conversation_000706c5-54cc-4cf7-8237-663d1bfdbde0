# # HSI/HHI/MHI Option Market Report
# # HSI/HHI/MHI Option Market Report

# %%
# from typing_extensions import IntVar
# HTTP-related imports moved to hkex_fetcher module
import pandas as pd
from sqlalchemy import create_engine
from datetime import datetime
from pandas.tseries.offsets import MonthEnd, BMonthEnd
import os
import pandas as pd
# import numpy as np
# yfinance import moved to Storacle module
import datetime as dt
from scipy.stats import norm
from Storacle import listMonths, getWODay2Expiry, getDay2Expiry
from OptionPricing import d1, gamma
# Timezone support for HKEX
try:
    from zoneinfo import ZoneInfo
    HK_TZ = ZoneInfo('Asia/Hong_Kong')
except ImportError:
    # Fallback for older Python versions
    try:
        import pytz
        HK_TZ = pytz.timezone('Asia/Hong_Kong')
    except ImportError:
        HK_TZ = None
# from bs import implVolNR
from dotenv import load_dotenv
from hkex_pipeline import (
    HKEXPipeline, getDailyMarketReport, getDailyWOReport, getDailyHTIReport
)
from hkex_fetcher import test_hkex_connection, test_specific_report_url, check_environment

load_dotenv()
LOG_LEVEL = int(os.environ.get('LOG_LEVEL'))
SQL_ECHO = int(os.environ.get('SQL_ECHO'))
platform = os.environ.get('WILL9700_PLATFORM')
db = os.environ.get('WILL9700_DB')
pathname = os.getenv('out_path')
# print(db)
# print(pathname)
# Create engine with SQLAlchemy 2.0 style
cnx = create_engine(db, isolation_level="AUTOCOMMIT", echo=SQL_ECHO)

# HTTP session functions moved to hkex_fetcher module

def get_hkex_today():
    """Get today's date in Hong Kong timezone for HKEX trading"""
    if HK_TZ:
        hk_now = dt.datetime.now(HK_TZ)
        return hk_now.date()
    else:
        # Fallback to system date if timezone not available
        print("⚠️ Hong Kong timezone not available, using system date")
        return dt.date.today()

def get_hkex_yesterday():
    """Get yesterday's date in Hong Kong timezone for HKEX trading"""
    return get_hkex_today() - dt.timedelta(days=1)

# %%
# From HKEXDailyMarketReport.py

# Dictionary to store ticker data across function calls
_TICKER_DATA_CACHE = {}

# Function to get all tickers needed for the update
def get_all_tickers_for_update():
    """Get all ticker symbols needed for the update."""
    tickers = []
    # Add the main index tickers
    tickers.append('^HSI')  # HSI
    tickers.append('^HSCE')  # HHI
    # tickers.append('3032.HK')  # HTI
    tickers.append('HSTECH.HK')  # HTI

    # Add any other tickers needed for the update
    # This could be extended to query the database for all tickers

    return tickers

# Function to pre-download all ticker data
# updatePrice function moved to Storacle module

# getPrice function moved to Storacle module

def insert_report(iinst_name, ixtxn_date, ixopen, ixhigh, ixlow, ixclose, ixvolume, itxn_date, iopen, ihigh, ilow, iclose, ivolume, iiv, ioi, ioi_change, istock_price, idelta, ig, ic ):
    # Use SQLAlchemy 2.0 style with text() for SQL statements
    from sqlalchemy import text

    x = 0
    sql = text("INSERT INTO option_daily_report(inst_name, xtxn_date, xopen, xhigh, xlow, xclose, xvolume, \
        txn_date, open, high, low, close, volume, iv, oi, oi_change , stock_price, delta, gamma, charm) \
            VALUES (:inst_name, :xtxn_date, :xopen, :xhigh, :xlow, :xclose, :xvolume, :txn_date, \
            :open, :high, :low, :close, :volume, :iv, :oi, :oi_change, :stock_price, :delta, :gamma, :charm)")

    # Create parameter dictionary
    params = {
        "inst_name": iinst_name,
        "xtxn_date": ixtxn_date,
        "xopen": ixopen,
        "xhigh": ixhigh,
        "xlow": ixlow,
        "xclose": ixclose,
        "xvolume": ixvolume,
        "txn_date": itxn_date,
        "open": iopen,
        "high": ihigh,
        "low": ilow,
        "close": iclose,
        "volume": ivolume,
        "iv": iiv,
        "oi": ioi,
        "oi_change": ioi_change,
        "stock_price": float(istock_price),
        "delta": float(idelta),
        "gamma": float(ig),
        "charm": float(ic)
    }

    try:
        # Use connection context manager
        with cnx.connect() as conn:
            conn.execute(sql, params)
            conn.commit()
            x = 1
    except Exception as e:
        print(iinst_name, ixtxn_date, str(e), flush=True)

    return x

#getContract('HSI.OCT-20',dt.date(2020, 10, 1) )
def getContract(isymbmonth, d):
    from sqlalchemy import text

    # Use parameterized query with text()
    query = text("""
        SELECT
            a.txn_id, a.txn_date,
            substr(a.inst_name,1,3) as symb,
            substr(a.inst_name,5,6) as cont_month,
            to_number(substr(a.inst_name,12,5),'999999') as strike,
            substr(a.inst_name,1,16) as inst,
            substr(a.inst_name,18,1) as cp,
            a.close, a.iv::decimal/100 as iv, a.oi,
            a.stock_price as stock_price
        FROM option_daily_report a
        WHERE a.txn_date > CAST(:date AS DATE)
        AND left(a.inst_name,10) = :isymbmonth
    """)

    # Execute query with parameters
    with cnx.connect() as conn:
        # lines = pd.read_sql(query, conn, params={"date": str(d), "isymbmonth": isymbmonth})
        result = conn.execute(query, {"date": str(d), "isymbmonth": isymbmonth})
        df_data = result.fetchall()
        df_columns = result.keys()
        lines = pd.DataFrame(df_data, columns=df_columns)

    print(f"{len(lines)=} rows retrieved from getContract {isymbmonth}, {d}")
    return lines

def insert_strikeDG(itxn_id, ip, idelta, ig):
    from sqlalchemy import text

    x = 0
    sql = text("INSERT INTO option_daily_strikeDG(txn_id, strike, delta, gamma) VALUES (:txn_id, :strike, :delta, :gamma)")

    # Create parameter dictionary
    params = {
        "txn_id": itxn_id,
        "strike": ip,
        "delta": float(idelta),
        "gamma": float(ig)
    }

    try:
        # Use connection context manager
        with cnx.connect() as conn:
            conn.execute(sql, params)
            conn.commit()
            x = 1
    except Exception as e:
        print(itxn_id, ip, idelta, ig, str(e), flush=True)

    return x

# fetchHKEXReport moved to hkex_fetcher module

# parseHKEXReport moved to hkex_fetcher module

def saveHKEXDataToDatabase(processed_data, report_type='daily'):
    """Save processed data to database"""
    success_count = 0
    for item in processed_data:
        if insert_report(
            item['inst_name'], item.get('prev_date'),
            item.get('prev_open', 0), item.get('prev_high', 0), item.get('prev_low', 0),
            item.get('prev_close', 0), item.get('prev_volume', 0),
            item['txn_date'],
            item['curr_open'], item['curr_high'], item['curr_low'], item['curr_close'], item['curr_volume'],
            item['iv_percent'], item['open_interest'], item['oi_change'],
            item['stock_price'], item['delta'], item['gamma'], item.get('charm', 0)
        ) == 1:
            success_count += 1

    return success_count

def getDailyMarketReport(symb, trade_date):
    """Main function to orchestrate the process using new pipeline"""
    # Create pipeline instance (debug mode auto-detected from env)
    pipeline = HKEXPipeline(pathname, saveHKEXDataToDatabase)

    # Process using pipeline
    results = pipeline.process_daily_report(symb, trade_date)

    # Return summary counts for backward compatibility
    return results.get('summary_counts', [])
def ProcessStrikeDG(isymbmonth):
    # isymbmonth: 'HSI.OCT-20'
    inputs = isymbmonth.split('.')
    print("ProcessStrikeDG: ", inputs[0], inputs[1])

    from sqlalchemy import text

    # Check Last updated date + 1 using parameterized query
    query = text("""
        SELECT greatest((CURRENT_DATE - 60), max(txn_date))
        FROM option_daily_report a, option_daily_strikedg b
        WHERE a.txn_id = b.txn_id
        AND left(a.inst_name, 10) = :isymbmonth
    """)

    with cnx.connect() as conn:
        # q_result = pd.read_sql(query, conn, params={"isymbmonth": isymbmonth})
        result = conn.execute(query, {"isymbmonth": isymbmonth})
        df_data = result.fetchall()
        df_columns = result.keys()
        q_result = pd.DataFrame(df_data, columns=df_columns)

    print(f'{q_result=}')
    start_date = q_result.iloc[0,0]

    if start_date is None:
        start_date = dt.date.today() - dt.timedelta(weeks=1)
        print(f'Process StrikDG, Override start_date {start_date=}')
    lines = getContract( isymbmonth, start_date )
    # Limit Strike considered for Whatif
    min_strike=0.9*lines.stock_price.min()
    max_strike=1.1*lines.stock_price.max()
    all_strikes = sorted(set(lines[(lines.strike>= min_strike) & (lines.strike <= max_strike)].strike))
    # all_strikes = sorted(set(lines.strike))
    # cursor = cnx.connect()
    strikeDG_count=0
    for i, l in lines.iterrows():
        # Limit Process Scope for performance
        if l.strike < min_strike or l.strike > max_strike:
            continue
        if l.oi < 1:
            continue
        #=max(NETWORKDAYS(today(),A21, holidays!$B$1:$B$20),1)/247
        tx= getDay2Expiry( l.cont_month, l.txn_date)/247
        d= d1( l.strike, l.stock_price, l.iv,tx)
        #d1=(np.log(24387/25200)+tx*(0+(np.square(0.18)/2)))/(0.18*np.sqrt(tx))
        cdelta=norm.cdf(d)
        if  l.cp =='C':
            delta = cdelta
        else:
            delta = 1- cdelta
        #Gamma =(EXP(-1*POWER(d1,2)/2)/SQRT(2*PI()))/($E$5*$B$5*SQRT($B$21))
        g = gamma(d, l.stock_price, l.iv, tx)
        #print(i, l.iv, l.strike, d, delta, g)
        lines.loc[i, 'cdelta'] = cdelta
        lines.loc[i, 'gamma'] = g
        #cursor.execute('UPDATE option_daily_report SET stock_price ={}, delta ={}, gamma = {} WHERE txn_id = {}'.format(l.stock_price, delta, g, l.txn_id)  )
        # Whatif for all stock price, find minimum delta price point
        # Calc Delta/Gamma at all strikes where oi > 10
        print(l.txn_date, l.inst, l.cp)
        strikeDG_count=0
        for p in all_strikes:
                #Calc Option Delta when stock price = p
                d= d1( l.strike, p, l.iv, tx)
                cdelta=norm.cdf(d)
                if  l.cp =='C':
                    delta = cdelta
                else:
                    delta = 1 - cdelta
                g = gamma(d, p, l.iv, tx)
                #print(p, d, delta, g)
                if (l.oi * delta) < 1:
                    continue
                if insert_strikeDG(l.txn_id, p, delta, g) == 1:
                    strikeDG_count=  strikeDG_count + 1
        print(strikeDG_count, 'StrikeDG Rows')
    #cursor.close()
    return strikeDG_count

# WEEKLY OPTION
#getWeeklyContract('HSI.06-NOV-20',dt.date(2020, 11, 2) )
def getWeeklyContract(isymbmonth, d):
    from sqlalchemy import text

    # Use parameterized query with text()
    query = text("""
        SELECT
            a.txn_id, a.txn_date,
            substr(a.inst_name,1,3) as symb,
            substr(a.inst_name,5,9) as cont_month,
            TO_NUMBER(substr(a.inst_name,15,5),'999999') as strike,
            substr(a.inst_name,1,19) as inst,
            substr(a.inst_name,21,1) as cp,
            a.close, a.iv::decimal/100 as iv, a.oi,
            a.stock_price as stock_price
        FROM weekly_option_daily_report a
        WHERE a.txn_date > :date
        AND left(a.inst_name,13) = :isymbmonth
    """)

    # Execute query with parameters
    with cnx.connect() as conn:
        # lines = pd.read_sql(query, conn, params={"date": str(d), "isymbmonth": isymbmonth})
        result = conn.execute(query, {"date": str(d), "isymbmonth": isymbmonth})
        df_data = result.fetchall()
        df_columns = result.keys()
        lines = pd.DataFrame(df_data, columns=df_columns)

    print(f"{len(lines)=} rows retrieved from getWeeklyContract {isymbmonth}, {d}")
    return lines

#%%
# WEEKLY HSI HHI
def insert_weekly_report(iinst_name, ixtxn_date, ixopen, ixhigh, ixlow, ixclose, ixvolume, itxn_date, iopen, ihigh, ilow, iclose, ivolume, iiv, ioi, ioi_change, istock_price, idelta, ig ):
    # Use SQLAlchemy 2.0 style with text() for SQL statements
    from sqlalchemy import text

    x = 0
    sql = text("""
        INSERT INTO weekly_option_daily_report(
            inst_name, xtxn_date, xopen, xhigh, xlow, xclose, xvolume,
            txn_date, open, high, low, close, volume, iv, oi, oi_change,
            stock_price, delta, gamma
        ) VALUES (
            :inst_name, :xtxn_date, :xopen, :xhigh, :xlow, :xclose, :xvolume,
            :txn_date, :open, :high, :low, :close, :volume, :iv, :oi, :oi_change,
            :stock_price, :delta, :gamma
        )
    """)

    # Create parameter dictionary
    params = {
        "inst_name": iinst_name,
        "xtxn_date": ixtxn_date,
        "xopen": ixopen,
        "xhigh": ixhigh,
        "xlow": ixlow,
        "xclose": ixclose,
        "xvolume": ixvolume,
        "txn_date": itxn_date,
        "open": iopen,
        "high": ihigh,
        "low": ilow,
        "close": iclose,
        "volume": ivolume,
        "iv": int(iiv),
        "oi": int(ioi),
        "oi_change": int(ioi_change),
        "stock_price": round(float(istock_price), 2),
        "delta": float(idelta),
        "gamma": float(ig)
    }

    try:
        # Use connection context manager
        with cnx.connect() as conn:
            conn.execute(sql, params)
            conn.commit()
            x = 1
    except Exception as e:
        print(iinst_name, ixtxn_date, str(e), flush=True)

    return x

def insert_WOstrikeDG(itxn_id, ip, idelta, ig):
    from sqlalchemy import text

    x = 0
    sql = text("INSERT INTO weekly_option_daily_strikeDG(txn_id, strike, delta, gamma) VALUES (:txn_id, :strike, :delta, :gamma)")

    # Create parameter dictionary
    params = {
        "txn_id": itxn_id,
        "strike": ip,
        "delta": float(idelta),
        "gamma": float(ig)
    }

    try:
        # Use connection context manager
        with cnx.connect() as conn:
            conn.execute(sql, params)
            conn.commit()
            x = 1
    except Exception as e:
        print(itxn_id, ip, idelta, ig, str(e), flush=True)

    return x

def ProcessWOStrikeDG(isymbmonth):
    # isymbmonth: 'HSI.06-NOV-20'
    inputs = isymbmonth.split('.')
    print(inputs[0], inputs[1])

    from sqlalchemy import text

    # Check Last updated date + 1 using parameterized query
    query = text("""
        SELECT max(txn_date)
        FROM weekly_option_daily_report a, weekly_option_daily_strikedg b
        WHERE a.txn_id = b.txn_id
        AND left(a.inst_name, 13) = :isymbmonth
    """)

    with cnx.connect() as conn:
        # q_result = pd.read_sql(query, conn, params={"isymbmonth": isymbmonth})
        result = conn.execute(query, {"isymbmonth": isymbmonth})
        df_data = result.fetchall()
        df_columns = result.keys()
        q_result = pd.DataFrame(df_data, columns=df_columns)

    start_date = q_result.iloc[0,0]

    if start_date is None:
        start_date = dt.date.today() - dt.timedelta(weeks=1)
    lines = getWeeklyContract( isymbmonth, start_date )
    # Limit Strike considered for Whatif
    # min_strike=0.9*lines.stock_price.min()
    # max_strike=1.1*lines.stock_price.max()
    all_strikes = sorted(set(lines.strike)  )
    # cursor = cnx.connect()
    strikeDG_count=0
    for i, l in lines.iterrows():
        # if l.strike < min_strike or l.strike > max_strike:
        #     continue
        if l.oi < 1:
            continue
        #=max(NETWORKDAYS(today(),A21, holidays!$B$1:$B$20),1)/247
        tx= getWODay2Expiry( l.cont_month, l.txn_date)/247
        d= d1( l.strike, l.stock_price, l.iv,tx)
        #d1=(np.log(24387/25200)+tx*(0+(np.square(0.18)/2)))/(0.18*np.sqrt(tx))
        cdelta=norm.cdf(d)
        if  l.cp =='C':
            delta = cdelta
        else:
            delta = 1- cdelta
        #Gamma =(EXP(-1*POWER(start_date,2)/2)/SQRT(2*PI()))/($E$5*$B$5*SQRT($B$21))
        g = gamma(d, l.stock_price, l.iv, tx)
        #print(i, l.iv, l.strike, d, delta, g)
        lines.loc[i, 'cdelta'] = cdelta
        lines.loc[i, 'gamma'] = g
        #cursor.execute('UPDATE option_daily_report SET stock_price ={}, delta ={}, gamma = {} WHERE txn_id = {}'.format(l.stock_price, delta, g, l.txn_id)  )
        # Whatif for all stock price, find minimum delta price point
        # Calc Delta/Gamma at all strikes where oi > 10
        print(l.txn_date, l.inst, l.cp)
        strikeDG_count=0
        for p in all_strikes:
                #Calc Option Delta when stock price = p
                d= d1( l.strike, p, l.iv, tx)
                cdelta=norm.cdf(d)
                if  l.cp =='C':
                    delta = cdelta
                else:
                    delta = 1 - cdelta
                g = gamma(d, p, l.iv, tx)
                #print(p, d, delta, g)
                if (l.oi * delta) < 1:
                    continue
                if insert_WOstrikeDG(l.txn_id, p, delta, g) == 1:
                    strikeDG_count=  strikeDG_count + 1
        print(strikeDG_count, 'StrikeDG Rows')
    #cursor.close()
    return strikeDG_count

# fetchHKEXWeeklyReport and parseHKEXWeeklyReport moved to hkex_fetcher module

def saveHKEXWeeklyDataToDatabase(processed_data, report_type='weekly'):
    """Save processed weekly option data to database"""
    success_count = 0
    for item in processed_data:
        if insert_weekly_report(
            item['inst_name'], item.get('prev_date'),
            item.get('prev_open', 0), item.get('prev_high', 0), item.get('prev_low', 0),
            item.get('prev_close', 0), item.get('prev_volume', 0),
            item['txn_date'],
            item['curr_open'], item['curr_high'], item['curr_low'], item['curr_close'], item['curr_volume'],
            item['iv_percent'], item['open_interest'], item['oi_change'],
            item['stock_price'], item['delta'], item['gamma']
        ) == 1:
            success_count += 1

    return success_count

def getDailyWOReport(symb, trade_date):
    """Main function to orchestrate the weekly option report processing using new pipeline"""
    # Create pipeline instance (debug mode auto-detected from env)
    pipeline = HKEXPipeline(pathname, saveHKEXWeeklyDataToDatabase)

    # Process using pipeline
    results = pipeline.process_weekly_report(symb, trade_date)

    # Return summary counts for backward compatibility
    return results.get('summary_counts', [])

def getDailyHTIReport(symb, trade_date):
    """HTI report processing using the new pipeline"""
    # Create pipeline instance with HTI-specific save function
    def save_hti_data(processed_data, report_type):
        count = 0
        for record in processed_data:
            if insert_report(
                record['inst_name'], None, None, None, None, None, None,
                record['txn_date'], record['curr_open'], record['curr_high'],
                record['curr_low'], record['curr_close'], record['curr_volume'],
                record['iv_percent'], record['open_interest'], record['oi_change'],
                record['stock_price'], record['delta'], record['gamma'], record.get('charm', 0)
            ) == 1:
                count += 1
        return count

    pipeline = HKEXPipeline(pathname, save_hti_data)

    # Process using pipeline
    results = pipeline.process_hti_report(symb, trade_date)

    # Return summary counts for backward compatibility
    return results.get('summary_counts', [])


# %%
def updateIndexDeltaAllStrikes( t_date ):
    # Create SQL Engine
    #cnx = create_cnx()
    #t_date =pd.read_sql('SELECT max(txn_date)  FROM stock_option_report', cnx)
    # i_date = t_date.strftime("%Y-%m-%d")
    i_date = t_date # t_date is now expected to be a pre-formatted string
    print(f"updateIndexDeltaAllStrikes: {i_date=}")
    from sqlalchemy import text

    # Delete t_index_delta_all_strikes
    delete_query = text("DELETE FROM t_index_delta_all_strikes WHERE txn_date = :date")
    with cnx.connect() as conn:
        conn.execute(delete_query, {"date": i_date})
        conn.commit()
    print(f"Deleted records for date: {i_date}")

    # Read All Strikes Delta
    query = text("""
        SELECT substr(a.inst_name,1,10) AS cmonth,
            a.txn_date AS txn_date, b.strike AS strike,
            sum(((case when substr(a.inst_name,18,1) = 'C' then 1 else 0 end) * a.oi) * b.delta) AS call_delta,
            sum(((CASE WHEN substr(a.inst_name,18,1) = 'P' THEN 1 ELSE 0 END) * a.oi) * b.delta) AS put_delta,
            sum((a.oi * b.delta)) AS abs_delta,
            sum((a.oi * b.gamma)) AS total_gamma
        FROM option_daily_report a, option_daily_strikedg b
        WHERE (a.txn_id = b.txn_id) AND b.delta > 0
        AND a.txn_date = CAST(:date AS DATE)
        GROUP BY substr(a.inst_name,1,10), a.txn_date, b.strike
    """)
    # Execute the query with parameters
    with cnx.connect() as conn:
        # df = pd.read_sql(query, conn, params={"date": i_date})
        result = conn.execute(query, {"date": i_date})
        df_data = result.fetchall()
        df_columns = result.keys()
        df = pd.DataFrame(df_data, columns=df_columns)

    print(f"Retrieved {len(df)} rows of delta data")

    # Insert t_index_delta_all_strikes
    try:
        print(df)
        # Use SQLAlchemy 2.0 style with connection context manager
        with cnx.connect() as conn:
            df.to_sql(name='t_index_delta_all_strikes', con=conn, if_exists='append', index=False)
            conn.commit()
    except Exception as e:
        print(str(e), flush=True)
        print(df)

    # Check Results
    check_query = text("SELECT cmonth, count(*) FROM t_index_delta_all_strikes WHERE txn_date = :date GROUP BY cmonth")
    with cnx.connect() as conn:
        # result_df = pd.read_sql(check_query, conn, params={\"date\": t_date})
        result = conn.execute(check_query, {"date": i_date}) # Pass date as string (assuming i_date is already the string)
        df_data = result.fetchall()
        df_columns = result.keys()
        result_df = pd.DataFrame(df_data, columns=df_columns)
        print("Results summary:")
        print(result_df)


# %%
def updateWeeklyDeltaAllStrikes(t_date):
    from sqlalchemy import text

    # Create SQL Engine
    i_date = t_date # t_date is now expected to be a pre-formatted string
    print(f"updateWeeklyDeltaAllStrikes: {i_date=}")

    # Delete t_weekly_delta_all_strikes
    delete_query = text("DELETE FROM t_weekly_delta_all_strikes WHERE txn_date = :date")
    with cnx.connect() as conn:
        conn.execute(delete_query, {"date": i_date})
        conn.commit()
    print(f"Deleted weekly delta records for date: {i_date}")

    # Read All Strikes Delta
    # 23-04-13 Corrected wrong substr Call/Put from 18=>21
    query = text("""
        SELECT substr(a.inst_name,1,13) AS cmonth,
            a.txn_date AS txn_date, b.strike AS strike,
            sum(((case when substr(a.inst_name,21,1) = 'C' then 1 else 0 end) * a.oi) * b.delta) AS call_delta,
            sum(((CASE WHEN substr(a.inst_name,21,1) = 'P' THEN 1 ELSE 0 END) * a.oi) * b.delta) AS put_delta,
            sum((a.oi * b.delta)) AS abs_delta,
            sum((a.oi * b.gamma)) AS total_gamma
        FROM weekly_option_daily_report a, weekly_option_daily_strikedg b
        WHERE (a.txn_id = b.txn_id) AND b.delta > 0
        AND a.txn_date = CAST(:date AS DATE)
        GROUP BY substr(a.inst_name,1,13), a.txn_date, b.strike
    """)

    # Execute the query with parameters
    with cnx.connect() as conn:
        # df = pd.read_sql(query, conn, params={"date": i_date})
        result = conn.execute(query, {"date": i_date})
        df_data = result.fetchall()
        df_columns = result.keys()
        df = pd.DataFrame(df_data, columns=df_columns)

    print(f"Retrieved {len(df)} rows of weekly delta data")

    # Insert into t_weekly_delta_all_strikes
    try:
        print(df)
        # Use SQLAlchemy 2.0 style with connection context manager
        with cnx.connect() as conn:
            df.to_sql(name='t_weekly_delta_all_strikes', con=conn, if_exists='append', index=False)
            conn.commit()
    except Exception as e:
        print(str(e), flush=True)
        print(df)

    # Check Results
    check_query = text("SELECT cmonth, count(*) FROM t_weekly_delta_all_strikes WHERE txn_date = :date GROUP BY cmonth")
    with cnx.connect() as conn:
        # result_df = pd.read_sql(check_query, conn, params={\"date\": t_date})
        result = conn.execute(check_query, {"date": i_date}) # Pass date as string (assuming i_date is already the string)
        df_data = result.fetchall()
        df_columns = result.keys()
        result_df = pd.DataFrame(df_data, columns=df_columns)
        print("Weekly results summary:")
        print(result_df)

# %% [markdown]
def refreshMaterializedViews():
    for view_name in ['index_daily_iv']:
        try:
            from sqlalchemy import text
            sql = text(f'REFRESH MATERIALIZED VIEW CONCURRENTLY {view_name}')
            with cnx.connect() as conn:
                conn.execute(sql)
                conn.commit()
            print(f"Successfully refreshed materialized view {view_name}@", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        except Exception as e:
            print(f"Error refreshing materialized view {view_name}: {str(e)} @", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
# %%
def getHistReport(symb, start_date, end_date):
    """Historical report fetching using the new pipeline"""
    # Create pipeline instance (debug mode auto-detected from env)
    pipeline = HKEXPipeline(pathname, saveHKEXDataToDatabase)

    # Process historical reports
    results = pipeline.process_historical_reports(symb, start_date, end_date)

    return results


# %%
# weekmask = 'Mon Tue Wed Thu Fri'
# exchange_holidays = [dt.date(2024, 4, 4), dt.date(2024, 4, 1),dt.date(2024, 3, 29)]
# Calc total trading days in a year around 246-247
# pd.bdate_range('2023/01/01','2023/12/31',
#                freq='C',
#                weekmask = weekmask,
#                holidays=exchange_holidays)
# %%

#%%
# exchange_holidays=[]
# get_exchange_holidays_from_file( 'hkex_holidays.xml')
# %%
# Patch Adjusted IV
#getContract('HSI.OCT-20',dt.date(2020, 10, 1) )
def getDailyIndexOptionsRows(d):
    from sqlalchemy import text

    # Use parameterized query with text()
    query = text("""
        SELECT
            a.txn_id, a.txn_date,
            substr(a.inst_name,1,3) as symb,
            substr(a.inst_name,5,6) as cont_month,
            to_number(substr(a.inst_name,12,5),'999999') as strike,
            substr(a.inst_name,1,16) as inst,
            substr(a.inst_name,18,1) as cp,
            a.close, a.iv::decimal/100 as iv, a.oi,
            a.stock_price as stock_price
        FROM option_daily_report a
        WHERE a.txn_date = CAST(:date AS DATE)
    """)

    # Execute query with parameters
    with cnx.connect() as conn:
        # lines = pd.read_sql(query, conn, params={"date": str(d)})
        result = conn.execute(query, {"date": str(d)})
        df_data = result.fetchall()
        df_columns = result.keys()
        lines = pd.DataFrame(df_data, columns=df_columns)

    print(f"{len(lines)=} rows retrieved from getDailyIndexOptionsRows {d}")
    return lines
# %%
def getDailyWeeklyOptionsRows(d):
    from sqlalchemy import text

    # Use parameterized query with text()
    query = text("""
        SELECT
            a.txn_id, a.txn_date,
            substr(a.inst_name,1,3) as symb,
            substr(a.inst_name,5,9) as cont_month,
            TO_NUMBER(substr(a.inst_name,15,5),'999999') as strike,
            substr(a.inst_name,1,19) as inst,
            substr(a.inst_name,21,1) as cp,
            a.close, a.iv::decimal/100 as iv, a.oi,
            a.stock_price as stock_price
        FROM weekly_option_daily_report a
        WHERE a.txn_date = CAST(:date AS DATE)
    """)

    # Execute query with parameters
    with cnx.connect() as conn:
        # lines = pd.read_sql(query, conn, params={"date": str(d)})
        result = conn.execute(query, {"date": str(d)})
        df_data = result.fetchall()
        df_columns = result.keys()
        lines = pd.DataFrame(df_data, columns=df_columns)

    print(f"{len(lines)=} rows retrieved from getDailyWeeklyOptionsRows {d}")
    return lines

# %%
def updateWeeklyIndexOptions():
# ## WEEKLY OPTIONS
    all_symb = ['HSI', 'HHI', 'HTI']
    # all_symb = ['HTI']

    total_weekly_insert_txn_count=[]
    print(f"WWWWWWWWWW updateWeeklyIndexOptions: {all_symb=} @", dt.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    #cnx = create_cnx()
    for each_symb in all_symb:
        # Check  Last updated date + 1
        q="SELECT max(txn_date)+1 FROM weekly_option_daily_report where left(inst_name, 3) ='{}'".format(each_symb)
        with cnx.connect() as conn:
            # q_result =  pd.DataFrame(conn.execute(q).fetchall())
            q_result = pd.read_sql(q, conn)
        start_date =q_result.iloc[0,0]
        if start_date == None:
            print(f"WWWW {each_symb} start_date = None: Override start_date {start_date}")
            start_date = dt.date.today()- dt.timedelta(weeks=1)
        else:
            print(f"WWWW {each_symb} start_date = {start_date}")

        # start_date = dt.date(2024, 11,12)
        # Use Hong Kong timezone for HKEX trading dates
        end_date = get_hkex_yesterday()
        # end_date = dt.date(2024, 11,25)

        # Fix: Handle case where database is already up-to-date
        if start_date > end_date:
            print(f"WWWW {each_symb} already up-to-date: start_date ({start_date}) > end_date ({end_date})")
            continue

        bdaterange = pd.bdate_range(start_date, end_date)
        print(f"WWWW {each_symb} processing date range: {start_date} to {end_date} ({len(bdaterange)} business days)")

        for single_date in bdaterange:
            print("getDailyReport: " + each_symb + ' ' + single_date.strftime("%Y-%m-%d"))
            insert_day_count= getDailyWOReport( each_symb, single_date)
            #print(insert_day_count)
            total_weekly_insert_txn_count= total_weekly_insert_txn_count + insert_day_count
    print("WWWWWW total_weekly_insert_txn_count =>", pd.DataFrame(total_weekly_insert_txn_count))
    # Process Each Contract Month Delta/Gamma at each Strike
    if len(total_weekly_insert_txn_count) > 0:
        for symbmonth in list(set(pd.DataFrame(total_weekly_insert_txn_count)[0])):
            ProcessWOStrikeDG(symbmonth)
        # Process DeltaAllStrikes
        for single_date in bdaterange:
            print("updateWeeklyDeltaAllStrikes: " +  single_date.strftime("%Y-%m-%d"))
            updateWeeklyDeltaAllStrikes(single_date.strftime("%Y-%m-%d"))
        last_processed_date = dt.datetime.strftime(total_weekly_insert_txn_count[-1][1], '%Y-%m-%d')
        print(f"WWWWWWWWWW Finished last trading days : {last_processed_date=}\n")
        # start_date = dt.date.today() - dt.timedelta(days = 1)
        # updateDeltaAllStrikes(start_date)
        sql=f"SELECT txn_date, cmonth, count(*) FROM t_weekly_delta_all_strikes where txn_date >='{last_processed_date}' group by 1,2"
        print(sql)
        df = pd.read_sql(sql , cnx)
        print(df)
    else:
        print('WWWWWWWWWW No Weekly Transaction Days Processed\n')

# %%
def updateMonthlyIndexOptions():
    #  Update Day by Day Get Daily Report
    all_symb = ['HSI', 'HHI', 'MHI', 'HTI']
    # all_symb = ['HTI']

    total_insert_txn_count=[]
    #cnx = create_cnx()
    print (f"MMMMMMMMMMMMMMM updateMonthlyIndexOptions: {all_symb=} @", dt.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    for each_symb in all_symb:
        # Check  Last updated date + 1
        q="SELECT  greatest((CURRENT_DATE -30) , (max(txn_date)+1)) \
            FROM option_daily_report a, option_daily_strikedg b \
            where a.txn_id = b.txn_id and left(a.inst_name, 3) = '{}'".format(each_symb)
        with cnx.connect() as conn:
            # q_result =  pd.DataFrame(conn.execute(q).fetchall())
            q_result = pd.read_sql(q, conn)
        # q_result=pd.read_sql(q , cnx)
        start_date =q_result.iloc[0,0]
        if start_date == None:
            start_date = dt.date.today()- dt.timedelta(weeks=1)
            print(f"MMMM {each_symb} start_date = None: Override start_date {start_date}")
        else:
            start_date =q_result.iloc[0,0]
            print(f"MMMM {each_symb} start_date = {start_date}")
        # Use Hong Kong timezone for HKEX trading dates
        end_date = get_hkex_yesterday()
        # start_date = dt.date(2024, 11,12)
        # end_date = dt.date(2024, 11,25)

        # Fix: Handle case where database is already up-to-date
        if start_date > end_date:
            print(f"MMMM {each_symb} already up-to-date: start_date ({start_date}) > end_date ({end_date})")
            continue

        bdaterange = pd.bdate_range(start_date, end_date)
        print(f"MMMM {each_symb} processing date range: {start_date} to {end_date} ({len(bdaterange)} business days)")
        for single_date in bdaterange:
            print("getDailyReport: " + each_symb + ' ' + single_date.strftime("%Y-%m-%d"))
            # if each_symb == 'HTI' :
            #     insert_day_count= getDailyHTIReport( each_symb, single_date)
            # else:
            #     insert_day_count= getDailyMarketReport( each_symb, single_date)
            insert_day_count= getDailyMarketReport( each_symb, single_date)
            #print(insert_day_count)
            total_insert_txn_count= total_insert_txn_count + insert_day_count

    x=pd.DataFrame(total_insert_txn_count)
    print("MMMMMM total_monthly_insert_txn_count =>", pd.DataFrame(total_insert_txn_count))
    if len(x) > 0:
        # Process only next 3 month for speed
        print("ProcessStrikeDG@", dt.datetime.now())
        this_cmonth = dt.datetime.strftime(x[1].iloc[-1], '%b-%y').upper()
        next_cmonth = dt.datetime.strftime( MonthEnd().rollforward(x[1].iloc[-1])+dt.timedelta(1), '%b-%y').upper()
        M2_cmonth = dt.datetime.strftime( MonthEnd().rollforward(x[1].iloc[-1])+dt.timedelta(31), '%b-%y').upper()
        contract_list =set (x[(x[0].str.endswith(this_cmonth)) | (x[0].str.endswith(next_cmonth)) | (x[0].str.endswith(M2_cmonth))][0])
        print(f"{contract_list=}")
        if len(contract_list) > 0:
            for symbmonth in contract_list:
                ProcessStrikeDG(symbmonth)
            # Process DeltaAllStrikes
            for single_date in list(set(pd.DataFrame(total_insert_txn_count)[1])):
                print("updateIndexDeltaAllStrikes: " +  single_date.strftime("%Y-%m-%d") + "@" + dt.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
                updateIndexDeltaAllStrikes(single_date)

    #from Storacle import updatePrice, getPrice
    #updatePrice('HHI')
    #getPrice('HHI', '2021-02-19')
    # print(pd.DataFrame(total_insert_txn_count))
    if len(total_insert_txn_count)>0:
        last_processed_date = dt.datetime.strftime(total_insert_txn_count[-1][1], '%Y-%m-%d')
        print(f"MMMMMMMMMMMMMMM Finished last trading days : {last_processed_date=}\n")
        # start_date = dt.date.today() - dt.timedelta(days = 1)
        # updateDeltaAllStrikes(start_date)
        sql=f"SELECT txn_date, cmonth, count(*) FROM t_index_delta_all_strikes where txn_date >='{last_processed_date}' group by 1,2"
        print(sql)
        df = pd.read_sql(sql , cnx)
        print(df)
        refreshMaterializedViews()

    else:
        print('MMMMMMMMMMMMMMM No Monthly Transaction Days Processed\n')


# %%

def main():
    """Main function to handle command line execution"""
    import argparse
    import sys

    parser = argparse.ArgumentParser(description='Update HKEX Index Option data in PostgreSQL')
    parser.add_argument('--date', type=str, required=False,
                       help='Transaction date in YYYY-MM-DD format (default: today)')
    parser.add_argument('--dry-run', action='store_true',
                       help='Run in dry-run mode (preview only)')
    parser.add_argument('--symbols', type=str, nargs='*',
                       default=['HSI', 'HHI', 'MHI', 'HTI'],
                       help='Symbols to process (default: HSI HHI MHI HTI)')

    args = parser.parse_args()

    # If no date provided, use today's date
    if args.date is None:
        import datetime as dt
        args.date = dt.date.today().strftime('%Y-%m-%d')
        print(f"No date provided, using today's date: {args.date}")

    print(f"Starting UpdateIndexOptionPostgres.py")
    print(f"Date: {args.date}")
    print(f"Dry run: {args.dry_run}")
    print(f"Symbols: {args.symbols}")

    # Show timezone information
    hk_today = get_hkex_today()
    hk_yesterday = get_hkex_yesterday()
    print(f"Hong Kong today: {hk_today}")
    print(f"Hong Kong yesterday (processing target): {hk_yesterday}")

    # Check environment variables
    if not check_environment():
        print("Environment check failed. Please set required environment variables.")
        sys.exit(1)

    # Import debug mode check
    from hkex_fetcher import is_debug_mode

    # Test HKEX connection (only when debug mode is enabled)
    if is_debug_mode():
        print("🐛 DEBUG MODE: Running connection tests")
        if not test_hkex_connection():
            print("HKEX connection test failed. Check your internet connection.")
            print("Continuing anyway, but downloads may fail...")

        # Test specific report URL that was failing
        test_report_url = "https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/hsiwo250613.htm"
        print(f"\nTesting specific report URL that was timing out...")
        test_specific_report_url(test_report_url)
    else:
        print("⏭️  Skipping connection tests (debug mode disabled)")

    if args.dry_run:
        print("DRY RUN MODE - No data will be written to database")
        print("This would process the following:")
        print(f"- Symbols: {args.symbols}")
        print(f"- Date: {args.date}")
        print("- Fetch HKEX reports")
        print("- Parse option data")
        print("- Calculate Greeks (delta, gamma, charm)")
        print("- Update price data")
        print("- Process strike delta/gamma calculations")
        print("- Update materialized views")
        return

    try:
        updateWeeklyIndexOptions()
        updateMonthlyIndexOptions()
    except Exception as e:
        print(f"Error during processing: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
#%%
if __name__ == "__main__":
    print("Starting UpdateIndexOptionPostgres.py")
    main()

# %%
