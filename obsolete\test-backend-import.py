#!/usr/bin/env python3
"""
Test script to verify backend can start without <PERSON><PERSON>.
This helps isolate Docker-specific issues from application issues.
"""

import os
import sys
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_dir))

print("🔍 Testing Backend Import and Startup")
print("=" * 50)

try:
    print("1. Testing app.core.config import...")
    from app.core.config import settings
    print("   ✅ Config imported successfully")
    print(f"   📝 Environment: {settings.environment}")
    print(f"   📝 Debug mode: {settings.debug}")
    print(f"   📝 Database URL: {settings.database_url}")
    
    print("\n2. Testing main application import...")
    from app.main import app
    print("   ✅ Main app imported successfully")
    
    print("\n3. Testing FastAPI app creation...")
    print(f"   📝 App title: {app.title}")
    print(f"   📝 App version: {app.version}")
    
    print("\n4. Testing uvicorn import...")
    import uvicorn
    print("   ✅ Uvicorn imported successfully")
    
    print("\n🎉 All imports successful! Backend should be able to start.")
    print("\n💡 To start the backend locally (without <PERSON><PERSON>):")
    print(f"   cd {backend_dir}")
    print("   python -m uvicorn app.main:app --reload --host 127.0.0.1 --port 8000")
    
except ImportError as e:
    print(f"   ❌ Import error: {e}")
    print("\n🔧 Suggested fixes:")
    print("   1. Install requirements: pip install -r backend/requirements.txt")
    print("   2. Check Python path and working directory")
    print("   3. Verify all required modules exist")
    
except Exception as e:
    print(f"   ❌ Error: {e}")
    print("\n🔧 Check configuration and dependencies")

print("\n" + "=" * 50)
