## Debugging and UI Fixes (May 2025)

This series of fixes addressed runtime errors in Python scripts and UI display issues in the dashboard application.

**Key Issues Resolved:**

1.  **Python Script Errors (`UpdateIndexOptionPostgres.py`):**
    *   **SQLAlchemy `AttributeError: 'Engine' object has no attribute 'cursor'`**: Corrected by ensuring database operations (reads and writes) use an SQLAlchemy `Connection` object rather than the `Engine` directly. All instances of `pd.read_sql` and `pd.read_sql_query` were replaced with `connection.execute(text(...))` followed by manual DataFrame creation (`pd.DataFrame(result.fetchall(), columns=result.keys())`).
    *   **`yfinance` Data Fetching Error**: Adjusted date calculations in `updatePrice` to correctly fetch recent price data, resolving an issue where `yfinance` reported "possibly delisted; no price data found." This involved changing `timedelta(days=2)` to `timedelta(days=1)` and `last_date.iloc[0,0] == None` to `pd.isna(last_date.iloc[0,0])`.
    *   Ensured `from sqlalchemy import text` was added where necessary.

2.  **Backend Orchestrator Python Environment (`simple_orchestrator.py`):**
    *   Diagnosed and fixed an issue where the backend orchestrator was using the global Python interpreter instead of the project's virtual environment (`env`).
    *   Modified `simple_orchestrator.py` to explicitly use the Python executable from `env/Scripts/python.exe` when launching scripts.

3.  **Frontend UI Issues:**
    *   **`RealTimeLogViewer.tsx` - `logs.map is not a function`**: Corrected an issue where the component was trying to access `data.lines` instead of `data.logs` from the API response.
    *   **`ProcessMonitor.tsx` & `RealTimeLogViewer.tsx` - Log Viewer Text Color**: Resolved a "black on black" text rendering issue in the log viewer. This involved:
        *   Initially attempting global CSS overrides with `!important`.
        *   Switching to direct `sx` prop styling on the MUI `Box` component used for log display.
        *   Ensuring explicit `backgroundColor` and `color` were set for the log box and its placeholder text.
        *   Commenting out potentially conflicting global CSS rules in `index.css`.
        *   Correcting a TypeScript error in `ProcessMonitor.tsx` related to `TypographyProps` by moving `wordBreak` and `whiteSpace` into the `sx` prop.
        *   Fixing CSS syntax errors in `index.css` related to commenting.

**Files Modified:**

*   `UpdateIndexOptionPostgres.py`
*   `dashboard/backend/app/services/simple_orchestrator.py`
*   `dashboard/frontend/src/components/RealTimeLogViewer.tsx`
*   `dashboard/frontend/src/components/ProcessMonitor.tsx`
*   `dashboard/frontend/src/index.css`

**Debugging Files Removed:**

*   `temp.py` (attempted removal)
*   `env_check.py` (removed previously)
