import asyncio
import sys

print("Testing basic asyncio on Windows...")
print(f"Platform: {sys.platform}")

# Test the Windows event loop policy
if sys.platform == 'win32':
    try:
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        print("✅ Successfully set ProactorEventLoop policy")
    except Exception as e:
        print(f"❌ Failed to set ProactorEventLoop policy: {e}")

# Test subprocess creation
async def test_subprocess():
    try:
        process = await asyncio.create_subprocess_exec(
            'python', '--version',
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        stdout, stderr = await process.communicate()
        print(f"✅ Subprocess test successful!")
        print(f"Output: {stdout.decode().strip()}")
        return True
    except Exception as e:
        print(f"❌ Subprocess test failed: {e}")
        return False

# Run the test
print("Running subprocess test...")
result = asyncio.run(test_subprocess())
print(f"Test result: {'PASSED' if result else 'FAILED'}")
