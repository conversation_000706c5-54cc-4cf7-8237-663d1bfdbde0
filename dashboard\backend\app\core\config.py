import os
from typing import List, Optional
from pydantic import validator
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    # Environment
    environment: str = "development"
    debug: bool = True
    
    
    # Database
    postgres_host: str = "localhost"
    postgres_port: int = 5432
    postgres_db: str = "hkex_options"
    postgres_user: str = "postgres"
    postgres_password: str = "postgres"
    database_url: Optional[str] = None
    
    @validator("database_url", pre=True)
    def assemble_db_connection(cls, v: Optional[str], values: dict) -> str:
        if isinstance(v, str):
            return v
        return f"postgresql://{values.get('postgres_user')}:{values.get('postgres_password')}@{values.get('postgres_host')}:{values.get('postgres_port')}/{values.get('postgres_db')}"
    
    # Redis
    redis_host: str = "localhost"
    redis_port: int = 6379
    redis_url: Optional[str] = None
    
    @validator("redis_url", pre=True)
    def assemble_redis_connection(cls, v: Optional[str], values: dict) -> str:
        if isinstance(v, str):
            return v
        return f"redis://{values.get('redis_host')}:{values.get('redis_port')}/0"
    
    # Security
    secret_key: str = "your-secret-key-change-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    refresh_token_expire_minutes: int = 10080  # 7 days
    
    # API
    api_v1_str: str = "/api/v1"
    project_name: str = "HKEX Dashboard"
    
    # CORS
    backend_cors_origins: List[str] = ["http://localhost:3000", "http://localhost:8000"]
    
    @validator("backend_cors_origins", pre=True)
    def assemble_cors_origins(cls, v) -> List[str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
      # Celery
    celery_broker_url: Optional[str] = None
    celery_result_backend: Optional[str] = None
    
    @validator("celery_broker_url", pre=True)
    def assemble_celery_broker(cls, v: Optional[str], values: dict) -> str:
        if isinstance(v, str):
            return v
        redis_url = values.get("redis_url") 
        if redis_url:
            # If redis_url already exists, replace the database number with 1
            if redis_url.endswith('/0'):
                return redis_url[:-1] + '1'
            else:
                return f"{redis_url}/1"
        else:
            return f"redis://{values.get('redis_host')}:{values.get('redis_port')}/1"
    
    @validator("celery_result_backend", pre=True)
    def assemble_celery_backend(cls, v: Optional[str], values: dict) -> str:
        if isinstance(v, str):
            return v
        redis_url = values.get("redis_url")
        if redis_url:
            # If redis_url already exists, replace the database number with 1
            if redis_url.endswith('/0'):
                return redis_url[:-1] + '1'
            else:
                return f"{redis_url}/1"
        else:
            return f"redis://{values.get('redis_host')}:{values.get('redis_port')}/1"
    
    # Processing scripts configuration (DEPRECATED - now using dynamic JSON config)
    workspace_root: str = "../"
    # script_update_index_options: str = "UpdateIndexOptionPostgres.py"
    # script_update_stock_options: str = "UpdateStockOptionReportPostgres.py"
    # script_copy_view_multidb: str = "copyViewMultiDB.py"
    
    # Monitoring
    health_check_interval: int = 30
    table_metrics_cache_ttl: int = 300
    process_timeout: int = 3600
    
    # Logging
    log_level: str = "INFO"
    log_file: str = "logs/dashboard.log"
      # WebSocket
    ws_heartbeat_interval: int = 30
    ws_max_connections: int = 100
    
    # Backend
    backend_port: int = int(os.getenv("BACKEND_PORT", 8000))
    
    class Config:
        env_file = ".env"
        case_sensitive = False
        extra = "ignore"  # Ignore extra fields from .env file

settings = Settings()
