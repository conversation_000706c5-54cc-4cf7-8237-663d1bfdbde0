"""
Test Firecrawl Fallback Specifically

This script tests the Firecrawl fallback functionality by forcing the direct HTTP to fail.
"""

import os
import sys
import datetime as dt
from pathlib import Path

# Add the scripts directory to Python path
script_dir = Path(__file__).parent
sys.path.append(str(script_dir))

# Set required environment variables for testing
os.environ['out_path'] = str(script_dir / 'output') + '/'
os.environ['LOG_LEVEL'] = '30'  # WARNING level
os.environ['SQL_ECHO'] = '0'
os.environ['WILL9700_DB'] = 'postgresql://dummy:dummy@localhost/dummy'  # Dummy DB for testing

# Import the functions
from firecrawl_fetcher import fetch_with_firecrawl, FIRECRAWL_AVAILABLE

def test_firecrawl_only():
    """Test Firecrawl directly without HTTP fallback"""
    print("🔥 Testing Firecrawl API Directly")
    print("=" * 50)
    
    if not FIRECRAWL_AVAILABLE:
        print("❌ Firecrawl not available")
        return False
    
    # Test with a known working URL
    test_url = "https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/hsiwo250613.htm"
    print(f"Testing URL: {test_url}")
    
    try:
        response = fetch_with_firecrawl(test_url)
        if response and response.status_code == 200:
            print(f"✅ Firecrawl succeeded")
            print(f"📏 Content length: {len(response.content)} bytes")
            
            # Check content
            content_str = response.text.lower()
            if any(keyword in content_str for keyword in ['hkex', 'hong kong exchange', 'option']):
                print("✅ Content appears valid")
                return True
            else:
                print("⚠️  Content may not be valid")
                return False
        else:
            print(f"❌ Firecrawl failed")
            if response:
                print(f"Status code: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Firecrawl error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_problematic_url_with_firecrawl():
    """Test the problematic URL that was timing out"""
    print("\n🎯 Testing Problematic URL with Firecrawl")
    print("=" * 50)
    
    # The URL that was originally failing
    today = dt.date.today()
    t = ('dqe' + today.strftime("%y%m%d")).lower()
    problem_url = f'https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/{t}.htm'
    
    print(f"URL: {problem_url}")
    print(f"Date: {today}")
    
    try:
        response = fetch_with_firecrawl(problem_url)
        if response and response.status_code == 200:
            print(f"✅ Firecrawl succeeded for problematic URL!")
            print(f"📏 Content length: {len(response.content)} bytes")
            
            # Check if it looks like a valid report
            content_str = response.text.lower()
            if any(keyword in content_str for keyword in ['stock option', 'hkex', 'hong kong exchange', 'daily market report']):
                print(f"✅ Content appears to be a valid HKEX report")
            else:
                print(f"⚠️  Content may not be a valid HKEX report")
                # Show first few lines for debugging
                lines = response.text.split('\n')[:5]
                print("First 5 lines:")
                for i, line in enumerate(lines, 1):
                    print(f"{i}: {line[:100]}")
            
            return True
        else:
            print(f"❌ Firecrawl failed for problematic URL")
            if response:
                print(f"Status code: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error testing problematic URL: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fallback_integration():
    """Test the integration with forced failure"""
    print("\n🔧 Testing Fallback Integration")
    print("=" * 50)
    
    try:
        from UpdateStockOptionReportPostgres import safe_http_get_with_firecrawl_fallback
        
        # Test with a URL that might timeout
        test_url = "https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/dqe250616.htm"
        print(f"Testing fallback integration with: {test_url}")
        
        # Use very short timeout to force failure of direct method
        response = safe_http_get_with_firecrawl_fallback(test_url, timeout=5, max_retries=1, delay_between_retries=1)
        
        if response and response.status_code == 200:
            print(f"✅ Fallback integration succeeded")
            print(f"📏 Content length: {len(response.content)} bytes")
            return True
        else:
            print(f"❌ Fallback integration failed")
            return False
            
    except Exception as e:
        print(f"❌ Integration test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🧪 Firecrawl Fallback Test Suite")
    print("=" * 60)
    
    print(f"🔥 Firecrawl Available: {'YES' if FIRECRAWL_AVAILABLE else 'NO'}")
    
    if not FIRECRAWL_AVAILABLE:
        print("❌ Firecrawl not available. Install with: pip install firecrawl-py")
        return
    
    # Run tests
    test1_result = test_firecrawl_only()
    test2_result = test_problematic_url_with_firecrawl()
    test3_result = test_fallback_integration()
    
    # Summary
    print("\n" + "=" * 60)
    print("🏁 FINAL RESULTS")
    print("=" * 60)
    
    tests = [
        ("Firecrawl Direct", test1_result),
        ("Problematic URL", test2_result),
        ("Fallback Integration", test3_result)
    ]
    
    passed = sum(1 for _, result in tests if result)
    total = len(tests)
    
    for test_name, result in tests:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<25}: {status}")
    
    print(f"\n📊 Overall: {passed}/{total} tests passed")
    
    if passed >= 2:  # At least Firecrawl direct and one other test
        print("\n🎉 FIRECRAWL FALLBACK IS WORKING!")
        print("✅ The fallback system should resolve your timeout issues")
        print("\n💡 To use in your main script:")
        print("   1. Make sure your environment variables are set")
        print("   2. Run your UpdateStockOptionReportPostgres.py script")
        print("   3. It will automatically fall back to Firecrawl when needed")
    else:
        print("\n⚠️  SOME ISSUES DETECTED")
        print("💡 Troubleshooting:")
        print("   - Check your internet connection")
        print("   - Verify Firecrawl API key is working")
        print("   - Check Firecrawl service status")

if __name__ == "__main__":
    main()
