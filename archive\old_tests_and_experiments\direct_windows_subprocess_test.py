#!/usr/bin/env python3
"""
Direct test of Windows asyncio subprocess creation
"""
import asyncio
import subprocess
import sys
import os
from pathlib import Path

# Windows event loop fix - same as in our orchestrator
if sys.platform == 'win32':
    try:
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        print("✅ Set Windows ProactorEventLoop policy")
    except Exception as e:
        print(f"❌ Could not set policy: {e}")

async def test_windows_subprocess():
    """Test Windows subprocess creation with our fixes"""
    print(f"=== Testing Windows Subprocess Creation ===")
    print(f"Platform: {sys.platform}")
    
    # Check current event loop
    try:
        current_loop = asyncio.get_running_loop()
        print(f"Current event loop: {type(current_loop)}")
        if sys.platform == 'win32':
            if isinstance(current_loop, asyncio.ProactorEventLoop):
                print("✅ Using ProactorEventLoop (good for Windows subprocess)")
            else:
                print(f"⚠️ Using {type(current_loop)} (may cause subprocess issues)")
    except Exception as e:
        print(f"❌ Could not check event loop: {e}")
    
    # Test command (simple Python script)
    test_script = Path("o:/Github/MaxPain/MaxPain2024/UpdateIndexOptionPostgres.py")
    if not test_script.exists():
        print(f"❌ Test script not found: {test_script}")
        return
    
    cmd_args = ['python', str(test_script), '--help']
    print(f"Testing command: {' '.join(cmd_args)}")
    
    # Test 1: Standard subprocess creation
    try:
        print("\n1. Testing standard subprocess creation...")
        if sys.platform == 'win32':
            process = await asyncio.create_subprocess_exec(
                *cmd_args,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=test_script.parent,
                creationflags=subprocess.CREATE_NO_WINDOW
            )
        else:
            process = await asyncio.create_subprocess_exec(
                *cmd_args,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=test_script.parent
            )
        
        stdout, stderr = await process.communicate()
        print("✅ Standard subprocess creation successful!")
        print(f"Return code: {process.returncode}")
        if stdout:
            print(f"Output preview: {stdout.decode()[:200]}...")
        return True
        
    except Exception as e:
        print(f"❌ Standard subprocess creation failed: {e}")
        
        # Test 2: Shell-based fallback
        try:
            print("\n2. Testing shell-based fallback...")
            shell_cmd = ' '.join(f'"{arg}"' if ' ' in arg else arg for arg in cmd_args)
            process = await asyncio.create_subprocess_shell(
                shell_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=test_script.parent
            )
            
            stdout, stderr = await process.communicate()
            print("✅ Shell-based subprocess creation successful!")
            print(f"Return code: {process.returncode}")
            return True
            
        except Exception as shell_error:
            print(f"❌ Shell-based subprocess also failed: {shell_error}")
            return False

if __name__ == "__main__":
    print("Testing Windows subprocess fixes...")
    try:
        result = asyncio.run(test_windows_subprocess())
        if result:
            print("\n🎉 SUCCESS: Windows subprocess creation is working!")
        else:
            print("\n💥 FAILURE: Windows subprocess creation still failing")
    except Exception as e:
        print(f"\n💥 Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
