#!/usr/bin/env python3
"""
Simple test of the orchestrator functionality
"""

import asyncio
import os
import sys

# Set Windows event loop policy
if os.name == 'nt':
    asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

# Add the backend app to the path
backend_path = r"O:\Github\MaxPain\MaxPain2024\dashboard\backend\app"
sys.path.insert(0, backend_path)

async def test_orchestrator():
    print("Testing orchestrator initialization...")
    
    try:
        from app.services.simple_orchestrator import SimpleOrchestrator
        orchestrator = SimpleOrchestrator()
        
        print("✓ Orchestrator created successfully")
        
        # Test listing processes
        processes = orchestrator.list_active_processes()
        print(f"✓ Active processes: {len(processes)}")
        
        # Test process configs
        print(f"✓ Available process types: {list(orchestrator.process_configs.keys())}")
        
        # Test starting a process (with dry run)
        print("\nTesting process execution...")
        task_id = await orchestrator.start_process('update_index_options', {
            'txn_date': '2024-12-20', 
            'dry_run': True
        })
        
        print(f"✓ Process started with task ID: {task_id}")
        
        # Wait a few seconds and check status
        await asyncio.sleep(3)
        
        processes = orchestrator.list_active_processes()
        if task_id in processes:
            process_info = processes[task_id]
            print(f"✓ Process status: {process_info['status']}")
            print(f"✓ Process progress: {process_info['progress']}%")
            print(f"✓ Process message: {process_info['message']}")
            
            # Check logs
            log_tail = await orchestrator.get_log_tail(task_id, 5)
            print(f"✓ Log tail available: {len(log_tail)} lines")
            if log_tail:
                print("Recent log lines:")
                for line in log_tail[-3:]:
                    print(f"  {line}")
        else:
            print("⚠ Process not found in active list")
        
        print("\n✓ All tests passed!")
        
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_orchestrator())
