"""
Test Script for Firecrawl HKEX Fetcher

This script thoroughly tests the Firecrawl API integration for fetching HKEX reports.
"""

import sys
import os
import datetime as dt
from pathlib import Path

# Add the scripts directory to Python path
script_dir = Path(__file__).parent
sys.path.append(str(script_dir))

# Import our Firecrawl functions
from firecrawl_fetcher import (
    fetch_with_firecrawl,
    test_firecrawl_connection,
    fetch_hkex_stock_option_report,
    fetch_hkex_index_option_report,
    FIRECRAWL_AVAILABLE
)

def test_firecrawl_comprehensive():
    """Comprehensive test of Firecrawl functionality"""
    print("🔥 Comprehensive Firecrawl Test Suite")
    print("=" * 60)
    
    # Check prerequisites
    if not FIRECRAWL_AVAILABLE:
        print("❌ Firecrawl library not installed")
        print("💡 Install with: pip install firecrawl-py")
        return False
    
    # Get API key
    api_key = os.getenv('FIRECRAWL_API_KEY', 'fc-f514d4245edf41438797e1733226896c')
    print(f"🔑 Using API key: {api_key[:15]}...")
    
    test_results = []
    
    # Test 1: Basic connection
    print("\n" + "="*50)
    print("TEST 1: Basic Connection Test")
    print("="*50)
    
    try:
        result = test_firecrawl_connection(api_key)
        test_results.append(("Basic Connection", result))
        if result:
            print("✅ PASS: Basic connection successful")
        else:
            print("❌ FAIL: Basic connection failed")
    except Exception as e:
        print(f"❌ FAIL: Basic connection error: {e}")
        test_results.append(("Basic Connection", False))
    
    # Test 2: Sample HKEX report (from your example)
    print("\n" + "="*50)
    print("TEST 2: Sample HKEX Report")
    print("="*50)
    
    sample_url = "https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/hsiwo250613.htm"
    print(f"🔗 Testing URL: {sample_url}")
    
    try:
        response = fetch_with_firecrawl(sample_url, api_key)
        if response and response.status_code == 200:
            print("✅ PASS: Sample report fetched successfully")
            print(f"📏 Content length: {len(response.content)} bytes")
            
            # Validate content
            content_str = response.text.lower()
            if any(keyword in content_str for keyword in ['hkex', 'hong kong exchange', 'option', 'hsi']):
                print("✅ PASS: Content validation successful")
                test_results.append(("Sample Report", True))
            else:
                print("⚠️  WARNING: Content may not be valid HKEX report")
                test_results.append(("Sample Report", False))
        else:
            print("❌ FAIL: Sample report fetch failed")
            test_results.append(("Sample Report", False))
    except Exception as e:
        print(f"❌ FAIL: Sample report error: {e}")
        test_results.append(("Sample Report", False))
    
    # Test 3: Stock Option Report (today)
    print("\n" + "="*50)
    print("TEST 3: Stock Option Report (Today)")
    print("="*50)
    
    today = dt.date.today()
    print(f"📅 Testing date: {today}")
    
    try:
        response = fetch_hkex_stock_option_report(today, api_key)
        if response and response.status_code == 200:
            print("✅ PASS: Today's stock option report fetched")
            print(f"📏 Content length: {len(response.content)} bytes")
            test_results.append(("Stock Option Today", True))
        else:
            print("⚠️  INFO: Today's report not available (normal if market closed)")
            test_results.append(("Stock Option Today", "N/A"))
    except Exception as e:
        print(f"❌ FAIL: Stock option report error: {e}")
        test_results.append(("Stock Option Today", False))
    
    # Test 4: Stock Option Report (recent business day)
    print("\n" + "="*50)
    print("TEST 4: Stock Option Report (Recent Business Day)")
    print("="*50)
    
    # Try last few business days
    for days_back in range(1, 8):
        test_date = today - dt.timedelta(days=days_back)
        
        # Skip weekends
        if test_date.weekday() >= 5:  # Saturday = 5, Sunday = 6
            continue
            
        print(f"📅 Testing date: {test_date}")
        
        try:
            response = fetch_hkex_stock_option_report(test_date, api_key)
            if response and response.status_code == 200:
                print(f"✅ PASS: Stock option report for {test_date} fetched")
                print(f"📏 Content length: {len(response.content)} bytes")
                test_results.append(("Stock Option Recent", True))
                break
            else:
                print(f"⚠️  Report for {test_date} not available")
        except Exception as e:
            print(f"❌ Error for {test_date}: {e}")
    else:
        print("❌ FAIL: No recent stock option reports available")
        test_results.append(("Stock Option Recent", False))
    
    # Test 5: Index Option Report
    print("\n" + "="*50)
    print("TEST 5: Index Option Report (HSI)")
    print("="*50)
    
    # Try recent business days for HSI
    for days_back in range(1, 8):
        test_date = today - dt.timedelta(days=days_back)
        
        # Skip weekends
        if test_date.weekday() >= 5:
            continue
            
        print(f"📅 Testing HSI for date: {test_date}")
        
        try:
            response = fetch_hkex_index_option_report('HSI', test_date, api_key)
            if response and response.status_code == 200:
                print(f"✅ PASS: HSI index option report for {test_date} fetched")
                print(f"📏 Content length: {len(response.content)} bytes")
                test_results.append(("Index Option HSI", True))
                break
            else:
                print(f"⚠️  HSI report for {test_date} not available")
        except Exception as e:
            print(f"❌ Error for HSI {test_date}: {e}")
    else:
        print("❌ FAIL: No recent HSI index option reports available")
        test_results.append(("Index Option HSI", False))
    
    # Test 6: Performance test
    print("\n" + "="*50)
    print("TEST 6: Performance Test")
    print("="*50)
    
    import time
    
    test_url = "https://www.hkex.com.hk"
    print(f"🔗 Performance testing with: {test_url}")
    
    try:
        start_time = time.time()
        response = fetch_with_firecrawl(test_url, api_key)
        end_time = time.time()
        
        duration = end_time - start_time
        print(f"⏱️  Fetch duration: {duration:.2f} seconds")
        
        if response and response.status_code == 200:
            if duration < 30:  # Under 30 seconds is reasonable
                print("✅ PASS: Performance acceptable")
                test_results.append(("Performance", True))
            else:
                print("⚠️  WARNING: Performance slow but functional")
                test_results.append(("Performance", "Slow"))
        else:
            print("❌ FAIL: Performance test failed")
            test_results.append(("Performance", False))
    except Exception as e:
        print(f"❌ FAIL: Performance test error: {e}")
        test_results.append(("Performance", False))
    
    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    
    passed = sum(1 for _, result in test_results if result is True)
    failed = sum(1 for _, result in test_results if result is False)
    na = sum(1 for _, result in test_results if result == "N/A" or result == "Slow")
    
    for test_name, result in test_results:
        status = "✅ PASS" if result is True else "❌ FAIL" if result is False else f"⚠️  {result}"
        print(f"{test_name:<25}: {status}")
    
    print(f"\n📊 Results: {passed} passed, {failed} failed, {na} other")
    
    if failed == 0:
        print("\n🎉 All critical tests passed! Firecrawl is ready for production use.")
        return True
    elif passed > failed:
        print("\n⚠️  Most tests passed. Firecrawl should work but may have some issues.")
        return True
    else:
        print("\n❌ Multiple tests failed. Check your API key and network connection.")
        return False

def save_test_content():
    """Save a sample of fetched content for inspection"""
    print("\n" + "="*50)
    print("SAVING SAMPLE CONTENT")
    print("="*50)
    
    api_key = os.getenv('FIRECRAWL_API_KEY', 'fc-f514d4245edf41438797e1733226896c')
    sample_url = "https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/hsiwo250613.htm"
    
    try:
        response = fetch_with_firecrawl(sample_url, api_key)
        if response and response.status_code == 200:
            # Save to file for inspection
            output_dir = Path("output")
            output_dir.mkdir(exist_ok=True)
            
            output_file = output_dir / "firecrawl_sample.html"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(response.text)
            
            print(f"💾 Sample content saved to: {output_file}")
            print(f"📏 File size: {len(response.text)} characters")
            
            # Show first few lines
            lines = response.text.split('\n')[:10]
            print("\n📄 First 10 lines of content:")
            for i, line in enumerate(lines, 1):
                print(f"{i:2}: {line[:80]}{'...' if len(line) > 80 else ''}")
                
        else:
            print("❌ Failed to fetch sample content")
    except Exception as e:
        print(f"❌ Error saving sample content: {e}")

def main():
    """Main test function"""
    print("🧪 Firecrawl Test Suite")
    print("=" * 60)
    
    # Run comprehensive tests
    success = test_firecrawl_comprehensive()
    
    # Save sample content if tests passed
    if success:
        save_test_content()
    
    print("\n" + "="*60)
    if success:
        print("🎉 FIRECRAWL TESTS COMPLETED SUCCESSFULLY!")
        print("\n💡 Next steps:")
        print("   1. Firecrawl is ready to use as a fallback method")
        print("   2. Proceed to Task 2: Integrate with main script")
        print("   3. Test the integrated solution")
    else:
        print("❌ FIRECRAWL TESTS FAILED")
        print("\n💡 Troubleshooting:")
        print("   1. Check your internet connection")
        print("   2. Verify your Firecrawl API key")
        print("   3. Ensure firecrawl-py is installed: pip install firecrawl-py")
        print("   4. Check Firecrawl service status")

if __name__ == "__main__":
    main()
