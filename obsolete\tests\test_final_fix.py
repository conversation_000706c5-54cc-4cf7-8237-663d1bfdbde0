import asyncio
import sys
import os

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

async def test_process_execution():
    """Test running a process through the orchestrator with the Windows fix."""
    print("Testing end-to-end process execution...")
    
    try:
        # Import the fixed orchestrator
        from app.services.simple_orchestrator import orchestrator
        print("✅ Orchestrator imported successfully")
        
        # Test parameters
        test_parameters = {
            'txn_date': '2024-05-24',
            'dry_run': True,
            'batch_size': 100
        }
        
        print(f"Available process types: {list(orchestrator.get_process_types().keys())}")
        
        # Start a test process
        print("\n🚀 Starting update_index_options process...")
        task_id = await orchestrator.start_process('update_index_options', test_parameters)
        print(f"✅ Process started with task ID: {task_id}")
        
        # Monitor the process for a few seconds
        print("\n📊 Monitoring process...")
        for i in range(15):  # Monitor for 15 seconds
            await asyncio.sleep(1)
            status = orchestrator.get_process_status(task_id)
            if status:
                print(f"Status: {status['status']} | Progress: {status['progress']}% | Message: {status['message']}")
                
                if status['status'] in ['completed', 'failed', 'cancelled']:
                    break
            else:
                print(f"No status found for task {task_id}")
                break
        
        # Test log methods
        print("\n📝 Testing log methods...")
        logs = orchestrator.get_process_logs(task_id)
        print(f"Log output lines: {len(logs.get('output', []))}")
        
        log_tail = orchestrator.get_log_tail(task_id, 5)
        print(f"Last 5 log lines: {len(log_tail)}")
        
        full_log = orchestrator.get_full_log_content(task_id)
        print(f"Full log content length: {len(full_log)} characters")
        
        print("\n✅ All tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Set Windows event loop policy for subprocess support
    if os.name == 'nt':
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        print("✅ Windows ProactorEventLoopPolicy set")
    
    success = asyncio.run(test_process_execution())
    
    if success:
        print("\n🎉 All Windows subprocess and orchestrator fixes are working!")
    else:
        print("\n💥 Some tests failed!")
