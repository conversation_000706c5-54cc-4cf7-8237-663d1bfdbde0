#!/bin/bash

# Script to start the MaxPain2024 application using Docker Compose

# Load environment variables from .env file
if [ -f .env ]; then
  set -a  # automatically export all variables
  source .env
  set +a  # stop automatically exporting
fi

# Set default ports if not in environment
FRONTEND_PORT=${FRONTEND_PORT:-3080}
BACKEND_PORT=${BACKEND_PORT:-8004}

# Default to production mode
MODE="prod"

# Check if an argument is provided
if [ ! -z "$1" ]; then
  MODE=$1
fi

echo "Starting application in $MODE mode..."

if [ "$MODE" == "dev" ]; then
  echo "Using docker-compose.dev.yml"
  docker-compose -f docker-compose.dev.yml up -d --build
  if [ $? -eq 0 ]; then
    echo "Development services started successfully."
    echo "Backend should be available at http://localhost:${BACKEND_PORT}"
    echo "Frontend should be available at http://localhost:${FRONTEND_PORT}"
  else
    echo "Failed to start development services."
  fi
elif [ "$MODE" == "prod" ]; then
  echo "Using docker-compose.yml"
  docker-compose -f docker-compose.yml up -d --build
  if [ $? -eq 0 ]; then
    echo "Production services started successfully."
    echo "Application should be available at:"
    echo "  - Main application: http://localhost:${FRONTEND_PORT}"
    echo "  - API documentation: http://localhost:${FRONTEND_PORT}/docs"
    echo "  - API health check: http://localhost:${FRONTEND_PORT}/health"
  else
    echo "Failed to start production services."
  fi
elif [ "$MODE" == "status" ] || [ "$MODE" == "--status" ]; then
  echo "Current Port Configuration:"
  echo "  - Frontend Port: ${FRONTEND_PORT}"
  echo "  - Backend Port: ${BACKEND_PORT}"
  echo ""
  echo "Container Status:"
  docker-compose ps
  echo ""
  echo "If services are running, they should be available at:"
  echo "  - Main application: http://localhost:${FRONTEND_PORT}"
  echo "  - Backend API: http://localhost:${BACKEND_PORT}"
  echo "  - API health check: http://localhost:${BACKEND_PORT}/health"
  echo "  - WebSocket: ws://localhost:${BACKEND_PORT}/ws"
else
  echo "Invalid mode specified. Use 'dev', 'prod', or 'status'."
  exit 1
fi

echo "To view logs, use:"
echo "  Development: docker-compose -f docker-compose.dev.yml logs -f"
echo "  Production:  docker-compose logs -f"
echo ""
echo "To stop services, use:"
echo "  Development: docker-compose -f docker-compose.dev.yml down"
echo "  Production:  docker-compose down"
