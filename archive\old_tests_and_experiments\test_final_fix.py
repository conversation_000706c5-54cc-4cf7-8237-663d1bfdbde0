#!/usr/bin/env python3
"""
Test script to verify the final fix for Windows subprocess issue
"""
import asyncio
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'dashboard', 'backend'))

from app.services.simple_orchestrator import orchestrator
import json

async def test_subprocess_fix():
    """Test that the subprocess creation works on Windows"""
    print("=== Testing Final Windows Subprocess Fix ===")
    print(f"Platform: {sys.platform}")
    
    # Check event loop
    try:
        loop = asyncio.get_running_loop()
        print(f"Current event loop: {type(loop)}")
        if sys.platform == 'win32' and isinstance(loop, asyncio.ProactorEventLoop):
            print("✅ Using ProactorEventLoop on Windows")
        elif sys.platform != 'win32':
            print("✅ Not Windows, event loop type is fine")
        else:
            print(f"⚠️ Windows but using {type(loop)}")
    except Exception as e:
        print(f"❌ Could not get event loop: {e}")
    
    # Test process start
    try:
        print("\nTesting process start...")
        task_id = await orchestrator.start_process('update_index_options', {})
        print(f"✅ Process started successfully: {task_id}")
        
        # Wait a moment and check status
        await asyncio.sleep(2)
        status = orchestrator.get_process_status(task_id)
        if status:
            print(f"Process status: {status['status']}")
            print(f"Process message: {status['message']}")
            if status['status'] in ['running', 'completed']:
                print("✅ Subprocess creation successful!")
            elif status['status'] == 'failed':
                print(f"❌ Process failed: {status.get('error', 'Unknown error')}")
            else:
                print(f"Process in status: {status['status']}")
        else:
            print("❌ Could not get process status")
            
    except Exception as e:
        print(f"❌ Process start failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("Starting final fix test...")
    asyncio.run(test_subprocess_fix())
