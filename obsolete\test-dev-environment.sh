#!/bin/bash

# Test script to verify development environment setup

echo "Testing HKEX Dashboard Development Environment..."
echo "=============================================="

# Check if we're in the right directory
if [ ! -f "docker-compose.dev.yml" ]; then
    echo "❌ Error: docker-compose.dev.yml not found. Run this from the dashboard directory."
    exit 1
fi

echo "✅ Found docker-compose.dev.yml"

# Check if the environment configuration file exists
if [ ! -f "frontend/src/config/environment.ts" ]; then
    echo "❌ Error: Environment configuration file not found"
    exit 1
fi

echo "✅ Found environment configuration"

# Test starting development containers in dry-run mode
echo ""
echo "🧪 Testing development container configuration..."

# Quick validation of docker-compose file
docker-compose -f docker-compose.dev.yml config > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ Docker Compose configuration is valid"
else
    echo "❌ Docker Compose configuration has errors"
    exit 1
fi

echo ""
echo "📋 Development Environment Summary:"
echo "   Frontend: http://localhost:3000 (React dev server)"
echo "   Backend:  http://localhost:8000 (FastAPI with reload)"
echo "   Redis:    localhost:6379"
echo ""
echo "🔌 WebSocket Configuration:"
echo "   Development: ws://localhost:8000/ws (direct to backend)"
echo "   Production:  ws://localhost/ws (through nginx proxy)"
echo ""
echo "✅ Development environment configuration looks good!"
echo "   Run './start-dev.sh' or 'start-dev.bat' to start development"
