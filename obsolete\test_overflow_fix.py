#!/usr/bin/env python3
"""
Test the overflow fix for numeric columns in copyViewMultiDB.py
"""
import pandas as pd
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import our functions
from copyViewMultiDB import get_sqlalchemy_dtype_mapping, ensure_numeric_types, analyze_numeric_ranges

def test_overflow_handling():
    """Test handling of large values that would cause DECIMAL overflow"""
    print("=== Testing Overflow Handling ===")
    
    # Create test DataFrame with values that would cause DECIMAL(15,6) overflow
    test_data = {
        'strike_price': [100.50, 105.25, 110.00],        # Normal range - should use DECIMAL
        'coi_delta_value': [71050.0, 3339350.0, 50000.0], # Large values - should use FLOAT
        'poi_option_value': [-3900.0, -55419.0, -124104.0], # Large values - should use FLOAT
        'volume': [1000, 2000, 1500],                     # Normal range - should use INTEGER
        'delta': [0.45, 0.52, 0.38],                      # Financial data - should use DECIMAL
        'symbol': ['AAPL', 'GOOGL', 'MSFT'],              # Text - should use VARCHAR
        'very_large_value': [5e9, 3e10, 1.2e11],          # Huge values - should use FLOAT
    }
    
    df = pd.DataFrame(test_data)
    print("Test DataFrame created:")
    print(df.dtypes)
    print()
    
    # Test ensure_numeric_types function
    print("=== Testing ensure_numeric_types ===")
    df_numeric = ensure_numeric_types(df)
    print("After ensure_numeric_types:")
    print(df_numeric.dtypes)
    print()
    
    # Test analyze_numeric_ranges function
    print("=== Testing analyze_numeric_ranges ===")
    analyze_numeric_ranges(df_numeric)
    print()
    
    # Test get_sqlalchemy_dtype_mapping function
    print("=== Testing get_sqlalchemy_dtype_mapping ===")
    dtype_mapping = get_sqlalchemy_dtype_mapping(df_numeric, 'test_table')
    
    print("SQLAlchemy dtype mapping:")
    for col, sqltype in dtype_mapping.items():
        print(f"  {col}: {sqltype}")
    print()
    
    # Verify expected mappings
    expected_float_columns = ['coi_delta_value', 'poi_option_value', 'very_large_value']
    expected_decimal_columns = ['strike_price', 'delta']
    
    print("=== Verification ===")
    all_correct = True
    
    for col in expected_float_columns:
        actual = str(dtype_mapping[col])
        is_float = 'FLOAT' in actual.upper()
        status = "✓" if is_float else "✗"
        print(f"{status} {col}: expected FLOAT, got {actual}")
        if not is_float:
            all_correct = False
    
    for col in expected_decimal_columns:
        actual = str(dtype_mapping[col])
        is_decimal = 'DECIMAL' in actual.upper()
        status = "✓" if is_decimal else "✗"
        print(f"{status} {col}: expected DECIMAL, got {actual}")
        if not is_decimal:
            all_correct = False
    
    print()
    if all_correct:
        print("🎉 All overflow handling tests passed!")
        return True
    else:
        print("❌ Some overflow handling tests failed")
        return False

if __name__ == "__main__":
    print("Testing overflow fix for copyViewMultiDB.py")
    print("=" * 50)
    
    success = test_overflow_handling()
    
    print()
    print("=" * 50)
    if success:
        print("🎉 All tests passed! The overflow fix is working correctly.")
        print("Large values will now use FLOAT instead of DECIMAL to avoid overflow errors.")
    else:
        print("❌ Some tests failed. Please check the output above.")
