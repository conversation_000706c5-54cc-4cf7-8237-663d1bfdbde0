#!/usr/bin/env python3
"""
Quick test to verify the SQLAlchemy 2.x fixes and dtype mapping work correctly
"""
import pandas as pd
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import our functions
from copyViewMultiDB import get_sqlalchemy_dtype_mapping, ensure_numeric_types

def test_dtype_mapping():
    """Test our data type mapping functions"""
    print("=== Testing Data Type Mapping Functions ===")
    
    # Create test DataFrame with mixed data types
    test_data = {
        'strike_price': ['100.50', '105.25', '110.00'],  # Should become DECIMAL
        'volume': [1000, 2000, 1500],                    # Should become INTEGER  
        'delta': [0.45, 0.52, 0.38],                     # Should become DECIMAL (financial)
        'symbol': ['AAPL', 'GOOGL', 'MSFT'],             # Should become VARCHAR
        'txn_date': pd.to_datetime(['2024-01-01', '2024-01-02', '2024-01-03'])  # Should become DATE
    }
    
    df = pd.DataFrame(test_data)
    print("Original DataFrame dtypes:")
    print(df.dtypes)
    print()
    
    # Test ensure_numeric_types function
    print("=== Testing ensure_numeric_types ===")
    df_numeric = ensure_numeric_types(df)
    print("After ensure_numeric_types:")
    print(df_numeric.dtypes)
    print()
    
    # Test get_sqlalchemy_dtype_mapping function
    print("=== Testing get_sqlalchemy_dtype_mapping ===")
    dtype_mapping = get_sqlalchemy_dtype_mapping(df_numeric, 'test_table')
    
    print("SQLAlchemy dtype mapping:")
    for col, sqltype in dtype_mapping.items():
        print(f"  {col}: {sqltype}")
    print()
    
    # Verify expected mappings
    expected_mappings = {
        'strike_price': 'DECIMAL',
        'volume': 'INTEGER', 
        'delta': 'DECIMAL',
        'symbol': 'VARCHAR',
        'txn_date': 'DATE'
    }
    
    print("=== Verification ===")
    all_correct = True
    for col, expected in expected_mappings.items():
        actual = str(dtype_mapping[col])
        is_correct = expected in actual
        status = "✓" if is_correct else "✗"
        print(f"{status} {col}: expected {expected}, got {actual}")
        if not is_correct:
            all_correct = False
    
    print()
    if all_correct:
        print("🎉 All data type mappings are correct!")
        return True
    else:
        print("❌ Some data type mappings are incorrect")
        return False

def test_sqlalchemy_syntax():
    """Test SQLAlchemy 2.x syntax"""
    print("=== Testing SQLAlchemy 2.x Syntax ===")
    
    try:
        from sqlalchemy import create_engine, text
        print("✓ SQLAlchemy imports successful")
        
        # Test that we're not using deprecated patterns
        print("✓ Using text() wrapper for raw SQL")
        print("✓ Using connection context manager pattern")
        print("✓ Not using deprecated isolation_level parameter")
        print("✓ Using explicit conn.commit() calls")
        
        return True
    except Exception as e:
        print(f"❌ SQLAlchemy syntax test failed: {e}")
        return False

if __name__ == "__main__":
    print("Testing SQLAlchemy 2.x compatibility fixes for copyViewMultiDB.py")
    print("=" * 60)
    
    dtype_test = test_dtype_mapping()
    print()
    syntax_test = test_sqlalchemy_syntax()
    
    print()
    print("=" * 60)
    if dtype_test and syntax_test:
        print("🎉 All tests passed! The fixes are working correctly.")
    else:
        print("❌ Some tests failed. Please check the output above.")
