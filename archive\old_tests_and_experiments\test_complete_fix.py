#!/usr/bin/env python3
"""Test the complete Windows subprocess fix by starting server and testing API"""
import subprocess
import time
import requests
import json
import sys
import os

def test_backend_api():
    """Test the backend API to verify Windows subprocess fix"""
    base_url = "http://localhost:8000"
    
    print("🚀 Testing HKEX Backend API with Windows Subprocess Fix")
    print("=" * 60)
    
    # Give the server a moment to start
    time.sleep(3)
    
    # Test 1: Check if server is running
    print("\n1️⃣ Testing server connectivity...")
    try:
        response = requests.get(f"{base_url}/docs", timeout=5)
        if response.status_code == 200:
            print("✅ Backend server is running")
        else:
            print(f"❌ Server responded with {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to server: {e}")
        return False
    
    # Test 2: Get process types
    print("\n2️⃣ Getting available process types...")
    try:
        response = requests.get(f"{base_url}/api/v1/processes/types", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Found {len(data.get('process_types', []))} process types")
        else:
            print(f"❌ Failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    # Test 3: Start a process (the real test for Windows subprocess fix)
    print("\n3️⃣ Testing process start (Windows subprocess fix)...")
    try:
        payload = {
            "process_type": "update_index_options",
            "parameters": {
                "txn_date": "2024-12-20",
                "dry_run": True
            }
        }
        response = requests.post(f"{base_url}/api/v1/processes/start", json=payload, timeout=10)
        if response.status_code == 200:
            data = response.json()
            task_id = data.get('task_id')
            print(f"✅ Process started successfully: {task_id}")
            
            # Test 4: Monitor the process
            print("\n4️⃣ Monitoring process status...")
            for i in range(5):
                time.sleep(1)
                try:
                    status_response = requests.get(f"{base_url}/api/v1/processes/{task_id}/status", timeout=5)
                    if status_response.status_code == 200:
                        status_data = status_response.json()
                        status = status_data.get('status', 'unknown')
                        message = status_data.get('message', 'no message')
                        error = status_data.get('error', '')
                        
                        print(f"   Step {i+1}: {status} - {message}")
                        
                        # Check for the specific NotImplementedError
                        if 'NotImplementedError' in str(error):
                            print(f"❌ Windows subprocess fix FAILED: {error}")
                            return False
                        elif status in ['completed', 'failed']:
                            if status == 'completed':
                                print("✅ Process completed successfully - Windows fix working!")
                            else:
                                print(f"⚠️  Process failed but not due to NotImplementedError: {error}")
                            break
                    else:
                        print(f"   Step {i+1}: Status check failed ({status_response.status_code})")
                except Exception as e:
                    print(f"   Step {i+1}: Error checking status - {e}")
            
            print("✅ Windows subprocess fix is working - no NotImplementedError!")
            return True
        else:
            print(f"❌ Failed to start process: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        if 'NotImplementedError' in str(e):
            print(f"❌ Windows subprocess fix FAILED: {e}")
            return False
        else:
            print(f"❌ Error starting process: {e}")
            return False

if __name__ == "__main__":
    print("Starting backend server...")
    
    # Start the server in background
    server_process = subprocess.Popen([
        sys.executable, "-m", "uvicorn", "app.main:app", 
        "--host", "0.0.0.0", "--port", "8000"
    ], cwd="O:/Github/MaxPain/MaxPain2024/dashboard/backend")
    
    try:
        # Test the API
        success = test_backend_api()
        
        if success:
            print("\n🎉 All tests passed! Windows subprocess fix is working!")
        else:
            print("\n💥 Tests failed!")
            
    finally:
        # Clean up server
        print("\nShutting down server...")
        server_process.terminate()
        server_process.wait()
    
    sys.exit(0 if success else 1)
