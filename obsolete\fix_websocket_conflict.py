#!/usr/bin/env python3
"""
WebSocket Conflict Diagnostic and <PERSON><PERSON>ript

This script diagnoses and attempts to fix the WebSocket import conflict
that prevents Selenium from working in the Docker environment.

The issue: Selenium needs 'websocket-client' but there's a conflict with 'websocket'
"""

import sys
import subprocess
import importlib.util

def check_package_installed(package_name):
    """Check if a package is installed"""
    spec = importlib.util.find_spec(package_name)
    return spec is not None

def run_pip_command(command):
    """Run a pip command and return the result"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def diagnose_websocket_issue():
    """Diagnose the WebSocket import issue"""
    print("🔍 Diagnosing WebSocket import conflict...")
    print("=" * 50)
    
    # Check what's installed
    packages_to_check = [
        'websocket',
        'websockets', 
        'websocket_client',
        'selenium',
        'trio',
        'trio_websocket'
    ]
    
    for package in packages_to_check:
        installed = check_package_installed(package)
        status = "✅ INSTALLED" if installed else "❌ NOT FOUND"
        print(f"{package}: {status}")
    
    print("\n🧪 Testing imports...")
    print("-" * 30)
    
    # Test WebSocket imports
    try:
        import websocket
        print("✅ 'websocket' import: SUCCESS")
        print(f"   Location: {websocket.__file__}")
    except ImportError as e:
        print(f"❌ 'websocket' import: FAILED - {e}")
    
    try:
        from websocket import WebSocketApp
        print("✅ 'WebSocketApp' import: SUCCESS")
    except ImportError as e:
        print(f"❌ 'WebSocketApp' import: FAILED - {e}")
    
    try:
        import selenium
        print("✅ 'selenium' import: SUCCESS")
    except ImportError as e:
        print(f"❌ 'selenium' import: FAILED - {e}")
    
    try:
        from selenium import webdriver
        print("✅ 'selenium.webdriver' import: SUCCESS")
    except ImportError as e:
        print(f"❌ 'selenium.webdriver' import: FAILED - {e}")

def fix_websocket_conflict():
    """Attempt to fix the WebSocket conflict"""
    print("\n🔧 Attempting to fix WebSocket conflict...")
    print("=" * 50)
    
    # Step 1: Uninstall conflicting packages
    print("Step 1: Removing conflicting packages...")
    packages_to_remove = ['websocket', 'websocket-client']
    
    for package in packages_to_remove:
        print(f"Uninstalling {package}...")
        success, stdout, stderr = run_pip_command(f"pip uninstall -y {package}")
        if success:
            print(f"✅ Successfully uninstalled {package}")
        else:
            print(f"⚠️  Could not uninstall {package}: {stderr}")
    
    # Step 2: Install the correct websocket-client
    print("\nStep 2: Installing correct websocket-client...")
    success, stdout, stderr = run_pip_command("pip install websocket-client==1.8.0")
    if success:
        print("✅ Successfully installed websocket-client==1.8.0")
    else:
        print(f"❌ Failed to install websocket-client: {stderr}")
        return False
    
    # Step 3: Reinstall Selenium and dependencies
    print("\nStep 3: Reinstalling Selenium dependencies...")
    selenium_deps = [
        "selenium==4.33.0",
        "webdriver-manager==4.0.2",
        "trio==0.30.0",
        "trio-websocket==0.12.2"
    ]
    
    for dep in selenium_deps:
        print(f"Installing {dep}...")
        success, stdout, stderr = run_pip_command(f"pip install {dep}")
        if success:
            print(f"✅ Successfully installed {dep}")
        else:
            print(f"❌ Failed to install {dep}: {stderr}")
    
    return True

def test_selenium_after_fix():
    """Test Selenium functionality after the fix"""
    print("\n🧪 Testing Selenium after fix...")
    print("=" * 50)
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options as ChromeOptions
        print("✅ Selenium imports successful")
        
        # Test basic Chrome options (without actually starting browser)
        chrome_options = ChromeOptions()
        chrome_options.add_argument("--headless")
        print("✅ Chrome options creation successful")
        
        print("🎉 Selenium should now work correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Selenium test failed: {e}")
        return False

def main():
    """Main function to run the diagnostic and fix"""
    print("🚀 WebSocket Conflict Fix Script")
    print("=" * 50)
    
    # Step 1: Diagnose the issue
    diagnose_websocket_issue()
    
    # Step 2: Ask user if they want to attempt a fix
    print("\n" + "=" * 50)
    response = input("Do you want to attempt to fix the WebSocket conflict? (y/n): ").lower().strip()
    
    if response in ['y', 'yes']:
        # Step 3: Attempt the fix
        if fix_websocket_conflict():
            # Step 4: Test the fix
            test_selenium_after_fix()
        else:
            print("❌ Fix attempt failed. Manual intervention may be required.")
    else:
        print("ℹ️  Fix skipped. You can run this script again later.")
    
    print("\n📋 Manual Fix Instructions:")
    print("-" * 30)
    print("If the automatic fix doesn't work, try these commands manually:")
    print("1. pip uninstall -y websocket websocket-client")
    print("2. pip install websocket-client==1.8.0")
    print("3. pip install selenium==4.33.0 webdriver-manager==4.0.2")
    print("4. pip install trio==0.30.0 trio-websocket==0.12.2")

if __name__ == "__main__":
    main()
