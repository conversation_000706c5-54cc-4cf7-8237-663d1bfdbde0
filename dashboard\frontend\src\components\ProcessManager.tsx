import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>H<PERSON>er,
  CardContent,
  Button,
  TextField,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Box,
  Grid,
  Typography,
  Alert,
  CircularProgress,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  LinearProgress
} from '@mui/material';
import { PlayArrow, Stop, Refresh, History } from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiService } from '../services/api';

interface ProcessType {
  value: string;
  label: string;
  description: string;
  required_parameters: string[];
  optional_parameters: string[];
}

interface ProcessStatus {
  task_id: string;
  process_type: string;
  status: 'pending' | 'running' | 'success' | 'failed' | 'cancelled';
  message: string;
  progress: number;
  started_at?: string;
  completed_at?: string;
  output?: string;
  error?: string;
}

const ProcessManager: React.FC = () => {
  const [selectedProcess, setSelectedProcess] = useState<string>('');
  const [parameters, setParameters] = useState<Record<string, string>>({});
  const [showDetails, setShowDetails] = useState<string | null>(null);
  const queryClient = useQueryClient();

  const { data: processTypes, isLoading: typesLoading } = useQuery({
    queryKey: ['processTypes'],
    queryFn: () => apiService.getProcessTypes(),
  });

  const { data: activeProcesses, isLoading: processesLoading } = useQuery({
    queryKey: ['activeProcesses'],
    queryFn: () => apiService.getActiveProcesses(),
    refetchInterval: 2000, // Poll every 2 seconds
  });

  const startProcessMutation = useMutation({
    mutationFn: (data: { process: string; parameters: Record<string, any> }) =>
      apiService.startProcess(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['activeProcesses'] });
      setParameters({});
      setSelectedProcess('');
    },
  });

  const cancelProcessMutation = useMutation({
    mutationFn: (taskId: string) => apiService.cancelProcess(taskId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['activeProcesses'] });
    },
  });

  const handleProcessChange = (processValue: string) => {
    setSelectedProcess(processValue);
    setParameters({});
  };

  const handleParameterChange = (key: string, value: string) => {
    setParameters(prev => ({ ...prev, [key]: value }));
  };

  const handleStartProcess = async () => {
    if (!selectedProcess) return;

    const processType = processTypes?.process_types.find((p: any) => p.value === selectedProcess);
    if (!processType) return;    // Validate required parameters
    const missingParams = processType.required_parameters.filter((param: string) => !parameters[param]);
    if (missingParams.length > 0) {
      alert(`Missing required parameters: ${missingParams.join(', ')}`);
      return;
    }

    startProcessMutation.mutate({
      process: selectedProcess,
      parameters
    });
  };

  const handleCancelProcess = (taskId: string) => {
    cancelProcessMutation.mutate(taskId);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'success';
      case 'failed': return 'error';
      case 'running': return 'primary';
      case 'pending': return 'warning';
      case 'cancelled': return 'default';
      default: return 'default';
    }
  };

  const formatDuration = (startTime?: string, endTime?: string) => {
    if (!startTime) return 'N/A';
    
    const start = new Date(startTime);
    const end = endTime ? new Date(endTime) : new Date();
    const durationMs = end.getTime() - start.getTime();
    const durationSec = Math.floor(durationMs / 1000);
    const minutes = Math.floor(durationSec / 60);
    const seconds = durationSec % 60;
    
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const selectedProcessType = processTypes?.process_types.find((p: any) => p.value === selectedProcess);

  return (
    <Box>
      <Grid container spacing={3}>
        {/* Process Starter */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Start New Process" />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <InputLabel>Process Type</InputLabel>
                    <Select
                      value={selectedProcess}
                      onChange={(e) => handleProcessChange(e.target.value)}
                      disabled={typesLoading || startProcessMutation.isPending}
                    >
                      {processTypes?.process_types.map((type: ProcessType) => (
                        <MenuItem key={type.value} value={type.value}>
                          {type.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                {selectedProcessType && (
                  <>
                    <Grid item xs={12}>
                      <Typography variant="body2" color="text.secondary">
                        {selectedProcessType.description}
                      </Typography>
                    </Grid>                    {/* Required Parameters */}
                    {selectedProcessType.required_parameters.map((param: string) => (
                      <Grid item xs={12} sm={6} key={param}>
                        <TextField
                          fullWidth
                          label={param.replace('_', ' ').toUpperCase()}
                          value={parameters[param] || ''}
                          onChange={(e) => handleParameterChange(param, e.target.value)}
                          required
                          placeholder={
                            param === 'txn_date' ? 'YYYY-MM-DD' :
                            param === 'source_db' ? 'Database name' :
                            param === 'target_db' ? 'Database name' : 
                            `Enter ${param}`
                          }
                        />
                      </Grid>
                    ))}                    {/* Optional Parameters */}
                    {selectedProcessType.optional_parameters.map((param: string) => (
                      <Grid item xs={12} sm={6} key={param}>
                        <TextField
                          fullWidth
                          label={`${param.replace('_', ' ').toUpperCase()} (Optional)`}
                          value={parameters[param] || ''}
                          onChange={(e) => handleParameterChange(param, e.target.value)}
                          placeholder={
                            param === 'symbols' ? 'HSI,HSCEI,HSTECH' :
                            param === 'batch_size' ? '1000' :
                            param === 'views' ? 'view1,view2,view3' :
                            `Enter ${param}`
                          }
                        />
                      </Grid>
                    ))}

                    <Grid item xs={12}>
                      <Button
                        variant="contained"
                        startIcon={<PlayArrow />}
                        onClick={handleStartProcess}
                        disabled={startProcessMutation.isPending}
                        fullWidth
                      >
                        {startProcessMutation.isPending ? 'Starting...' : 'Start Process'}
                      </Button>
                    </Grid>
                  </>
                )}
              </Grid>

              {startProcessMutation.error && (
                <Alert severity="error" sx={{ mt: 2 }}>
                  {startProcessMutation.error.message}
                </Alert>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Active Processes */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader 
              title="Active Processes" 
              action={
                <IconButton onClick={() => queryClient.invalidateQueries({ queryKey: ['activeProcesses'] })}>
                  <Refresh />
                </IconButton>
              }
            />
            <CardContent>
              {processesLoading ? (
                <CircularProgress />
              ) : activeProcesses && activeProcesses.length > 0 ? (
                <Box>
                  {activeProcesses.map((process: ProcessStatus) => (
                    <Card key={process.task_id} variant="outlined" sx={{ mb: 2 }}>
                      <CardContent>
                        <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                          <Typography variant="subtitle1">
                            {process.process_type.replace('_', ' ').toUpperCase()}
                          </Typography>
                          <Box display="flex" gap={1}>
                            <Chip 
                              label={process.status} 
                              color={getStatusColor(process.status)}
                              size="small"
                            />
                            {process.status === 'running' && (
                              <IconButton 
                                size="small" 
                                color="error"
                                onClick={() => handleCancelProcess(process.task_id)}
                                disabled={cancelProcessMutation.isPending}
                              >
                                <Stop />
                              </IconButton>
                            )}
                            <IconButton 
                              size="small"
                              onClick={() => setShowDetails(process.task_id)}
                            >
                              <History />
                            </IconButton>
                          </Box>
                        </Box>

                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          {process.message}
                        </Typography>

                        <LinearProgress 
                          variant="determinate" 
                          value={process.progress} 
                          sx={{ mb: 1 }}
                        />

                        <Box display="flex" justifyContent="space-between">
                          <Typography variant="caption">
                            Duration: {formatDuration(process.started_at, process.completed_at)}
                          </Typography>
                          <Typography variant="caption">
                            {process.progress}%
                          </Typography>
                        </Box>
                      </CardContent>
                    </Card>
                  ))}
                </Box>
              ) : (
                <Typography color="text.secondary">
                  No active processes
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Process Details Dialog */}
      <Dialog 
        open={!!showDetails} 
        onClose={() => setShowDetails(null)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Process Details</DialogTitle>
        <DialogContent>
          {showDetails && (
            <ProcessDetails taskId={showDetails} />
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowDetails(null)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

const ProcessDetails: React.FC<{ taskId: string }> = ({ taskId }) => {
  const { data: processStatus, isLoading } = useQuery({
    queryKey: ['processStatus', taskId],
    queryFn: () => apiService.getProcessStatus(taskId),
    refetchInterval: 2000,
  });

  if (isLoading) return <CircularProgress />;
  if (!processStatus) return <Typography>Process not found</Typography>;

  return (
    <Box>
      <Grid container spacing={2}>
        <Grid item xs={6}>
          <Typography variant="body2"><strong>Task ID:</strong> {processStatus.task_id}</Typography>
        </Grid>
        <Grid item xs={6}>
          <Typography variant="body2"><strong>Process Type:</strong> {processStatus.process_type}</Typography>
        </Grid>
        <Grid item xs={6}>
          <Typography variant="body2"><strong>Status:</strong> {processStatus.status}</Typography>
        </Grid>
        <Grid item xs={6}>
          <Typography variant="body2"><strong>Progress:</strong> {processStatus.progress}%</Typography>
        </Grid>
        <Grid item xs={6}>
          <Typography variant="body2"><strong>Started:</strong> {processStatus.started_at ? new Date(processStatus.started_at).toLocaleString() : 'N/A'}</Typography>
        </Grid>
        <Grid item xs={6}>
          <Typography variant="body2"><strong>Completed:</strong> {processStatus.completed_at ? new Date(processStatus.completed_at).toLocaleString() : 'N/A'}</Typography>
        </Grid>
        <Grid item xs={12}>
          <Typography variant="body2"><strong>Message:</strong> {processStatus.message}</Typography>
        </Grid>
        
        {processStatus.parameters && (
          <Grid item xs={12}>
            <Typography variant="body2"><strong>Parameters:</strong></Typography>
            <pre style={{ fontSize: '0.8rem', backgroundColor: '#f5f5f5', padding: '8px', borderRadius: '4px' }}>
              {JSON.stringify(processStatus.parameters, null, 2)}
            </pre>
          </Grid>
        )}

        {processStatus.output && (
          <Grid item xs={12}>
            <Typography variant="body2"><strong>Output:</strong></Typography>
            <pre style={{ fontSize: '0.8rem', backgroundColor: '#f5f5f5', padding: '8px', borderRadius: '4px', maxHeight: '200px', overflow: 'auto' }}>
              {processStatus.output}
            </pre>
          </Grid>
        )}

        {processStatus.error && (
          <Grid item xs={12}>
            <Typography variant="body2" color="error"><strong>Error:</strong></Typography>
            <pre style={{ fontSize: '0.8rem', backgroundColor: '#ffebee', padding: '8px', borderRadius: '4px', maxHeight: '200px', overflow: 'auto' }}>
              {processStatus.error}
            </pre>
          </Grid>
        )}
      </Grid>
    </Box>
  );
};

export default ProcessManager;
