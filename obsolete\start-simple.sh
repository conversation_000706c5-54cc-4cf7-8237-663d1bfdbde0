#!/bin/bash

# Simple development startup script for HKEX Dashboard
echo "Starting HKEX Dashboard Development Environment..."

# Check if we're in the correct directory
if [ ! -f "docker-compose.dev.yml" ]; then
    echo "Error: Please run this script from the dashboard directory"
    exit 1
fi

# Create logs directory if it doesn't exist
mkdir -p backend/logs

# Copy environment file if it doesn't exist
if [ ! -f ".env" ]; then
    if [ -f ".env.dev" ]; then
        cp .env.dev .env
        echo "Copied .env.dev to .env"
    else
        cp .env.example .env
        echo "Copied .env.example to .env - please update database settings"
    fi
fi

# Start the development environment
echo "Starting services with Docker Compose..."
docker-compose -f docker-compose.dev.yml up --build

echo "Dashboard development environment stopped."
