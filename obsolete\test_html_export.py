#!/usr/bin/env python3
"""
Quick test for HTML export functionality.
This should work immediately without any hanging issues.
"""

import os
from datetime import datetime

def test_html_export():
    """Test HTML export functionality."""
    print("=" * 50)
    print("HTML EXPORT TEST")
    print("=" * 50)
    
    try:
        import plotly.graph_objects as go
        print("✓ Plotly imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import Plotly: {e}")
        return False
    
    # Create test figure
    print("Creating test figure...")
    fig = go.Figure()
    fig.add_trace(go.Scatter(
        x=[1, 2, 3, 4, 5], 
        y=[10, 11, 12, 13, 14], 
        name="Test Data",
        line=dict(color='blue', width=2)
    ))
    fig.update_layout(
        title="HTML Export Test Chart",
        width=1920,
        height=1080,
        xaxis_title="X Axis",
        yaxis_title="Y Axis"
    )
    print("✓ Test figure created")
    
    # Test HTML export
    test_dir = "test_output"
    os.makedirs(test_dir, exist_ok=True)
    
    html_file = f"{test_dir}/html_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
    
    try:
        print(f"Exporting to HTML: {html_file}")
        fig.write_html(html_file, include_plotlyjs='cdn')
        
        if os.path.exists(html_file):
            file_size = os.path.getsize(html_file)
            print(f"✓ HTML export successful: {html_file} ({file_size} bytes)")
            print(f"  You can open this file in any web browser to view the chart")
            return True
        else:
            print("✗ HTML file was not created")
            return False
            
    except Exception as e:
        print(f"✗ HTML export failed: {type(e).__name__}: {e}")
        return False

def main():
    """Main function."""
    success = test_html_export()
    
    if success:
        print("\n🎉 HTML export test passed!")
        print("Your SROC_FS.py script should now work reliably with HTML output.")
        print("\nTo run SROC_FS.py with HTML-only export:")
        print("  python scripts/SROC_FS.py")
        print("\nTo force PNG export (may hang):")
        print("  set FORCE_PNG=1")
        print("  python scripts/SROC_FS.py")
    else:
        print("\n❌ HTML export test failed.")
        print("There may be an issue with your Plotly installation.")
    
    print(f"\nTest completed at: {datetime.now()}")

if __name__ == "__main__":
    main()
