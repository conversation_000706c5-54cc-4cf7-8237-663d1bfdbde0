version: '3.4'

services:
  maxpain:
    image: maxpain
    build:
      context: .. # Changed context to parent directory (project root)
      dockerfile: ./archive/Dockerfile # Path to Dockerfile from new context
    command: ["sh", "-c", "pip install debugpy -t /tmp && python /tmp/debugpy --wait-for-client --listen 0.0.0.0:5678 ./scripts/UpdateIndexOptionPostgres.py "] # Assuming script is in scripts folder at root
    ports:
      - 5678:5678
    volumes:
      - ../:/workspace # Mount project root to /workspace
