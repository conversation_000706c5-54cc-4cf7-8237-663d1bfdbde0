#%%
from flask import Flask
import graphene
from graphene import ObjectType, String, Float, List, Field
from graphene_sqlalchemy import SQLAlchemyObjectType, SQLAlchemyConnectionField
from flask_graphql import GraphQLView
from sqlalchemy import create_engine, Column, String, Float, Date, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import scoped_session, sessionmaker

# Initialize Flask app
app = Flask(__name__)

# Database configuration - replace with your actual PostgreSQL connection details
app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('WILL9700_DB')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Create database connection
engine = create_engine(app.config['SQLALCHEMY_DATABASE_URI'])
db_session = scoped_session(sessionmaker(autocommit=False, autoflush=False, bind=engine))

Base = declarative_base()
Base.query = db_session.query_property()

# Define SQLAlchemy model for the v_monthly_iv view
class VMonthlyIv(Base):
    __tablename__ = 'v_monthly_iv'
    
    # Assuming these are the columns in your view
    # Note: For views, you need to specify a primary key even if it doesn't exist in the actual view
    # Here we use a composite primary key of inst_name and txn_date
    inst_name = Column(String, primary_key=True)
    txn_date = Column(Date, primary_key=True)
    call_iv = Column(Float)
    put_iv = Column(Float)

# Define GraphQL schema
class VMonthlyIvType(SQLAlchemyObjectType):
    class Meta:
        model = VMonthlyIv
        interfaces = (graphene.relay.Node,)

class Query(ObjectType):
    v_monthly_iv = List(VMonthlyIvType, order_by=String())
    
    def resolve_v_monthly_iv(self, info, order_by=None):
        query = VMonthlyIvType.get_query(info)
        
        # Handle order_by
        if order_by == 'inst_name: asc':
            query = query.order_by(VMonthlyIv.inst_name.asc())
        elif order_by == 'inst_name: desc':
            query = query.order_by(VMonthlyIv.inst_name.desc())
        # Add more ordering options as needed
        
        return query.all()

schema = graphene.Schema(query=Query)

# Set up GraphQL endpoint
app.add_url_rule(
    '/graphql',
    view_func=GraphQLView.as_view(
        'graphql',
        schema=schema,
        graphiql=True  # Enable GraphiQL interface for testing
    )
)

@app.teardown_appcontext
def shutdown_session(exception=None):
    db_session.remove()

if __name__ == '__main__':
    app.run(debug=True)