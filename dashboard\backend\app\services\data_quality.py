from datetime import datetime, date
from typing import List, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import text
from ..models.schemas import DataQualityCheck, DataQualityResponse

class DataQualityService:
    def __init__(self, db_session: Session):
        self.db = db_session
    
    async def run_comprehensive_checks(self, txn_date: date = None) -> DataQualityResponse:
        """Run all data quality checks"""
        if txn_date is None:
            txn_date = date.today()
        
        checks = []
        
        # Run various checks
        checks.extend(await self.run_completeness_checks(txn_date))
        checks.extend(await self.run_accuracy_checks(txn_date))
        checks.extend(await self.run_consistency_checks(txn_date))
        
        # Calculate overall score
        if checks:
            total_score = sum(check.score or 0 for check in checks if check.score is not None)
            overall_score = total_score / len(checks)
        else:
            overall_score = 0.0
        
        return DataQualityResponse(
            overall_score=overall_score,
            checks=checks,
            updated_at=datetime.utcnow()
        )
    
    async def run_completeness_checks(self, txn_date: date) -> List[DataQualityCheck]:
        """Check data completeness for a given date"""
        checks = []
        
        # Check if all expected index symbols are present
        expected_symbols = ['HSI', 'HHI', 'HTI', 'MHI']
        for symbol in expected_symbols:
            try:
                query = text("""
                    SELECT COUNT(*) FROM option_daily_report 
                    WHERE txn_date = :txn_date AND inst_name LIKE :symbol
                """)
                count = self.db.execute(query, {
                    "txn_date": txn_date, 
                    "symbol": f"{symbol}%"
                }).scalar()
                
                # Expect at least 10 records per symbol (rough estimate)
                expected_min = 10
                score = min(count / expected_min * 100, 100) if count > 0 else 0
                
                checks.append(DataQualityCheck(
                    check_type="completeness",
                    table_name="option_daily_report",
                    status="pass" if count >= expected_min else "warning",
                    score=score,
                    message=f"Found {count} records for {symbol} (expected >= {expected_min})",
                    details={"symbol": symbol, "count": count, "expected_min": expected_min}
                ))
            except Exception as e:
                checks.append(DataQualityCheck(
                    check_type="completeness",
                    table_name="option_daily_report",
                    status="error",
                    score=0.0,
                    message=f"Error checking {symbol}: {str(e)}"
                ))
        
        return checks
    
    async def run_accuracy_checks(self, txn_date: date) -> List[DataQualityCheck]:
        """Check calculation accuracy"""
        checks = []
        
        # Check delta values are within valid range (-1 to 1)
        try:
            query = text("""
                SELECT COUNT(*) as total_count,
                       SUM(CASE WHEN g.delta < -1 OR g.delta > 1 THEN 1 ELSE 0 END) as invalid_delta_count
                FROM option_greeks g
                JOIN option_daily_report o ON g.option_id = o.id
                WHERE o.txn_date = :txn_date
            """)
            
            result = self.db.execute(query, {"txn_date": txn_date}).fetchone()
            total_count = result[0] if result else 0
            invalid_count = result[1] if result else 0
            
            if total_count > 0:
                accuracy_score = ((total_count - invalid_count) / total_count) * 100
                status = "pass" if invalid_count == 0 else "warning"
            else:
                accuracy_score = 0.0
                status = "error"
            
            checks.append(DataQualityCheck(
                check_type="accuracy",
                table_name="option_greeks",
                status=status,
                score=accuracy_score,
                message=f"Delta validation: {invalid_count} invalid out of {total_count}",
                details={"total_count": total_count, "invalid_count": invalid_count}
            ))
        except Exception as e:
            checks.append(DataQualityCheck(
                check_type="accuracy",
                table_name="option_greeks",
                status="error",
                score=0.0,
                message=f"Error checking delta values: {str(e)}"
            ))
        
        # Check for reasonable settlement prices
        try:
            query = text("""
                SELECT COUNT(*) as total_count,
                       SUM(CASE WHEN settlement_price <= 0 OR settlement_price > 100000 THEN 1 ELSE 0 END) as invalid_price_count
                FROM option_daily_report
                WHERE txn_date = :txn_date
            """)
            
            result = self.db.execute(query, {"txn_date": txn_date}).fetchone()
            total_count = result[0] if result else 0
            invalid_count = result[1] if result else 0
            
            if total_count > 0:
                price_score = ((total_count - invalid_count) / total_count) * 100
                status = "pass" if invalid_count == 0 else "warning"
            else:
                price_score = 0.0
                status = "error"
            
            checks.append(DataQualityCheck(
                check_type="accuracy",
                table_name="option_daily_report",
                status=status,
                score=price_score,
                message=f"Price validation: {invalid_count} invalid out of {total_count}",
                details={"total_count": total_count, "invalid_count": invalid_count}
            ))
        except Exception as e:
            checks.append(DataQualityCheck(
                check_type="accuracy",
                table_name="option_daily_report", 
                status="error",
                score=0.0,
                message=f"Error checking settlement prices: {str(e)}"
            ))
        
        return checks
    
    async def run_consistency_checks(self, txn_date: date) -> List[DataQualityCheck]:
        """Check consistency between tables"""
        checks = []
        
        # Check that all options in daily report have corresponding Greeks
        try:
            query = text("""
                SELECT COUNT(o.id) as total_options,
                       COUNT(g.option_id) as options_with_greeks
                FROM option_daily_report o
                LEFT JOIN option_greeks g ON o.id = g.option_id
                WHERE o.txn_date = :txn_date
            """)
            
            result = self.db.execute(query, {"txn_date": txn_date}).fetchone()
            total_options = result[0] if result else 0
            options_with_greeks = result[1] if result else 0
            
            if total_options > 0:
                consistency_score = (options_with_greeks / total_options) * 100
                status = "pass" if consistency_score >= 95 else "warning"
            else:
                consistency_score = 0.0
                status = "error"
            
            checks.append(DataQualityCheck(
                check_type="consistency",
                table_name="option_daily_report",
                status=status,
                score=consistency_score,
                message=f"Greeks consistency: {options_with_greeks} out of {total_options} options have Greeks",
                details={"total_options": total_options, "options_with_greeks": options_with_greeks}
            ))
        except Exception as e:
            checks.append(DataQualityCheck(
                check_type="consistency",
                table_name="option_daily_report",
                status="error",
                score=0.0,
                message=f"Error checking Greeks consistency: {str(e)}"
            ))
        
        return checks
