# Stock Option Report Processing System

## Overview

The Stock Option Report Processing System is a comprehensive Python application designed to extract, process, and analyze Hong Kong Exchange (HKEX) stock option market data. The system downloads daily option reports, calculates option Greeks using the Black-Scholes model, and maintains a sophisticated database for option position analysis and risk management.

## Table of Contents

1. [System Architecture](#system-architecture)
2. [Data Flow](#data-flow)
3. [Function Reference](#function-reference)
4. [Database Schema](#database-schema)
5. [Installation & Setup](#installation--setup)
6. [Usage Instructions](#usage-instructions)
7. [Configuration](#configuration)
8. [Error Handling](#error-handling)
9. [Performance Considerations](#performance-considerations)

## System Architecture

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                           Stock Option Processing System                    │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐         │
│  │   HKEX Website  │───▶│  Data Extraction │───▶│  HTML Parser    │         │
│  │  Daily Reports  │    │     Module       │    │ (BeautifulSoup) │         │
│  └─────────────────┘    └─────────────────┘    └─────────────────┘         │
│           │                       │                       │                 │
│           ▼                       ▼                       ▼                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐         │
│  │ Local HTML Copy │    │ Contract Data   │    │ Option Greeks   │         │
│  │  (Backup/Debug) │    │   Extraction    │    │  Calculation    │         │
│  └─────────────────┘    └─────────────────┘    └─────────────────┘         │
│                                   │                       │                 │
│                                   ▼                       ▼                 │
│           ┌─────────────────────────────────────────────────────────────┐   │
│           │                PostgreSQL Database                         │   │
│           │                                                             │   │
│           │  ┌───────────────────┐  ┌──────────────────┐  ┌──────────┐  │   │
│           │  │stock_option_report│  │stock_option_     │  │t_delta_  │  │   │
│           │  │    (Raw Data)     │  │   strikeDG       │  │all_strikes│  │   │
│           │  │                   │  │ (Greeks Grid)    │  │(Aggregated)│  │   │
│           │  └───────────────────┘  └──────────────────┘  └──────────┘  │   │
│           │                   │               │               │         │   │
│           │                   ▼               ▼               ▼         │   │
│           │  ┌───────────────────────────────────────────────────────┐  │   │
│           │  │           Materialized Views                          │  │   │
│           │  │    ┌─────────────────┐  ┌─────────────────┐           │  │   │
│           │  │    │option_daily_iv  │  │option_daily_    │           │  │   │
│           │  │    │                 │  │    volume       │           │  │   │
│           │  │    └─────────────────┘  └─────────────────┘           │  │   │
│           │  └───────────────────────────────────────────────────────┘  │   │
│           └─────────────────────────────────────────────────────────────┘   │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Key Components

1. **Data Extraction Layer**: Downloads and parses HKEX HTML reports
2. **Processing Engine**: Calculates option Greeks and performs what-if analysis
3. **Database Layer**: PostgreSQL with optimized schema for option data
4. **Aggregation Engine**: Creates summary tables for analysis and reporting

## Data Flow

```mermaid
graph TD
    A[Start Processing] --> B[Determine Date Range]
    B --> C[Download HKEX Reports]
    C --> D[Parse HTML Content]
    D --> E[Extract Option Data]
    E --> F[Calculate Greeks]
    F --> G[Store Raw Data]
    G --> H[Process Strike Grid]
    H --> I[Calculate What-if Greeks]
    I --> J[Store Greeks Grid]
    J --> K[Aggregate Delta Positions]
    K --> L[Refresh Materialized Views]
    L --> M[Verification & Reporting]
    M --> N[End Processing]
    
    subgraph "Error Handling"
    O[Log Errors]
    P[Continue Processing]
    Q[Retry Failed Operations]
    end
    
    C --> O
    E --> O
    F --> O
    H --> O
    K --> O
```

### Processing Workflow

1. **Date Range Determination** (Step 1)
   - Query database for last processed date
   - Calculate business date range to process
   - Handle incremental vs. full refresh scenarios

2. **Daily Report Processing** (Step 2)
   - Download HKEX HTML reports for each date
   - Parse HTML using BeautifulSoup
   - Extract option contract data (OHLC, volume, OI, IV)
   - Calculate initial Greeks using Black-Scholes model
   - Store in `stock_option_report` table

3. **Greeks Grid Generation** (Step 3)
   - For each option contract, calculate Greeks across price range
   - Generate "what-if" scenarios (80%-120% of current price)
   - Store detailed Greeks in `stock_option_strikeDG` table

4. **Delta Aggregation** (Step 4)
   - Aggregate all option positions by strike price
   - Calculate net delta exposure for calls and puts
   - Store aggregated data in `t_delta_all_strikes` table

5. **View Refresh** (Step 5)
   - Update materialized views for reporting
   - Ensure data consistency across all tables

## Function Reference

### Core Database Query Functions

#### `getSOContract(isymbmonth, d)`
**Purpose**: Retrieve stock option contract data from database  
**Parameters**:
- `isymbmonth` (str): Symbol and month identifier (e.g., 'A50.OCT20')
- `d` (datetime.date): Starting date for query

**Returns**: pandas.DataFrame with contract details  
**Key Features**:
- Filters for significant positions (oi * delta > 10)
- Uses SQLAlchemy 2.0 parameterized queries
- Returns comprehensive option data including Greeks

**Usage Example**:
```python
contracts = getSOContract('XCC.DEC24', datetime.date(2024, 12, 1))
print(f"Found {len(contracts)} active contracts")
```

### Database Insert Functions

#### `insert_SO_Report(iinst_name, itxn_date, iopen, ihigh, ilow, iclose, ivolume, iiv, ioi, ioi_change, istock_price, idelta, ig)`
**Purpose**: Insert complete option contract record into database  
**Parameters**:
- `iinst_name` (str): Full instrument name
- `itxn_date` (datetime.date): Transaction date
- `iopen, ihigh, ilow, iclose` (float): OHLC prices
- `ivolume` (int): Trading volume
- `iiv` (float): Implied volatility percentage
- `ioi` (int): Open interest
- `ioi_change` (int): Change in open interest
- `istock_price` (float): Underlying stock price
- `idelta, ig` (float): Calculated Greeks

**Returns**: 1 if successful, 0 if failed  
**Security**: Uses parameterized queries to prevent SQL injection

#### `insert_SO_strikeDG(itxn_id, ip, idelta, ig)`
**Purpose**: Store option Greeks for different strike prices  
**Use Case**: What-if analysis and risk scenario modeling  
**Parameters**:
- `itxn_id` (int): Transaction ID reference
- `ip` (float): Strike price for calculation
- `idelta, ig` (float): Calculated delta and gamma

### Option Greeks Processing Functions

#### `ProcessSOStrikeDG(isymbmonth)`
**Purpose**: Generate comprehensive Greeks grid for all strike prices  
**Algorithm**:
1. Determine processing date range
2. Retrieve all contracts for symbol/month
3. Define strike range (80%-120% of current price)
4. Calculate Greeks using Black-Scholes for each strike
5. Store results for analysis

**Mathematical Models**:
- **Delta Calculation**: Call δ = N(d₁), Put δ = N(d₁) - 1
- **Gamma Calculation**: γ = φ(d₁)/(S√T)σ
- **Time to Expiry**: Uses business days, accounts for holidays

**Performance**: Processes ~1000 strike/contract combinations per minute

#### `updateDeltaAllStrikes(t_date)`
**Purpose**: Aggregate option deltas across all strikes for specific date  
**Process**:
1. Delete existing aggregations for date
2. Sum delta exposures by contract month and strike
3. Separate call vs. put delta calculations
4. Calculate total gamma exposure
5. Store aggregated results

**Output Metrics**:
- `call_delta`: Net call option delta exposure
- `put_delta`: Net put option delta exposure  
- `abs_delta`: Total absolute delta exposure
- `total_gamma`: Aggregate gamma exposure

### HKEX Data Extraction Functions

#### `getStockOptionReport(symb_list, trade_date)`
**Purpose**: Download and parse HKEX daily option reports  
**Data Source**: https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/  
**Processing Features**:
- Handles multiple symbols simultaneously
- Filters to relevant expiry months (current, next, month+2)
- Calculates Greeks in real-time during parsing
- Robust error handling for parsing failures

**Data Extracted**:
- Contract specifications (symbol, expiry, strike, call/put)
- Market data (OHLC, volume, open interest, implied volatility)
- Underlying stock prices
- Calculated option Greeks

### Main Orchestration Function

#### `main()`
**Purpose**: Orchestrate complete processing workflow  
**Process Flow**:
1. **Date Range Determination**: Calculate processing window
2. **Report Download**: Fetch HKEX data for date range
3. **Greeks Processing**: Calculate option sensitivities
4. **Delta Aggregation**: Create position summaries
5. **View Refresh**: Update materialized views
6. **Verification**: Validate processing results

**Error Handling**: Continues processing even if individual dates fail  
**Logging**: Comprehensive progress and error reporting

## Database Schema

### Primary Tables

#### `stock_option_report`
**Purpose**: Raw option market data from HKEX  
**Key Columns**:
- `txn_id` (Primary Key): Unique transaction identifier
- `inst_name`: Full instrument name (e.g., 'XCC.28NOV24.123456.C')
- `txn_date`: Trading date
- `open, high, low, close`: OHLC prices
- `volume`: Trading volume
- `iv`: Implied volatility (decimal)
- `oi`: Open interest
- `oi_change`: Daily change in open interest
- `stock_price`: Underlying asset price
- `delta, gamma`: Calculated option Greeks

**Indexes**: 
- Primary: `txn_id`
- Secondary: `(txn_date, inst_name)`, `(left(inst_name,11))`

#### `stock_option_strikeDG`
**Purpose**: Option Greeks grid for what-if analysis  
**Key Columns**:
- `txn_id`: References `stock_option_report.txn_id`
- `strike`: Strike price for calculation
- `delta`: Delta at this strike price
- `gamma`: Gamma at this strike price

**Use Case**: Risk scenario analysis, hedging calculations

#### `t_delta_all_strikes`
**Purpose**: Aggregated delta exposure by strike price  
**Key Columns**:
- `cmonth`: Contract month identifier
- `txn_date`: Trading date
- `strike`: Strike price
- `call_delta`: Net call option delta
- `put_delta`: Net put option delta
- `abs_delta`: Total absolute delta
- `total_gamma`: Total gamma exposure

### Materialized Views

#### `option_daily_iv`
**Purpose**: Daily implied volatility analysis  
**Refresh**: Concurrent refresh after data updates

#### `option_daily_volume`
**Purpose**: Daily volume and activity metrics  
**Refresh**: Concurrent refresh after data updates

## Installation & Setup

### Prerequisites

```bash
# Python 3.8+ required
python --version

# Required system packages
pip install -r requirements.txt
```

### Required Python Packages

```txt
pandas>=1.5.0
sqlalchemy>=2.0.0
requests>=2.28.0
beautifulsoup4>=4.11.0
scipy>=1.9.0
python-dotenv>=0.19.0
psycopg2-binary>=2.9.0
```

### Database Setup

```sql
-- Create database
CREATE DATABASE stockoption_db;

-- Create tables (run in order)
CREATE TABLE stock_option_report (
    txn_id SERIAL PRIMARY KEY,
    inst_name VARCHAR(50) NOT NULL,
    txn_date DATE NOT NULL,
    open DECIMAL(10,4),
    high DECIMAL(10,4),
    low DECIMAL(10,4),
    close DECIMAL(10,4),
    volume INTEGER,
    iv DECIMAL(8,4),
    oi INTEGER,
    oi_change INTEGER,
    stock_price DECIMAL(10,4),
    delta DECIMAL(8,6),
    gamma DECIMAL(8,6)
);

CREATE TABLE stock_option_strikeDG (
    txn_id INTEGER REFERENCES stock_option_report(txn_id),
    strike DECIMAL(10,4),
    delta DECIMAL(8,6),
    gamma DECIMAL(8,6)
);

CREATE TABLE t_delta_all_strikes (
    cmonth VARCHAR(20),
    txn_date DATE,
    strike DECIMAL(10,4),
    call_delta DECIMAL(15,6),
    put_delta DECIMAL(15,6),
    abs_delta DECIMAL(15,6),
    total_gamma DECIMAL(15,6)
);

-- Create indexes for performance
CREATE INDEX idx_option_report_date_inst ON stock_option_report(txn_date, inst_name);
CREATE INDEX idx_option_report_inst_prefix ON stock_option_report(left(inst_name,11));
CREATE INDEX idx_strike_dg_txn ON stock_option_strikeDG(txn_id);
CREATE INDEX idx_delta_strikes_date ON t_delta_all_strikes(txn_date);
```

### Environment Configuration

Create a `.env` file in the project root:

```bash
# Database connection
WILL9700_DB=*************************************************/storacle

# Logging configuration
LOG_LEVEL=20  # INFO level
SQL_ECHO=0    # Set to 1 for SQL debugging

# Platform identifier
platform=development
```

## Usage Instructions

### Basic Usage

```bash
# Run complete processing for recent dates
python UpdateStockOptionReportPostgres.py
```

### Advanced Usage

#### Process Specific Date Range

```python
# Modify main() function to process custom date range
start_date = dt.date(2024, 12, 1)
end_date = dt.date(2024, 12, 31)
bdaterange = pd.bdate_range(start_date, end_date)

for single_date in bdaterange:
    insert_day_count = getStockOptionReport(None, single_date)
    # ... continue processing
```

#### Process Specific Symbols

```python
# Process only selected symbols
symbol_list = ['XCC', 'TCH', 'ALB']
insert_day_count = getStockOptionReport(symbol_list, trade_date)
```

#### Greeks Recalculation

```python
# Recalculate Greeks for specific contract
contract_id = 'XCC.DEC24'
result_count = ProcessSOStrikeDG(contract_id)
print(f"Processed {result_count} Greeks calculations")
```

### Monitoring and Verification

```sql
-- Check daily processing results
SELECT txn_date, 
       left(inst_name,3) as symbol,
       count(*) as contracts,
       sum(volume) as total_volume,
       sum(oi) as total_oi
FROM stock_option_report 
WHERE txn_date >= CURRENT_DATE - 7
GROUP BY txn_date, left(inst_name,3)
ORDER BY txn_date DESC, symbol;

-- Verify Greeks grid completeness
SELECT a.txn_date,
       left(a.inst_name,11) as contract,
       count(a.*) as option_records,
       count(b.*) as greeks_records
FROM stock_option_report a
LEFT JOIN stock_option_strikeDG b ON a.txn_id = b.txn_id
WHERE a.txn_date >= CURRENT_DATE - 7
GROUP BY a.txn_date, left(a.inst_name,11)
ORDER BY a.txn_date DESC;

-- Check delta aggregations
SELECT txn_date, 
       cmonth,
       count(*) as strike_count,
       sum(abs_delta) as total_delta_exposure
FROM t_delta_all_strikes
WHERE txn_date >= CURRENT_DATE - 7
GROUP BY txn_date, cmonth
ORDER BY txn_date DESC, cmonth;
```

## Configuration

### Processing Parameters

- **Strike Range**: 80%-120% of current stock price
- **Minimum Position Size**: oi * delta > 10
- **Contract Months**: Current, Next, and Month+2
- **Time Convention**: 247 trading days per year

### Performance Tuning

```python
# Database connection pooling
cnx = create_engine(
    db_url, 
    isolation_level="AUTOCOMMIT",
    pool_size=10,
    max_overflow=20,
    pool_pre_ping=True
)

# Batch processing settings
BATCH_SIZE = 1000  # Records per batch insert
MAX_STRIKES = 50   # Maximum strikes per contract
```

### Logging Configuration

```python
import logging

# Configure detailed logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('stockoption_processing.log'),
        logging.StreamHandler()
    ]
)
```

## Error Handling

### Common Error Scenarios

1. **HKEX Website Unavailable**
   - **Symptom**: HTTP 4xx/5xx errors during download
   - **Handling**: Logs error, continues with next date
   - **Recovery**: Manual re-run for missed dates

2. **HTML Format Changes**
   - **Symptom**: Parsing errors, missing data fields
   - **Handling**: Detailed error logging with problematic HTML line
   - **Recovery**: Update parsing logic in `getStockOptionReport()`

3. **Database Connection Issues**
   - **Symptom**: SQLAlchemy connection errors
   - **Handling**: Transaction rollback, connection retry
   - **Recovery**: Check database connectivity and credentials

4. **Data Quality Issues**
   - **Symptom**: Invalid IV, negative prices, missing OI
   - **Handling**: Skip invalid records, log data quality issues
   - **Recovery**: Manual data correction if needed

### Error Recovery Procedures

```bash
# Check processing logs
tail -f stockoption_processing.log

# Identify failed dates
grep "Error processing" stockoption_processing.log

# Re-run specific date range
python -c "
from UpdateStockOptionReportPostgres import *
import datetime as dt
failed_date = dt.date(2024, 12, 15)
getStockOptionReport(None, failed_date)
"
```

## Performance Considerations

### Processing Benchmarks

- **Daily Report Processing**: ~2-5 minutes per trading date
- **Greeks Calculation**: ~500 contracts per minute
- **Delta Aggregation**: ~10,000 records per second
- **Full Day Processing**: ~10-15 minutes for complete workflow

### Optimization Strategies

1. **Database Optimization**
   - Use appropriate indexes on query columns
   - Regular VACUUM and ANALYZE operations
   - Connection pooling for concurrent operations

2. **Memory Management**
   - Process data in chunks to avoid memory overflow
   - Use pandas iterators for large datasets
   - Clear intermediate DataFrames after processing

3. **Network Optimization**
   - Local HTML file caching
   - Retry logic for failed downloads
   - Parallel processing for independent dates

### Monitoring Queries

```sql
-- Performance monitoring
SELECT schemaname, tablename, 
       n_tup_ins as inserts,
       n_tup_upd as updates,
       n_tup_del as deletes
FROM pg_stat_user_tables 
WHERE tablename LIKE '%option%';

-- Index usage statistics
SELECT indexname, idx_scan, idx_tup_read, idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE tablename LIKE '%option%';

-- Table sizes
SELECT tablename,
       pg_size_pretty(pg_total_relation_size(tablename::regclass)) as size
FROM pg_tables 
WHERE tablename LIKE '%option%';
```

## Maintenance and Support

### Regular Maintenance Tasks

1. **Weekly**: Verify data completeness and quality
2. **Monthly**: Database maintenance (VACUUM, REINDEX)
3. **Quarterly**: Review and optimize slow queries
4. **Annually**: Archive old data, update processing logic

### Support Contacts

- **Database Issues**: DBA team
- **HKEX Data Issues**: Market data team  
- **System Performance**: Infrastructure team

### Version History

- **v2.0**: SQLAlchemy 2.0 migration, enhanced error handling
- **v1.5**: Added materialized views, improved Greeks calculation
- **v1.0**: Initial production release

---

*Last Updated: May 2025*  
*Document Version: 2.0*  
*System Version: SQLAlchemy 2.0 Compatible*
