services:
  # Redis for Celery and caching
  redis:
    image: redis:7-alpine
    container_name: hkex_redis
    volumes:
      - redis_data:/data
    restart: unless-stopped
    command: redis-server --appendonly yes
    networks:
      - backend

  # FastAPI Backend
  backend:
    build:
      context: .
      dockerfile: ./dashboard/backend/Dockerfile
    container_name: hkex_backend
    env_file:
      - .env
    ports:
      - "${BACKEND_PORT}:${BACKEND_PORT}"
    environment:
      - BACKEND_PORT=${BACKEND_PORT}
      - DATABASE_URL=*************************************************/storacle
      - REDIS_URL=redis://redis:6379/0
      - ENVIRONMENT=production
      - PYTHONPATH=/app
      - TZ=Pacific/Auckland
    volumes:
      - C:\\output\\MaxPain\\logs:/app/logs
      - ./:/workspace
      - C:\\output\\MaxPain:/output
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - backend
    command: sh -c "cd /app && uvicorn main:app --host 0.0.0.0 --port ${BACKEND_PORT} --workers 4"

  # Celery Worker
  celery_worker:
    build:
      context: .
      dockerfile: ./dashboard/backend/Dockerfile
    container_name: hkex_celery_worker
    environment:
      - DATABASE_URL=*************************************************/storacle
      - REDIS_URL=redis://redis:6379/0
      - ENVIRONMENT=production
      - PYTHONPATH=/app
      - TZ=Pacific/Auckland
    volumes:
      - C:\\output\\MaxPain\\logs:/app/logs
      - ./:/workspace
      - C:\\output\\MaxPain:/output
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - backend
    command: sh -c "cd /app && celery -A tasks.celery_app:celery_app worker --loglevel=info --concurrency=2"

  # Celery Beat (Scheduler)
  celery_beat:
    build:
      context: .
      dockerfile: ./dashboard/backend/Dockerfile
    container_name: hkex_celery_beat
    environment:
      - DATABASE_URL=*************************************************/storacle
      - REDIS_URL=redis://redis:6379/0
      - ENVIRONMENT=production
      - PYTHONPATH=/app
      - TZ=Pacific/Auckland
    volumes:
      - C:\\output\\MaxPain\\logs:/app/logs
      - ./:/workspace
      - C:\\output\\MaxPain:/output
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - backend
    command: sh -c "cd /app && celery -A tasks.celery_app:celery_app beat --loglevel=info"

  # React Frontend
  frontend:
    build:
      context: ./dashboard/frontend
      dockerfile: Dockerfile.prod
    container_name: hkex_frontend
    environment:
      - REACT_APP_API_URL=http://backend:${BACKEND_PORT}
      - REACT_APP_WS_URL=ws://localhost:${BACKEND_PORT}/ws
      - REACT_APP_FRONTEND_PORT=${FRONTEND_PORT}
      - REACT_APP_BACKEND_PORT=${BACKEND_PORT}
    restart: unless-stopped
    networks:
      - frontend
      - backend

  # Nginx Reverse Proxy
  nginx:
    build:
      context: ./dashboard/nginx
      dockerfile: Dockerfile
    container_name: hkex_nginx
    env_file:
      - .env
    environment:
      - BACKEND_PORT=${BACKEND_PORT}
    ports:
      - "${FRONTEND_PORT}:80"
      - "3443:443"
    volumes:
      - nginx_logs:/var/log/nginx
    depends_on:
      - frontend
      - backend
    restart: unless-stopped
    networks:
      - frontend
      - backend

volumes:
  redis_data:
    driver: local
  nginx_logs:
    driver: local

networks:
  frontend:
    name: hkex_frontend_network
  backend:
    name: hkex_backend_network
