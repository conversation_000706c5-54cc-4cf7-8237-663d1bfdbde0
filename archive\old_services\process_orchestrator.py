print("DEBUG: Starting process_orchestrator.py file execution")

import os
import subprocess
import asyncio
import logging
import json
from datetime import datetime
from typing import Dict, Any, Optional, List
from pathlib import Path

print("DEBUG: All imports completed")

logger = logging.getLogger(__name__)

print("DEBUG: Starting process_orchestrator.py import")

"""
Enhanced Process Orchestration Service for HKEX Dashboard.
Handles running updateIndexOptionPostgres.py, updateStockOptionReportPostgres.py, and copyViewMultiDB.py
without relying on Celery, using asyncio directly for better control.
"""

print("DEBUG: About to define ProcessOrchestratorService class")

class ProcessOrchestratorService:
    """Enhanced orchestrator for HKEX processing scripts with real-time updates."""
    
    def __init__(self):
        print("DEBUG: ProcessOrchestratorService.__init__ called")
        self.active_processes = {}  # Dict[str, asyncio.Task]
        self.websocket_manager = None  # Will be set by dependency injection
        self.process_configs = {
            'update_index_options': {
                'script': 'updateIndexOptionPostgres.py',
                'description': 'Index Option data in PostgreSQL',
                'timeout': 1800,  # 30 minutes
                'requires_params': [],
                'optional_params': ['txn_date', 'dry_run', 'batch_size']
            },
            'update_stock_options': {
                'script': 'updateStockOptionReportPostgres.py', 
                'description': 'Stock Option Report data in PostgreSQL',
                'timeout': 3600,  # 60 minutes
                'requires_params': [],
                'optional_params': ['txn_date', 'dry_run', 'batch_size']
            },
            'copy_view_multidb': {
                'script': 'copyViewMultiDB.py',
                'description': 'Copy views across multiple databases',
                'timeout': 2700,  # 45 minutes
                'requires_params': [],
                'optional_params': ['source_db', 'target_db', 'view_names', 'dry_run']
            }
        }
    
    def set_websocket_manager(self, manager):
        """Set WebSocket manager for broadcasting updates."""
        self.websocket_manager = manager
    
    def get_process_types(self) -> Dict[str, Any]:
        """Get all available process types with their configurations."""
        return {
            name: {
                'description': config['description'],
                'timeout': config['timeout'],
                'requires_params': config['requires_params'],
                'optional_params': config.get('optional_params', [])
            }
            for name, config in self.process_configs.items()
        }
    
    def _find_script_path(self, script_name: str) -> Path:
        """Find the actual path to the script file."""
        # Get the current directory and work backwards to find scripts
        current_dir = Path(__file__).parent
        base_path = current_dir.parent.parent.parent  # Go up to project root
        
        # Check common script locations
        possible_paths = [
            base_path / script_name,
            base_path / "scripts" / script_name,
            base_path / "processing" / script_name,
            base_path / ".." / script_name,
            Path(".") / script_name  # Current directory fallback
        ]
        
        for path in possible_paths:
            if path.exists():
                logger.info(f"Found script {script_name} at {path}")
                return path
                
        raise FileNotFoundError(f"Script {script_name} not found in any expected location: {possible_paths}")
    
    async def start_process(self, process_type: str, parameters: Dict[str, Any]) -> str:
        """Start a new process and return task ID."""
        if process_type not in self.process_configs:
            raise ValueError(f"Unknown process type: {process_type}")
        
        config = self.process_configs[process_type]
        task_id = f"{process_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Validate required parameters
        for param in config['requires_params']:
            if param not in parameters:
                raise ValueError(f"Required parameter '{param}' missing for {process_type}")
        
        # Store process execution info (simplified - no database for now)
        process_info = {
            'task_id': task_id,
            'process_type': process_type,
            'status': 'pending',
            'parameters': parameters,
            'started_at': datetime.utcnow(),
            'progress': 0,
            'message': 'Process initialized'
        }
        
        # Start the process task
        task = asyncio.create_task(
            self._run_process(task_id, process_type, parameters, config)
        )
        self.active_processes[task_id] = {
            'task': task,
            'info': process_info
        }
        
        logger.info(f"Started process {task_id} of type {process_type}")
        
        # Broadcast initial status
        await self._broadcast_update(task_id, process_info)
        
        return task_id
    
    async def cancel_process(self, task_id: str) -> bool:
        """Cancel a running process."""
        if task_id not in self.active_processes:
            return False
        
        process_data = self.active_processes[task_id]
        task = process_data['task']
        
        if not task.done():
            task.cancel()
            process_data['info']['status'] = 'cancelled'
            process_data['info']['message'] = 'Process cancelled by user'
            
            await self._broadcast_update(task_id, process_data['info'])
            logger.info(f"Cancelled process {task_id}")
            return True
        
        return False
    
    def get_process_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get the current status of a process."""
        if task_id not in self.active_processes:
            return None
        
        return self.active_processes[task_id]['info'].copy()
    
    def list_active_processes(self) -> List[Dict[str, Any]]:
        """List all currently active processes."""
        return [
            data['info'].copy() 
            for data in self.active_processes.values()
            if not data['task'].done()
        ]
    
    async def _run_process(self, task_id: str, process_type: str, parameters: Dict[str, Any], config: Dict[str, Any]):
        """Execute the actual process."""
        try:
            # Update status to running
            process_info = self.active_processes[task_id]['info']
            process_info['status'] = 'running'
            process_info['message'] = 'Starting process...'
            await self._broadcast_update(task_id, process_info)
            
            # Find script path
            script_path = self._find_script_path(config['script'])
            
            # Build command arguments
            cmd = ['python', str(script_path)]
            
            # Add parameters as command line arguments
            for key, value in parameters.items():
                if value is not None and value != '':
                    cmd.extend([f'--{key}', str(value)])
            
            logger.info(f"Executing command: {' '.join(cmd)}")
            
            # Start the subprocess
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=script_path.parent
            )
            
            # Monitor process with timeout
            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(),
                    timeout=config['timeout']
                )
                
                if process.returncode == 0:
                    process_info['status'] = 'completed'
                    process_info['progress'] = 100
                    process_info['message'] = 'Process completed successfully'
                    process_info['output'] = stdout.decode('utf-8') if stdout else ''
                else:
                    process_info['status'] = 'failed'
                    process_info['message'] = f'Process failed with return code {process.returncode}'
                    process_info['error'] = stderr.decode('utf-8') if stderr else ''
                    
            except asyncio.TimeoutError:
                process.kill()
                await process.wait()
                process_info['status'] = 'failed'
                process_info['message'] = f'Process timed out after {config["timeout"]} seconds'
                
        except asyncio.CancelledError:
            process_info['status'] = 'cancelled'
            process_info['message'] = 'Process was cancelled'
            logger.info(f"Process {task_id} was cancelled")
            
        except Exception as e:
            process_info['status'] = 'failed'
            process_info['message'] = f'Process failed with error: {str(e)}'
            logger.error(f"Process {task_id} failed: {e}", exc_info=True)
        
        finally:
            process_info['end_time'] = datetime.utcnow()
            await self._broadcast_update(task_id, process_info)
    
    async def _broadcast_update(self, task_id: str, process_info: Dict[str, Any]):
        """Broadcast process update to WebSocket clients."""
        if self.websocket_manager:
            message = {
                'type': 'process_update',
                'task_id': task_id,
                'data': {
                    **process_info,
                    'started_at': process_info['started_at'].isoformat() if isinstance(process_info['started_at'], datetime) else process_info['started_at'],
                    'end_time': process_info['end_time'].isoformat() if process_info.get('end_time') and isinstance(process_info['end_time'], datetime) else process_info.get('end_time')
                }
            }
            await self.websocket_manager.broadcast_to_all(json.dumps(message))
        else:
            logger.debug(f"No WebSocket manager available for broadcasting update: {task_id}")

# Global instance
print("DEBUG: Creating orchestrator instance")
orchestrator = ProcessOrchestratorService()
print("DEBUG: Orchestrator instance created successfully")
