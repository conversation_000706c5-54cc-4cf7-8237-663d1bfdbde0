import asyncio
import requests
import json
import sys
import os
import time

async def test_api_endpoints():
    """Test the API endpoints that were returning 500 errors."""
    print("Testing API endpoints...")
    
    # Import orchestrator to start a process directly for testing
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))
    from app.services.simple_orchestrator import orchestrator
    
    # Start a test process
    test_parameters = {
        'txn_date': '2024-05-24', 
        'dry_run': True
    }
    
    task_id = await orchestrator.start_process('update_index_options', test_parameters)
    print(f"Started test process: {task_id}")
    
    # Wait a moment for some output
    await asyncio.sleep(2)
    
    # Test the log methods directly (these were causing 500 errors)
    print("\n📝 Testing log-related methods that were causing 500 errors...")
    
    try:
        # Test get_log_tail (this was missing and causing 500 error)
        log_tail = orchestrator.get_log_tail(task_id, 10)
        print(f"✅ get_log_tail() works: {len(log_tail)} lines returned")
        
        # Test get_full_log_content (this was missing)
        full_content = orchestrator.get_full_log_content(task_id)
        print(f"✅ get_full_log_content() works: {len(full_content)} chars returned")
        
        # Test get_process_logs (this was missing)
        process_logs = orchestrator.get_process_logs(task_id)
        print(f"✅ get_process_logs() works: {len(process_logs.get('output', []))} output lines")
        
        # Test get_process_history (this was missing)
        history = orchestrator.get_process_history()
        print(f"✅ get_process_history() works: {len(history)} history items")
        
        # Test get_active_processes (this exists but was named differently)
        active = orchestrator.get_active_processes()
        print(f"✅ get_active_processes() works: {len(active)} active processes")
        
        print("\n🎉 All previously missing log methods are now implemented!")
        
        # Show sample log content
        if log_tail:
            print(f"\nSample log tail (last 3 lines):")
            for line in log_tail[-3:]:
                print(f"  {line}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing log methods: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Set Windows event loop policy
    if os.name == 'nt':
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    success = asyncio.run(test_api_endpoints())
    
    if success:
        print("\n🎉 All API endpoint methods are fixed and working!")
        print("The 500 Internal Server Error on log-tail and other endpoints should be resolved.")
    else:
        print("\n💥 Some API method tests failed!")
