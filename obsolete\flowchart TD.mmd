flowchart TD
    %% External Layer
    Internet(["`🌐 **Internet Users**`"]) 
    
    %% Load Balancer Layer
    Internet --> Nginx["`🔧 **Nginx Reverse Proxy**
    - Port 80/443
    - Rate Limiting
    - SSL Termination
    - Static File Serving`"]
    
    %% Application Layer
    Nginx --> Frontend["`⚛️ **React Frontend**
    - TypeScript
    - Tailwind CSS
    - Static Build
    - SPA Routing`"]
    
    Nginx --> Backend["`🚀 **FastAPI Backend**
    - Python 3.11+
    - 4 Uvicorn Workers
    - SQLAlchemy ORM
    - RESTful APIs`"]
    
    %% Async Processing Layer
    Backend --> CeleryWorker["`⚙️ **Celery Worker**
    - Async Task Processing
    - 2 Worker Concurrency
    - Background Jobs`"]
    
    Backend --> CeleryBeat["`⏰ **Celery Beat**
    - Task Scheduler
    - Periodic Jobs
    - Cron-like Scheduling`"]
    
    %% Data Layer
    Backend --> Redis["`🗄️ **Redis Cache**
    - In-Memory Storage
    - Celery Message Broker
    - Session Store`"]
    
    Backend --> PostgreSQL["`🐘 **PostgreSQL**
    - Primary Database
    - ACID Compliance
    - Relational Data`"]
    
    CeleryWorker --> Redis
    CeleryBeat --> Redis
    
    %% Storage Layer
    Backend --> Logs["`📋 **Application Logs**
    - Centralized Logging
    - Error Tracking
    - Debug Information`"]
    
    CeleryWorker --> Output["`📄 **HTML Output**
    - Generated Reports
    - Data Exports
    - File Storage`"]
    
    %% Docker Network Isolation
    subgraph Docker["`🐳 **Docker Environment**`"]
        subgraph FrontendNet["`🌐 **Frontend Network**`"]
            Frontend
            Nginx
        end
        
        subgraph BackendNet["`🔧 **Backend Network**`"]
            Backend
            CeleryWorker
            CeleryBeat
            Redis
        end
        
        subgraph External["`🌍 **External Services**`"]
            PostgreSQL
            Logs
            Output
        end
    end
    
    %% Styling
    classDef internetStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px
    classDef proxyStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef frontendStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef backendStyle fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef dataStyle fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef storageStyle fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef networkStyle fill:#f5f5f5,stroke:#616161,stroke-width:1px,stroke-dasharray: 5 5
    
    class Internet internetStyle
    class Nginx proxyStyle
    class Frontend frontendStyle
    class Backend,CeleryWorker,CeleryBeat backendStyle
    class Redis,PostgreSQL dataStyle
    class Logs,Output storageStyle
    class FrontendNet,BackendNet,External networkStyle