"""
HKEX Report Parser Module

This module handles ONLY the parsing of HKEX HTML reports:
- Reading saved HTML files from hkex folder
- Extracting raw data from HTML structure
- Converting to structured data format
- No calculations or database operations

The parser knows nothing about Black-Scholes calculations or database storage.
It only extracts raw trading data from HTML reports.
"""

from bs4 import BeautifulSoup
from datetime import datetime
from Storacle import listMonths
import os


class HKEXReportParser:
    """Parser for HKEX option reports"""
    
    def __init__(self):
        self.all_months = listMonths()
    
    def parse_daily_report(self, file_path, symb, trade_date):
        """
        Parse daily option report from saved HTML file.
        
        Args:
            file_path: Path to saved HTML file
            symb: Symbol (HSI, HHI, MHI, HTI)
            trade_date: Trading date
            
        Returns:
            tuple: (parsed_data, summary_counts)
        """
        if not os.path.exists(file_path):
            print(f"File not found: {file_path}")
            return [], []
            
        with open(file_path, 'rb') as f:
            content = f.read()
            
        return self._parse_daily_content(content, symb, trade_date)
    
    def parse_weekly_report(self, file_path, symb, trade_date):
        """
        Parse weekly option report from saved HTML file.
        
        Args:
            file_path: Path to saved HTML file
            symb: Symbol (HSI, HHI, HTI)
            trade_date: Trading date
            
        Returns:
            tuple: (parsed_data, summary_counts)
        """
        if not os.path.exists(file_path):
            print(f"File not found: {file_path}")
            return [], []
            
        with open(file_path, 'rb') as f:
            content = f.read()
            
        return self._parse_weekly_content(content, symb, trade_date)
    
    def parse_hti_report(self, file_path, symb, trade_date):
        """
        Parse HTI option report from saved HTML file.

        Args:
            file_path: Path to saved HTML file
            symb: Symbol (HTI)
            trade_date: Trading date

        Returns:
            tuple: (parsed_data, summary_counts)
        """
        if not os.path.exists(file_path):
            print(f"File not found: {file_path}")
            return [], []

        with open(file_path, 'rb') as f:
            content = f.read()

        return self._parse_hti_content(content, symb, trade_date)

    def parse_stock_option_report(self, file_path, symb_list, trade_date):
        """
        Parse stock option report from saved HTML file.

        Stock option reports contain data for all symbols in a single file
        and use a different format than index options.

        Args:
            file_path: Path to saved HTML file
            symb_list: List of symbols to process (or None for all symbols)
            trade_date: Trading date

        Returns:
            tuple: (parsed_data, summary_counts)
        """
        if not os.path.exists(file_path):
            print(f"File not found: {file_path}")
            return [], []

        with open(file_path, 'rb') as f:
            content = f.read()

        return self._parse_stock_option_content(content, symb_list, trade_date)
    
    def _parse_daily_content(self, content, symb, trade_date):
        """Parse daily option report HTML content"""
        print(f'Parsing daily report: {symb}, {trade_date}')
        parsed_data = []

        soup = BeautifulSoup(content, 'html.parser')
        li = soup.prettify().split('\n')

        # Find trading dates
        txn_date, prev_date, ldate = self._extract_trading_dates(li)
        if txn_date is None:
            print('No trading day found in report')
            return [], []

        print(f'Processing trading date: {txn_date}')

        last_month = ''
        txn_count = 0
        summary_counts = []

        # Process transaction lines
        for i in range(ldate, len(li)):
            if len(li[i]) > 1 and li[i][0:3] in self.all_months:
                txn_line = li[i].strip().split()
                
                # Track month changes for summary
                if txn_line[0] != last_month and last_month != '':
                    summary_counts.append([symb + '.' + last_month, txn_date, txn_count])
                    txn_count = 0
                last_month = txn_line[0]

                # Extract raw data
                raw_data = self._extract_daily_line_data(txn_line, symb, txn_date, prev_date)
                if raw_data:
                    parsed_data.append(raw_data)
                    txn_count += 1

        if last_month:
            summary_counts.append([symb + '.' + last_month, txn_date, txn_count])

        return parsed_data, summary_counts
    
    def _parse_weekly_content(self, content, symb, trade_date):
        """Parse weekly option report HTML content"""
        print(f'Parsing weekly report: {symb}, {trade_date}')
        parsed_data = []

        soup = BeautifulSoup(content, 'html.parser')
        li = soup.prettify().split('\n')

        # Find trading dates
        txn_date, prev_date, ldate = self._extract_trading_dates(li)
        if txn_date is None:
            print('No trading day found in weekly report')
            return [], []

        print(f'Processing weekly trading date: {txn_date}')

        last_contract = ''
        txn_count = 0
        summary_counts = []

        # Process transaction lines (weekly format is slightly different)
        for i in range(ldate, len(li)):
            if len(li[i]) > 1 and li[i][3:6] in self.all_months:
                txn_line = li[i].strip().split()
                
                # Track contract changes for summary
                if txn_line[0] != last_contract and last_contract != '':
                    summary_counts.append([symb + '.' + last_contract, txn_date, txn_count])
                    txn_count = 0
                last_contract = txn_line[0]

                # Extract raw data
                raw_data = self._extract_weekly_line_data(txn_line, symb, txn_date, prev_date)
                if raw_data:
                    parsed_data.append(raw_data)
                    txn_count += 1

        if last_contract:
            summary_counts.append([symb + '.' + last_contract, txn_date, txn_count])

        return parsed_data, summary_counts
    
    def _parse_hti_content(self, content, symb, trade_date):
        """Parse HTI option report HTML content"""
        print(f'Parsing HTI report: {symb}, {trade_date}')
        parsed_data = []

        soup = BeautifulSoup(content, 'html.parser')
        li = soup.prettify().split('\n')

        # Find trading date (HTI format is slightly different)
        txn_date, ldate = self._extract_hti_trading_date(li)
        if txn_date is None:
            print('No trading day found in HTI report')
            return [], []

        print(f'Processing HTI trading date: {txn_date}')

        last_contract = ''
        txn_count = 0
        summary_counts = []

        # Process transaction lines
        for i in range(ldate, len(li)):
            if len(li[i]) > 1 and li[i][0:3] in self.all_months:
                txn_line = li[i].strip().split()
                
                # Track contract changes for summary
                if txn_line[0] != last_contract and last_contract != '':
                    summary_counts.append([symb + '.' + last_contract, txn_date, txn_count])
                    txn_count = 0
                last_contract = txn_line[0]

                # Extract raw data
                raw_data = self._extract_hti_line_data(txn_line, symb, txn_date)
                if raw_data:
                    parsed_data.append(raw_data)
                    txn_count += 1

        if last_contract:
            summary_counts.append([symb + '.' + last_contract, txn_date, txn_count])

        return parsed_data, summary_counts

    def _parse_stock_option_content(self, content, symb_list, trade_date):
        """Parse stock option report HTML content"""
        print(f'Parsing stock option report for date: {trade_date}')
        parsed_data = []
        summary_counts = []

        soup = BeautifulSoup(content, 'html.parser')
        li = soup.prettify().split('\n')

        # Initialize processing variables
        symb_item = []
        txn_count = 0
        last_month = ''

        # Determine relevant contract months (current, next, and month+2)
        from datetime import datetime
        from pandas.tseries.offsets import MonthEnd
        import datetime as dt

        this_cmonth = dt.datetime.strftime(trade_date, '%b%y').upper()
        next_cmonth = dt.datetime.strftime(MonthEnd().rollforward(trade_date) + dt.timedelta(1), '%b%y').upper()
        M2_cmonth = dt.datetime.strftime(MonthEnd().rollforward(trade_date) + dt.timedelta(31), '%b%y').upper()

        print(f"Processing contracts for months: {this_cmonth}, {next_cmonth}, {M2_cmonth}")

        # Pre-processing: identify symbols and extract date
        ldate = None  # Initialize ldate
        for i, line in enumerate(li):
            if line.find('AS AT') != -1:
                ldate = i
                print(f"Report date line: {li[ldate]}")
            if line.find('CLASS') != -1:
                header_line = li[i].strip().split()
                symb = header_line[1]

                # Filter symbols based on input parameter
                if symb_list is None:
                    symb_item.append([symb, i])
                elif symb in symb_list:
                    symb_item.append([symb, i])
                    print(f'{symb} added for processing')

        # Check if we found the date and any symbols
        if ldate is None:
            print("❌ Could not find 'AS AT' date line in stock option report")
            return [], []
            
        if not symb_item:
            print("❌ No CLASS symbols found in stock option report")
            return [], []

        # Extract transaction date from report
        l = li[ldate].replace(',', ' ').strip().split()
        try:
            txn_date = datetime.strptime(l[-3] + '-' + l[-2] + '-' + l[-1], '%d-%b-%Y')
        except (IndexError, ValueError) as e:
            print(f"❌ Could not parse date from line: {li[ldate]}")
            print(f"Date parsing error: {e}")
            return [], []
        print(f'Symbols to process: {symb_item}')
        print(f'Transaction date: {txn_date}')

        # Process all transaction lines
        last_symb = ''
        for i in range(symb_item[0][1], len(li)):
            try:
                # Handle symbol header lines
                if li[i][0:5] == 'CLASS':
                    header_line = li[i].strip().split()
                    symb = header_line[1]

                    # Process month break summary
                    if last_symb != '' and txn_count > 0:
                        summary_counts.append([last_symb + '.' + last_month, txn_date, txn_count])
                        print(f'     {last_symb}.{last_month}, Line#{i}, {txn_count} contracts processed')

                    # Handle symbol change
                    if symb != last_symb:
                        if last_symb != '':
                            print(f'  COMPLETED: {last_symb}.{last_month}, {txn_count} total contracts')
                        # Extract underlying stock price from header
                        stock_price = float(header_line[-1].replace(',', ''))

                    # Reset counters for new symbol
                    last_month = ''
                    txn_count = 0
                    last_symb = symb
                    print(f'Processing {symb}, Line#{i}, Stock Price: {stock_price}')

                # Handle contract data lines
                elif li[i][2:5] in self.all_months:
                    txn_line = li[i].strip().split()

                    # Handle contract month changes
                    if txn_line[0] != last_month:
                        if last_month != '' and txn_count > 0:
                            summary_counts.append([last_symb + '.' + last_month, txn_date, txn_count])
                            print(f'    {last_symb}.{last_month}, Line#{i}, {txn_count=}')

                        # Start new contract month
                        last_month = txn_line[0]
                        txn_count = 0

                        # Skip months outside our target range
                        if txn_line[0][-5:] not in {this_cmonth, next_cmonth, M2_cmonth}:
                            continue

                    # Skip processing if outside target months
                    if txn_line[0][-5:] not in {this_cmonth, next_cmonth, M2_cmonth}:
                        continue

                    # Extract raw data from the line
                    raw_data = self._extract_stock_option_line_data(txn_line, symb, txn_date, stock_price, li[i])
                    if raw_data:
                        parsed_data.append(raw_data)
                        txn_count += 1

            except Exception as e:
                print(f'{symb} Exception: {e} at Line#{i}')
                print(f'Problematic line: {li[i]}')
                print(f"{type(e).__name__} at line {e.__traceback__.tb_lineno}: {e}")
                continue

        return parsed_data, summary_counts

    def _extract_trading_dates(self, lines):
        """Extract trading dates from report header"""
        for i, line in enumerate(lines):
            if line.lower().find('trading day of the exchange') != -1:
                date_line = i + 1
                if date_line < len(lines):
                    date_parts = lines[date_line].replace(',', ' ').strip().split()
                    if len(date_parts) >= 7:
                        prev_date = datetime.strptime(f"{date_parts[0]}-{date_parts[1]}-{date_parts[2]}", '%d-%b-%Y')
                        main_date = datetime.strptime(f"{date_parts[4]}-{date_parts[5]}-{date_parts[6]}", '%d-%b-%Y')
                        return main_date, prev_date, date_line
        return None, None, None
    
    def _extract_hti_trading_date(self, lines):
        """Extract trading date from HTI report header (different format)"""
        for i, line in enumerate(lines):
            if line.lower().find('trading day of the exchange') != -1:
                date_line = i + 1
                if date_line < len(lines):
                    date_parts = lines[date_line].replace(',', ' ').strip().split()
                    if len(date_parts) >= 3:
                        main_date = datetime.strptime(f"{date_parts[0]}-{date_parts[1]}-{date_parts[2]}", '%d-%b-%Y')
                        return main_date, date_line
        return None, None

    def _extract_daily_line_data(self, txn_line, symb, txn_date, prev_date):
        """Extract raw data from daily report transaction line"""
        try:
            # Basic contract info
            contract_month = txn_line[0]
            strike_price = int(txn_line[1])
            call_put = txn_line[2]
            inst_name = f"{symb}.{contract_month}.{str(strike_price).zfill(5)}.{call_put}"

            # Previous session data
            if txn_line[3] != '-':  # '-' indicates no previous session
                prev_open = int(txn_line[3])
                prev_high = int(txn_line[4])
                prev_low = int(txn_line[5])
                prev_close = int(txn_line[6])
                prev_volume = int(txn_line[7])
            else:
                prev_open = prev_high = prev_low = prev_close = prev_volume = 0

            # Current session data
            curr_open = int(txn_line[9])
            curr_high = int(txn_line[10])
            curr_low = int(txn_line[11])
            curr_close = int(txn_line[12])
            curr_volume = int(txn_line[15])

            # Market data
            iv_percent = int(txn_line[14])
            open_interest = int(txn_line[-2])
            oi_change = int(txn_line[-1].replace('-', '-0'))  # Handle '-' as 0

            return {
                'inst_name': inst_name,
                'contract_month': contract_month,
                'strike_price': strike_price,
                'call_put': call_put,
                'txn_date': txn_date,
                'prev_date': prev_date,
                'prev_open': prev_open,
                'prev_high': prev_high,
                'prev_low': prev_low,
                'prev_close': prev_close,
                'prev_volume': prev_volume,
                'curr_open': curr_open,
                'curr_high': curr_high,
                'curr_low': curr_low,
                'curr_close': curr_close,
                'curr_volume': curr_volume,
                'iv_percent': iv_percent,
                'open_interest': open_interest,
                'oi_change': oi_change
            }
        except (ValueError, IndexError) as e:
            print(f"Error parsing daily line: {txn_line}, Error: {e}")
            return None

    def _extract_weekly_line_data(self, txn_line, symb, txn_date, prev_date):
        """Extract raw data from weekly report transaction line"""
        try:
            # Basic contract info
            contract_code = txn_line[0]  # Weekly contracts have different naming
            strike_price = int(txn_line[1])
            call_put = txn_line[2]
            inst_name = f"{symb}.{contract_code}.{str(strike_price).zfill(5)}.{call_put}"

            # Previous session data
            if txn_line[3] != '-':  # '-' indicates no previous session
                prev_open = int(txn_line[3])
                prev_high = int(txn_line[4])
                prev_low = int(txn_line[5])
                prev_close = int(txn_line[6])
                prev_volume = int(txn_line[7])
            else:
                prev_open = prev_high = prev_low = prev_close = prev_volume = 0

            # Current session data
            curr_open = int(txn_line[9])
            curr_high = int(txn_line[10])
            curr_low = int(txn_line[11])
            curr_close = int(txn_line[12])
            curr_volume = int(txn_line[15])

            # Market data
            iv_percent = int(txn_line[14])
            open_interest = int(txn_line[-2])
            oi_change = int(txn_line[-1].replace('-', '-0'))  # Handle '-' as 0

            return {
                'inst_name': inst_name,
                'contract_code': contract_code,
                'strike_price': strike_price,
                'call_put': call_put,
                'txn_date': txn_date,
                'prev_date': prev_date,
                'prev_open': prev_open,
                'prev_high': prev_high,
                'prev_low': prev_low,
                'prev_close': prev_close,
                'prev_volume': prev_volume,
                'curr_open': curr_open,
                'curr_high': curr_high,
                'curr_low': curr_low,
                'curr_close': curr_close,
                'curr_volume': curr_volume,
                'iv_percent': iv_percent,
                'open_interest': open_interest,
                'oi_change': oi_change
            }
        except (ValueError, IndexError) as e:
            print(f"Error parsing weekly line: {txn_line}, Error: {e}")
            return None

    def _extract_hti_line_data(self, txn_line, symb, txn_date):
        """Extract raw data from HTI report transaction line"""
        try:
            # Basic contract info
            contract_month = txn_line[0]
            strike_price = int(txn_line[1])
            call_put = txn_line[2]
            inst_name = f"{symb}.{contract_month}.{str(strike_price).zfill(5)}.{call_put}"

            # HTI reports have different format - no previous session data
            curr_open = int(txn_line[3])
            curr_high = int(txn_line[4])
            curr_low = int(txn_line[5])
            curr_close = int(txn_line[6])
            curr_volume = int(txn_line[9])

            # Market data
            iv_percent = int(txn_line[8])
            open_interest = int(txn_line[-2])
            oi_change = int(txn_line[-1].replace('-', '-0'))  # Handle '-' as 0

            return {
                'inst_name': inst_name,
                'contract_month': contract_month,
                'strike_price': strike_price,
                'call_put': call_put,
                'txn_date': txn_date,
                'prev_date': None,  # HTI reports don't have previous session
                'prev_open': 0,
                'prev_high': 0,
                'prev_low': 0,
                'prev_close': 0,
                'prev_volume': 0,
                'curr_open': curr_open,
                'curr_high': curr_high,
                'curr_low': curr_low,
                'curr_close': curr_close,
                'curr_volume': curr_volume,
                'iv_percent': iv_percent,
                'open_interest': open_interest,
                'oi_change': oi_change
            }
        except (ValueError, IndexError) as e:
            print(f"Error parsing HTI line: {txn_line}, Error: {e}")
            return None

    def _extract_stock_option_line_data(self, txn_line, symb, txn_date, stock_price, full_line):
        """Extract raw data from stock option report transaction line"""
        try:
            # Parse contract data from the line
            inst_name = symb + '.' + txn_line[0] + '.' + txn_line[1].zfill(6) + '.' + txn_line[2]
            strike_price = float(txn_line[1].replace(',', ''))
            openprice = float(txn_line[3].replace(',', ''))
            high = float(txn_line[4].replace(',', ''))
            low = float(txn_line[5].replace(',', ''))
            close = float(txn_line[6].replace(',', ''))

            # Extract data from fixed positions (format changed in Nov 2024)
            iv_pct = full_line[69:74].replace(',', '')
            iv = float(max(int(iv_pct), 1) / 100)
            volume = int(full_line[75:85].replace(',', ''))
            oi = int(full_line[86:97].replace(',', ''))
            oi_change = int(full_line[98:].replace('-', '-0').replace(',', ''))

            # Skip contracts with no activity
            if volume == 0 and oi == 0:
                return None

            return {
                'inst_name': inst_name,
                'contract_month': txn_line[0],
                'strike_price': strike_price,
                'call_put': txn_line[2],
                'txn_date': txn_date,
                'curr_open': openprice,
                'curr_high': high,
                'curr_low': low,
                'curr_close': close,
                'curr_volume': volume,
                'iv_percent': int(iv_pct),
                'open_interest': oi,
                'oi_change': oi_change,
                'stock_price': stock_price
            }
        except (ValueError, IndexError) as e:
            print(f"Error parsing stock option line: {txn_line}, Error: {e}")
            return None


# Convenience functions for external use
def parse_daily_report_file(file_path, symb, trade_date):
    """
    Convenience function to parse a daily report file.

    Args:
        file_path: Path to saved HTML file
        symb: Symbol (HSI, HHI, MHI, HTI)
        trade_date: Trading date

    Returns:
        tuple: (parsed_data, summary_counts)
    """
    parser = HKEXReportParser()
    return parser.parse_daily_report(file_path, symb, trade_date)


def parse_weekly_report_file(file_path, symb, trade_date):
    """
    Convenience function to parse a weekly report file.

    Args:
        file_path: Path to saved HTML file
        symb: Symbol (HSI, HHI, HTI)
        trade_date: Trading date

    Returns:
        tuple: (parsed_data, summary_counts)
    """
    parser = HKEXReportParser()
    return parser.parse_weekly_report(file_path, symb, trade_date)


def parse_hti_report_file(file_path, symb, trade_date):
    """
    Convenience function to parse an HTI report file.

    Args:
        file_path: Path to saved HTML file
        symb: Symbol (HTI)
        trade_date: Trading date

    Returns:
        tuple: (parsed_data, summary_counts)
    """
    parser = HKEXReportParser()
    return parser.parse_hti_report(file_path, symb, trade_date)


def parse_stock_option_report_file(file_path, symb_list, trade_date):
    """
    Convenience function to parse a stock option report file.

    Args:
        file_path: Path to saved HTML file
        symb_list: List of symbols to process (or None for all symbols)
        trade_date: Trading date

    Returns:
        tuple: (parsed_data, summary_counts)
    """
    parser = HKEXReportParser()
    return parser.parse_stock_option_report(file_path, symb_list, trade_date)
