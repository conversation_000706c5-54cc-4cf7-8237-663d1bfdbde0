# Environment-Specific Favicons

This documentation describes the implementation of environment-specific favicons for the HKEX Dashboard application.

## Overview

The application now displays different favicons based on the environment:

- **Development Environment**: Blue favicon with "D" indicator
- **Production Environment**: Red favicon with "P" indicator

## Files Created

### Favicon Files
- `public/favicon-dev.svg` - Development favicon (blue)
- `public/favicon-prod.svg` - Production favicon (red)
- `public/favicon-dev.ico` - Development favicon (ICO format)
- `public/favicon-prod.ico` - Production favicon (ICO format)

### Configuration Files
- `src/config/environment.ts` - Updated with favicon selection logic
- `scripts/generate-favicons.js` - Favicon generation script
- `public/manifest.json` - Updated with environment-aware icon references

## How It Works

### 1. Environment Detection
The system uses `process.env.NODE_ENV` to determine the current environment:
- `development` - Loads blue favicon with "D" indicator
- `production` - Loads red favicon with "P" indicator

### 2. Dynamic Favicon Loading
The `updateFavicon()` function in `environment.ts`:
- Detects the current environment
- Selects the appropriate favicon path
- Updates the favicon link element in the document head
- Also updates apple-touch-icon if present

### 3. Application Integration
The favicon is initialized when the App component mounts:
```typescript
useEffect(() => {
  // Initialize environment-specific favicon
  updateFavicon();
  // ... other initialization code
}, []);
```

## Configuration Functions

### `getFaviconPath()`
Returns the appropriate favicon path based on environment:
- Development: `/favicon-dev.ico`
- Production: `/favicon-prod.ico`

### `updateFavicon()`
Dynamically updates the favicon in the browser:
- Finds or creates the favicon link element
- Sets the appropriate href based on environment
- Also handles apple-touch-icon updates

### `isDevelopment()`
Utility function to check if running in development mode.

## Favicon Generation

### Manual Generation
The `scripts/generate-favicons.js` script can be used to generate favicons:

```bash
node scripts/generate-favicons.js
```

This script:
- Updates `manifest.json` with environment-aware icons
- Generates PNG favicons if Sharp is installed
- Provides instructions for ICO conversion

### SVG Sources
The favicon designs are based on simple SVG graphics:
- Blue background (#2563eb) with white "D" text for development
- Red background (#dc2626) with white "P" text for production
- Both include a small colored indicator circle

## Browser Support

The implementation supports:
- Modern browsers with SVG favicon support
- Fallback to ICO format for older browsers
- Apple touch icons for iOS devices

## Manual Steps for Production

1. **Convert SVG to ICO**: Use an online converter or ImageMagick to convert the SVG files to proper ICO format:
   ```bash
   # Using ImageMagick (if available)
   convert favicon-dev.svg favicon-dev.ico
   convert favicon-prod.svg favicon-prod.ico
   ```

2. **Install Sharp** (optional for automatic PNG generation):
   ```bash
   npm install sharp
   ```

## Testing

To test the favicon implementation:

1. **Development Environment**:
   ```bash
   npm start
   ```
   Should show blue favicon with "D"

2. **Production Build**:
   ```bash
   npm run build
   npm run serve # or deploy to production
   ```
   Should show red favicon with "P"

## Troubleshooting

### Favicon Not Updating
- Clear browser cache
- Check browser developer tools for favicon requests
- Verify the correct environment variables are set

### ICO Files Not Working
- Ensure ICO files are properly converted from SVG
- Some browsers cache favicons aggressively - try in incognito mode

### Environment Detection Issues
- Verify `process.env.NODE_ENV` is set correctly
- Check the `isDevelopment()` function output in browser console

## Future Enhancements

Potential improvements for the favicon system:

1. **Animated Favicons**: Add progress indicators during long-running processes
2. **Status Indicators**: Change favicon color based on system health
3. **Custom Icons**: Allow user-defined favicon themes
4. **Notification Badges**: Show notification counts in the favicon

## Related Files

- `src/config/environment.ts` - Main configuration
- `src/App.tsx` - Favicon initialization
- `public/index.html` - HTML template with favicon reference
- `public/manifest.json` - PWA manifest with icon definitions
