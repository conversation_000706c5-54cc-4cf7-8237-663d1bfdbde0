#!/usr/bin/env python3
"""
Comprehensive end-to-end test for HKEX Dashboard
Tests the complete system functionality including Windows asyncio fix
"""

import requests
import json
import time
import sys
from datetime import datetime

# Configuration
BACKEND_URL = "http://localhost:8001"
FRONTEND_URL = "http://localhost:3000"
API_BASE = f"{BACKEND_URL}/api/v1"

class Colors:
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'

def print_header(text):
    print(f"\n{Colors.BLUE}{Colors.BOLD}{'='*60}")
    print(f"{text}")
    print(f"{'='*60}{Colors.ENDC}")

def print_success(text):
    print(f"{Colors.GREEN}✅ {text}{Colors.ENDC}")

def print_error(text):
    print(f"{Colors.RED}❌ {text}{Colors.ENDC}")

def print_warning(text):
    print(f"{Colors.YELLOW}⚠️  {text}{Colors.ENDC}")

def print_info(text):
    print(f"{Colors.BLUE}ℹ️  {text}{Colors.ENDC}")

def test_backend_health():
    """Test backend server health"""
    print_header("1. BACKEND HEALTH CHECK")
    try:
        response = requests.get(f"{BACKEND_URL}/health")
        if response.status_code == 200:
            data = response.json()
            print_success(f"Backend healthy: {data}")
            return True
        else:
            print_error(f"Backend health check failed: {response.status_code}")
            return False
    except Exception as e:
        print_error(f"Backend not accessible: {e}")
        return False

def test_frontend_accessibility():
    """Test frontend accessibility"""
    print_header("2. FRONTEND ACCESSIBILITY CHECK")
    try:
        response = requests.get(FRONTEND_URL, timeout=5)
        if response.status_code == 200 and "HKEX Dashboard" in response.text:
            print_success("Frontend accessible and serving HKEX Dashboard")
            return True
        else:
            print_error(f"Frontend check failed: {response.status_code}")
            return False
    except Exception as e:
        print_error(f"Frontend not accessible: {e}")
        return False

def test_api_endpoints():
    """Test various API endpoints"""
    print_header("3. API ENDPOINTS TEST")
    
    endpoints = [
        ("/processes/types", "GET", "Process types"),
        ("/health", "GET", "Health check")
    ]
    
    success_count = 0
    for endpoint, method, desc in endpoints:
        try:
            url = f"{API_BASE}{endpoint}"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                print_success(f"{desc}: {response.status_code} OK")
                success_count += 1
            else:
                print_error(f"{desc}: {response.status_code}")
        except Exception as e:
            print_error(f"{desc}: {e}")
    
    return success_count == len(endpoints)

def test_process_types():
    """Test process types endpoint specifically"""
    print_header("4. PROCESS TYPES TEST")
    try:
        response = requests.get(f"{API_BASE}/processes/types")
        if response.status_code == 200:
            data = response.json()
            print_success(f"Retrieved {len(data)} process types:")
            for proc_type, config in data.items():
                print_info(f"  - {proc_type}: {config.get('description', 'No description')}")
            return True, data
        else:
            print_error(f"Process types failed: {response.status_code}")
            return False, None
    except Exception as e:
        print_error(f"Process types error: {e}")
        return False, None

def test_windows_subprocess_fix():
    """Test the critical Windows subprocess fix"""
    print_header("5. WINDOWS SUBPROCESS FIX TEST")
    
    # Test with dry_run to avoid actual database changes
    process_data = {
        "process": "update_index_options",
        "parameters": {
            "dry_run": True,
            "txn_date": "2025-05-24"
        }
    }
    
    try:
        print_info("Starting test process...")
        response = requests.post(
            f"{API_BASE}/processes/start",
            json=process_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code != 200:
            print_error(f"Process start failed: {response.status_code}")
            print_error(f"Response: {response.text}")
            return False
        
        result = response.json()
        task_id = result.get("task_id")
        print_success(f"Process started: {task_id}")
        
        # Wait a moment for the process to initialize
        time.sleep(3)
        
        # Check status
        status_response = requests.get(f"{API_BASE}/processes/{task_id}/status")
        if status_response.status_code == 200:
            status_data = status_response.json()
            status = status_data.get("status", "unknown")
            error = status_data.get("error", "")
            message = status_data.get("message", "")
            
            print_info(f"Process status: {status}")
            if message:
                print_info(f"Message: {message}")
            
            # Check for the Windows NotImplementedError
            if "NotImplementedError" in str(error) or "NotImplementedError" in str(message):
                print_error("Windows subprocess NotImplementedError still present")
                print_error("The Windows asyncio fix is not working")
                return False
            else:
                print_success("No NotImplementedError detected")
                print_success("Windows subprocess fix is working!")
                
                if status == "failed":
                    print_warning("Process failed for script-related reasons (not Windows subprocess issue)")
                elif status in ["running", "completed"]:
                    print_success("Process executing successfully")
                
                return True
        else:
            print_error(f"Status check failed: {status_response.status_code}")
            return False
            
    except Exception as e:
        print_error(f"Subprocess test error: {e}")
        return False

def test_system_integration():
    """Test system integration"""
    print_header("6. SYSTEM INTEGRATION TEST")
    
    # Test that we can access the API documentation
    try:
        docs_response = requests.get(f"{BACKEND_URL}/docs", timeout=5)
        if docs_response.status_code == 200:
            print_success("API documentation accessible")
        else:
            print_warning("API documentation not accessible")
    except:
        print_warning("API documentation not accessible")
    
    # Test CORS (frontend can call backend)
    try:
        headers = {
            'Origin': FRONTEND_URL,
            'Content-Type': 'application/json'
        }
        response = requests.get(f"{API_BASE}/processes/types", headers=headers, timeout=5)
        if response.status_code == 200:
            print_success("CORS configured correctly")
        else:
            print_warning("CORS might have issues")
    except:
        print_warning("CORS test failed")
    
    return True

def main():
    """Run comprehensive system test"""
    print_header("🚀 HKEX DASHBOARD - COMPREHENSIVE SYSTEM TEST")
    print(f"Backend: {BACKEND_URL}")
    print(f"Frontend: {FRONTEND_URL}")
    print(f"Test time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Track test results
    results = []
    
    # Run all tests
    results.append(("Backend Health", test_backend_health()))
    results.append(("Frontend Accessibility", test_frontend_accessibility()))
    results.append(("API Endpoints", test_api_endpoints()))
    
    process_types_success, process_types_data = test_process_types()
    results.append(("Process Types", process_types_success))
    
    # The critical test - Windows subprocess fix
    results.append(("Windows Subprocess Fix", test_windows_subprocess_fix()))
    
    results.append(("System Integration", test_system_integration()))
    
    # Final summary
    print_header("📋 COMPREHENSIVE TEST SUMMARY")
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = f"{Colors.GREEN}✅ PASSED{Colors.ENDC}" if success else f"{Colors.RED}❌ FAILED{Colors.ENDC}"
        print(f"{test_name:<30} {status}")
        if success:
            passed += 1
    
    print(f"\n{Colors.BOLD}{'='*60}")
    print(f"TOTAL RESULTS: {passed}/{total} tests passed")
    print(f"{'='*60}{Colors.ENDC}")
    
    if passed == total:
        print_success("🎉 ALL TESTS PASSED!")
        print_success("✅ HKEX Dashboard system is fully functional")
        print_success("✅ Windows asyncio subprocess fix is working")
        print_success("✅ System is ready for production use")
        return True
    else:
        failed = total - passed
        print_error(f"⚠️  {failed} test(s) failed")
        if passed >= 4:  # Most tests passed
            print_warning("System is mostly functional but has some issues")
        else:
            print_error("System has significant issues")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
