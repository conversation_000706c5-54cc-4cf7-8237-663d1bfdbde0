# Critical Files Inventory - HKEX Dashboard

## Overview
This document provides a comprehensive inventory of all critical files required to run the HKEX Dashboard application.

## Backend Core Files

### 1. Entry Points
- **`dashboard/backend/run_server.py`** - Main server startup script
  - Sets up Python path and starts uvicorn server
  - Primary way to start backend in development
  
- **`dashboard/backend/app/main.py`** - FastAPI application entry point
  - Defines FastAPI app, routes, middleware, and startup/shutdown events
  - Configures WebSocket manager and process orchestrator

### 2. Configuration
- **`dashboard/backend/app/core/config.py`** - Backend configuration management
  - Database URLs, API settings, script paths
  - Environment-specific configurations
  
- **`dashboard/backend/requirements.txt`** - Python dependencies
  - FastAPI, SQLAlchemy 2.0, PostgreSQL drivers, WebSocket support
  - All required packages for backend functionality

### 3. Process Management
- **`dashboard/backend/app/services/simple_orchestrator.py`** - Process orchestration logic
  - Manages execution of the three HKEX scripts
  - Handles subprocess management, progress tracking, and status updates
  
- **`dashboard/backend/app/api/routes/processes.py`** - Process management API endpoints
  - REST endpoints for starting, stopping, and monitoring processes
  - Process type definitions and parameter validation

### 4. Real-time Communication
- **`dashboard/backend/app/websocket/manager.py`** - WebSocket connection management
  - Handles real-time updates to frontend
  - Manages client connections and message broadcasting

### 5. Data Models
- **`dashboard/backend/app/models/schemas.py`** - Pydantic models and schemas
  - Request/response models for API endpoints
  - Data validation and serialization

## Frontend Core Files

### 1. Entry Points
- **`dashboard/frontend/package.json`** - Node.js dependencies and build scripts
  - React, Material-UI, TypeScript, Socket.IO client
  - Build and development scripts
  
- **`dashboard/frontend/src/index.tsx`** - React application entry point
  - Renders main App component
  - Sets up React Query and theme providers

- **`dashboard/frontend/src/App.tsx`** - Main application component
  - Application layout and routing
  - Tab-based navigation between dashboard sections

### 2. Process Management UI
- **`dashboard/frontend/src/components/ProcessManager.tsx`** - Process management interface
  - Start/stop processes, view status, configure parameters
  - Real-time process monitoring and control

- **`dashboard/frontend/src/components/ProcessMonitor.tsx`** - Process status monitoring
  - Displays active processes and their current status
  - Real-time updates via WebSocket

### 3. API Integration
- **`dashboard/frontend/src/services/api.ts`** - API client for backend communication
  - HTTP client configuration and API endpoints
  - Request/response handling and error management

### 4. Real-time Features
- **`dashboard/frontend/src/components/RealTimeLogViewer.tsx`** - Live log streaming
  - WebSocket-based log viewer
  - Real-time process output display

## Deployment Files

### 1. Docker Configuration
- **`dashboard/docker-compose.yml`** - Production deployment configuration
  - Backend, frontend, Redis, and nginx services
  - Production-ready container orchestration

- **`dashboard/docker-compose.dev.yml`** - Development deployment configuration
  - Development containers with hot reload
  - Volume mounts for live code changes

- **`dashboard/backend/Dockerfile`** - Backend container configuration
  - Python environment setup and dependency installation
  - Production container build instructions

- **`dashboard/frontend/Dockerfile`** - Frontend container configuration
  - Node.js environment and React build process
  - Static file serving configuration

### 2. Startup Scripts
- **`dashboard/start-dev.sh`** / **`dashboard/start-dev.bat`** - Development startup
  - Starts development environment with Docker Compose
  - Creates necessary directories and environment files

- **`dashboard/start-prod.sh`** / **`dashboard/start-prod.bat`** - Production startup
  - Starts production environment with Docker Compose
  - Includes health checks and service status monitoring

## Integration Files

### 1. HKEX Processing Scripts (Root Directory)
- **`UpdateIndexOptionPostgres.py`** - Index option processing script
- **`UpdateStockOptionReportPostgres.py`** - Stock option processing script
- **`copyViewMultiDB.py`** - Multi-database synchronization script

### 2. Root Configuration
- **`requirements.txt`** - Root-level Python dependencies
  - Core dependencies for HKEX processing scripts
  - Database drivers and data processing libraries

## Quick Start Commands

### Development Mode
```bash
# Option 1: Docker-based (recommended)
cd dashboard
./start-dev.sh  # Linux/Mac
start-dev.bat   # Windows

# Option 2: Manual startup
cd dashboard/backend && python run_server.py
cd dashboard/frontend && npm start
```

### Production Mode
```bash
cd dashboard
./start-prod.sh  # Linux/Mac
start-prod.bat   # Windows
```

## Dependencies Summary

### Backend Dependencies
- Python 3.11+
- FastAPI 0.104.1+
- SQLAlchemy 2.0.23+
- PostgreSQL drivers (psycopg2)
- WebSocket support
- Redis (for caching)

### Frontend Dependencies
- Node.js 18+
- React 18.2+
- Material-UI 5.14+
- TypeScript 4.9+
- Socket.IO client

### Infrastructure
- PostgreSQL database (existing HKEX schema)
- Redis server (for caching and real-time features)
- Docker (optional, for containerized deployment)

## Status: FULLY OPERATIONAL ✅
All critical files are in place and the dashboard is ready for immediate use.
