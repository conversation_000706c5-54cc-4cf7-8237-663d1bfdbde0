import React from 'react';
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  Grid,
  Typography,
  Box,
  Chip,
  CircularProgress,
  Tooltip
} from '@mui/material';
import {
  Storage,
  CheckCircle,
  Warning,
  Error,
  Info
} from '@mui/icons-material';
import { useQuery } from '@tanstack/react-query';
import { apiService } from '../services/api';
import { TableMetric } from '../types';

const TableMetricsGrid: React.FC = () => {
  const { data: tableMetrics, isLoading, error } = useQuery({
    queryKey: ['tableMetrics'],
    queryFn: () => apiService.getTableMetrics(),
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'good':
        return <CheckCircle color="success" />;
      case 'warning':
        return <Warning color="warning" />;
      case 'error':
        return <Error color="error" />;
      default:
        return <Info color="info" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good':
        return 'success';
      case 'warning':
        return 'warning';
      case 'error':
        return 'error';
      default:
        return 'default';
    }
  };

  const formatTableName = (tableName: string) => {
    return tableName
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  const TableMetricCard: React.FC<{ metric: TableMetric }> = ({ metric }) => (
    <Card sx={{ height: '100%' }}>
      <CardHeader
        title={formatTableName(metric.table_name)}
        avatar={<Storage />}
        action={
          <Tooltip title={`Data Quality: ${metric.data_quality_status}`}>
            {getStatusIcon(metric.data_quality_status)}
          </Tooltip>
        }
      />
      <CardContent>
        <Box mb={2}>
          <Typography variant="h4" component="div" color="primary">
            {metric.record_count.toLocaleString()}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Records
          </Typography>
        </Box>

        {metric.data_quality_score !== undefined && (
          <Box mb={2}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Quality Score: {metric.data_quality_score.toFixed(1)}%
            </Typography>
            <Chip
              label={metric.data_quality_status.toUpperCase()}
              color={getStatusColor(metric.data_quality_status) as any}
              size="small"
            />
          </Box>
        )}

        {metric.last_updated && (
          <Typography variant="caption" color="text.secondary">
            Last Updated: {new Date(metric.last_updated).toLocaleDateString()}
          </Typography>
        )}
      </CardContent>
    </Card>
  );

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight={200}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent>
          <Typography color="error">
            Failed to load table metrics: {(error as Error).message}
          </Typography>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader title="Table Metrics" />
      <CardContent>
        <Grid container spacing={3}>
          {tableMetrics?.metrics.map((metric) => (
            <Grid item xs={12} sm={6} md={4} key={metric.table_name}>
              <TableMetricCard metric={metric} />
            </Grid>
          ))}
        </Grid>
        
        {tableMetrics && (
          <Box mt={2}>
            <Typography variant="caption" color="text.secondary">
              Last updated: {new Date(tableMetrics.updated_at).toLocaleString()}
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default TableMetricsGrid;
