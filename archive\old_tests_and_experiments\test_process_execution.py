#!/usr/bin/env python3
"""Test process execution functionality"""
import requests
import json
import time

def test_process_execution():
    base_url = "http://localhost:8000"
    
    print("🚀 Testing HKEX Process Execution")
    print("=" * 50)
    
    # Test 1: Get process types
    print("\n1️⃣ Getting available process types...")
    try:
        response = requests.get(f"{base_url}/api/v1/processes/types", timeout=10)
        if response.status_code == 200:
            data = response.json()
            process_types = data.get('process_types', [])
            print(f"✅ Found {len(process_types)} process types:")
            for pt in process_types:
                print(f"   - {pt['value']}: {pt['label']}")
        else:
            print(f"❌ Failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    # Test 2: Test process start (dry run)
    print("\n2️⃣ Testing process start (dry run)...")
    try:
        payload = {
            "process_type": "update_index_options",
            "parameters": {
                "txn_date": "2024-12-20",
                "dry_run": True
            }
        }
        response = requests.post(f"{base_url}/api/v1/processes/start", json=payload, timeout=10)
        if response.status_code == 200:
            data = response.json()
            task_id = data.get('task_id')
            print(f"✅ Process started with task_id: {task_id}")
            
            # Monitor for a few seconds
            print("\n3️⃣ Monitoring process status...")
            for i in range(10):
                time.sleep(1)
                try:
                    status_response = requests.get(f"{base_url}/api/v1/processes/{task_id}/status", timeout=5)
                    if status_response.status_code == 200:
                        status_data = status_response.json()
                        status = status_data.get('status', 'unknown')
                        message = status_data.get('message', 'no message')
                        progress = status_data.get('progress', 0)
                        print(f"   Step {i+1}: {status} ({progress}%) - {message}")
                        
                        if status in ['completed', 'failed', 'cancelled']:
                            break
                    else:
                        print(f"   Step {i+1}: Status check failed ({status_response.status_code})")
                except Exception as e:
                    print(f"   Step {i+1}: Error checking status - {e}")
            
            return True
        else:
            print(f"❌ Failed to start process: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error starting process: {e}")
        return False

if __name__ == "__main__":
    success = test_process_execution()
    if success:
        print("\n🎉 Process execution test completed successfully!")
        print("\n✅ Backend is fully functional and ready for use")
        print("\n🌐 Dashboard should be accessible at:")
        print("   - Frontend: http://localhost:3000")
        print("   - Backend API: http://localhost:8000")
        print("   - API Docs: http://localhost:8000/docs")
    else:
        print("\n❌ Process execution test failed")
