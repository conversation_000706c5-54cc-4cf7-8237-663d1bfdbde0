#!/usr/bin/env python3
"""
Test script to verify the backend server can handle subprocess creation successfully.
"""

import requests
import json
import time

def test_backend_health():
    """Test that the backend server is running."""
    try:
        response = requests.get("http://127.0.0.1:8000/health")
        if response.status_code == 200:
            print("✅ Backend server is running")
            return True
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Failed to connect to backend: {e}")
        return False

def test_process_start():
    """Test starting a process via the API."""
    try:
        # Try to start a simple process
        payload = {
            "process_type": "update_index_options",
            "parameters": {
                "txn_date": "2024-01-01",
                "dry_run": True
            }
        }
        
        print("Starting a test process...")
        response = requests.post("http://127.0.0.1:8000/api/processes/start", json=payload)
        
        if response.status_code == 200:
            result = response.json()
            task_id = result.get('task_id')
            print(f"✅ Process started successfully with task_id: {task_id}")
            
            # Wait a moment and check process status
            time.sleep(2)
            status_response = requests.get(f"http://127.0.0.1:8000/api/processes/{task_id}/status")
            if status_response.status_code == 200:
                status = status_response.json()
                print(f"Process status: {status.get('status', 'unknown')}")
                print(f"Process info: {status.get('info', 'no info')}")
                return True
            else:
                print(f"❌ Failed to get process status: {status_response.status_code}")
                return False
        else:
            print(f"❌ Failed to start process: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Process start test failed: {e}")
        return False

def main():
    """Main test function."""
    print("=" * 60)
    print("Backend Subprocess Fix Verification Test")
    print("=" * 60)
    
    # Test backend health
    if not test_backend_health():
        print("❌ Backend is not running. Please start it first.")
        return
    
    # Test process start
    if test_process_start():
        print("\n🎉 All tests passed! Windows subprocess fix is working in the backend!")
    else:
        print("\n❌ Process start test failed. Check the backend logs.")

if __name__ == "__main__":
    main()
