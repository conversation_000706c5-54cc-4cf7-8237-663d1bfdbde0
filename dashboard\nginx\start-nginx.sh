#!/bin/sh

# Set default values for ports if not provided
export BACKEND_PORT=${BACKEND_PORT:-8004}
export FRONTEND_PORT=${FRONTEND_PORT:-3080}

echo "Generating nginx.conf with BACKEND_PORT=${BACKEND_PORT}, FRONTEND_PORT=${FRONTEND_PORT}"

# Generate nginx.conf from template
envsubst '${BACKEND_PORT} ${FRONTEND_PORT}' < /etc/nginx/nginx.conf.template > /etc/nginx/nginx.conf

echo "Generated nginx configuration:"
cat /etc/nginx/nginx.conf

# Start nginx
exec nginx -g "daemon off;"
