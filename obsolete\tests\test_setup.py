#!/usr/bin/env python3
"""
Test script to verify the HKEX Dashboard setup and process orchestrator functionality.
"""

import sys
import os
import asyncio
from pathlib import Path

# Add the backend app to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend', 'app'))

async def test_orchestrator():
    """Test the process orchestrator functionality."""
    try:
        from services.process_orchestrator import orchestrator
        
        print("✅ Process Orchestrator imported successfully")
        
        # Test script discovery
        scripts_to_test = [
            'UpdateIndexOptionPostgres.py',
            'UpdateStockOptionReportPostgres.py', 
            'copyViewMultiDB.py'
        ]
        
        print("\n🔍 Testing script discovery:")
        for script in scripts_to_test:
            try:
                script_path = orchestrator.get_script_path(script)
                print(f"  ✅ {script} found at: {script_path}")
            except FileNotFoundError as e:
                print(f"  ❌ {script} not found: {e}")
        
        # Test process types
        print("\n📋 Available process types:")
        process_types = orchestrator.get_process_types()
        for process_type, config in process_types.items():
            print(f"  - {process_type}: {config['description']}")
            print(f"    Required params: {config['required_parameters']}")
        
        print("\n✅ Process Orchestrator test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Process Orchestrator test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_websocket_manager():
    """Test the WebSocket manager."""
    try:
        from websocket.manager import manager, connection_manager
        print("✅ WebSocket managers imported successfully")
        
        # Test broadcasting (should work even with no connections)
        await manager.broadcast_system_alert({"test": "alert"})
        print("✅ WebSocket broadcast test completed")
        return True
        
    except Exception as e:
        print(f"❌ WebSocket manager test failed: {e}")
        return False

def test_config():
    """Test configuration loading."""
    try:
        from core.config import settings
        print("✅ Configuration loaded successfully")
        print(f"  Project name: {settings.project_name}")
        print(f"  Environment: {settings.environment}")
        print(f"  Debug mode: {settings.debug}")
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

async def main():
    """Run all tests."""
    print("🚀 HKEX Dashboard Setup Test")
    print("=" * 50)
    
    results = []
    
    # Test configuration
    print("\n1. Testing Configuration...")
    results.append(test_config())
    
    # Test WebSocket manager
    print("\n2. Testing WebSocket Manager...")
    results.append(await test_websocket_manager())
    
    # Test process orchestrator
    print("\n3. Testing Process Orchestrator...")
    results.append(await test_orchestrator())
    
    # Summary
    print("\n" + "=" * 50)
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"🎉 All tests passed! ({passed}/{total})")
        print("\n✅ HKEX Dashboard is ready for development!")
    else:
        print(f"⚠️  Some tests failed ({passed}/{total} passed)")
        print("\n🔧 Please check the errors above and fix the issues.")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
