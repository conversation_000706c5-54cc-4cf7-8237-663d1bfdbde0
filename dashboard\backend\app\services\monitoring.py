from datetime import datetime
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import text
import psutil
import os
import redis
from ..core.config import settings
from ..models.schemas import SystemHealthResponse, TableMetric, TableMetricsResponse

class MonitoringService:
    def __init__(self, db_session: Session):
        self.db = db_session
        self.redis_client = redis.from_url(settings.redis_url)
    
    async def get_system_health(self) -> SystemHealthResponse:
        """Get overall system health status"""
        try:
            # Test database connection
            self.db.execute(text("SELECT 1"))
            db_connected = True
        except Exception:
            db_connected = False
        
        try:
            # Test Redis connection
            self.redis_client.ping()
            redis_connected = True
        except Exception:
            redis_connected = False
        
        # Get last processing date
        try:
            result = self.db.execute(text("""
                SELECT MAX(txn_date) as last_date 
                FROM option_daily_report
            """))
            last_date = result.scalar()
            last_processing_date = last_date.strftime('%Y-%m-%d') if last_date else None
        except Exception:
            last_processing_date = None
        
        # Get active processes count
        active_processes = len([p for p in psutil.process_iter() if 'python' in p.name().lower()])
        
        # System metrics
        disk_usage = psutil.disk_usage('/')
        memory = psutil.virtual_memory()
        
        return SystemHealthResponse(
            database_connection=db_connected,
            redis_connection=redis_connected,
            last_processing_date=last_processing_date,
            active_processes=active_processes,
            disk_space_gb=disk_usage.free / (1024**3),
            memory_usage_percent=memory.percent
        )
    
    async def get_table_metrics(self) -> TableMetricsResponse:
        """Get metrics for all important tables"""
        tables = [
            'option_daily_report',
            'option_greeks',
            'stock_option_daily_report',
            'index_option_details',
            'equity_option_details'
        ]
        
        metrics = []
        
        for table in tables:
            try:
                # Get record count
                count_result = self.db.execute(text(f"SELECT COUNT(*) FROM {table}"))
                record_count = count_result.scalar()
                
                # Get last updated (assuming txn_date exists)
                try:
                    date_result = self.db.execute(text(f"SELECT MAX(txn_date) FROM {table}"))
                    last_updated = date_result.scalar()
                except Exception:
                    last_updated = None
                
                # Calculate basic data quality score
                quality_score = await self._calculate_table_quality_score(table)
                
                metrics.append(TableMetric(
                    table_name=table,
                    record_count=record_count,
                    last_updated=last_updated,
                    data_quality_score=quality_score,
                    data_quality_status=self._get_quality_status(quality_score)
                ))
            except Exception as e:
                # Table might not exist or other error
                metrics.append(TableMetric(
                    table_name=table,
                    record_count=0,
                    data_quality_score=0.0,
                    data_quality_status="error"
                ))
        
        return TableMetricsResponse(
            metrics=metrics,
            updated_at=datetime.utcnow()
        )
    
    async def _calculate_table_quality_score(self, table_name: str) -> float:
        """Calculate a basic data quality score for a table"""
        try:
            # Check for recent data (last 7 days)
            recent_data_query = text(f"""
                SELECT COUNT(*) FROM {table_name} 
                WHERE txn_date >= CURRENT_DATE - INTERVAL '7 days'
            """)
            recent_count = self.db.execute(recent_data_query).scalar()
            
            # Check for null values in key columns
            if table_name == 'option_daily_report':
                null_check_query = text(f"""
                    SELECT COUNT(*) FROM {table_name} 
                    WHERE inst_name IS NULL OR settlement_price IS NULL
                """)
            else:
                null_check_query = text(f"SELECT 0")
            
            null_count = self.db.execute(null_check_query).scalar()
            
            # Get total count
            total_count = self.db.execute(text(f"SELECT COUNT(*) FROM {table_name}")).scalar()
            
            if total_count == 0:
                return 0.0
            
            # Calculate score
            recent_score = min(recent_count / max(total_count * 0.1, 1), 1.0) * 50
            quality_score = max(100 - (null_count / total_count * 100), 0) * 0.5
            
            return min(recent_score + quality_score, 100.0)
            
        except Exception:
            return 0.0
    
    def _get_quality_status(self, score: Optional[float]) -> str:
        """Convert quality score to status"""
        if score is None or score < 50:
            return "error"
        elif score < 80:
            return "warning"
        else:
            return "good"
