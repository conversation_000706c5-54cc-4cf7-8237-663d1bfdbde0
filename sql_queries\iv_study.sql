select *, left(right(inst_name, 7), 5) as strike,
ln(stock_price/ CAST (left(right(inst_name, 7), 5) as int)) as moneyness
from option_daily_report where txn_date = '2023-08-25'
and ln(stock_price/ CAST (left(right(inst_name, 7), 5) as int)) between -0.005 and 0.005
and left(inst_name, 10) ='HSI.DEC-23'
order by inst_name
limit 100


select *, left(right(inst_name, 7), 5) as strike,
ln(stock_price/ CAST (left(right(inst_name, 7), 5) as int)) as moneyness
from option_daily_report where txn_date = '2023-08-25'
and delta between 0.45 and  0.55
and left(inst_name, 10) ='HSI.DEC-23'
order by inst_name
limit 100

select * from option_daily_report a, option_daily_strikedg b, 
ln(a.stock_price / b.strike) as moneyness
where a.txn_id=b.txn_id
and a.txn_date = '2023-08-24'
and a.delta between 0.45 and  0.55
limit 100