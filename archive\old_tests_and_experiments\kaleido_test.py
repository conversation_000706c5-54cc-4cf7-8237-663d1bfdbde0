#%%
# Comprehensive Kaleido testing and troubleshooting script
import os
import sys
import platform
import traceback
import plotly
import plotly.io as pio
import plotly.graph_objects as go

print(f"Python version: {sys.version}")
print(f"Platform: {platform.platform()}")
print(f"Plotly version: {plotly.__version__}")

# 1. Configure Kaleido settings for maximum compatibility
# Increase timeout to 3 minutes (extremely generous)
pio.kaleido.scope.timeout = 180

# Disable hardware acceleration and other potentially problematic features
pio.kaleido.scope.chromium_args = (
    "--disable-gpu",            # Disable GPU hardware acceleration
    "--disable-dev-shm-usage",  # Overcome limited resource problems
    "--disable-extensions",     # Disable extensions
    "--disable-web-security",   # Disable web security
    "--no-sandbox"              # CAUTION: Only use in trusted environments!
)

# Disable MathJax to speed up rendering if not needed
pio.kaleido.scope.mathjax = None

# 2. Create a simple test figure
fig = go.Figure(data=[go.Bar(y=[2, 3, 1])])
fig.update_layout(title="Kaleido Test Figure")

# 3. First, try saving as HTML to verify the figure itself works
print("\n--- HTML Export Test ---")
try:
    html_path = "kaleido_test.html"
    fig.write_html(html_path)
    print(f"✓ HTML export successful: {os.path.abspath(html_path)}")
except Exception as e:
    print(f"❌ HTML export failed: {e}")
    traceback.print_exc()

# 4. Try PNG export with detailed error reporting
print("\n--- PNG Export Test ---")
try:
    png_path = "kaleido_test.png"
    print("Starting PNG export (this might take a while)...")
    fig.write_image(png_path, engine="kaleido")
    print(f"✓ PNG export successful: {os.path.abspath(png_path)}")
except Exception as e:
    print(f"❌ PNG export failed: {e}")
    traceback.print_exc()

# 5. Alternative: Try SVG which is sometimes more reliable
print("\n--- SVG Export Test ---")
try:
    svg_path = "kaleido_test.svg"
    fig.write_image(svg_path, engine="kaleido")
    print(f"✓ SVG export successful: {os.path.abspath(svg_path)}")
except Exception as e:
    print(f"❌ SVG export failed: {e}")
    traceback.print_exc()

# %%
