<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Test</title>
</head>
<body>
    <h1>WebSocket Connection Test</h1>
    <div id="status">Connecting...</div>
    <div id="messages"></div>
    
    <script>
        const statusDiv = document.getElementById('status');
        const messagesDiv = document.getElementById('messages');
        
        function log(message) {
            console.log(message);
            messagesDiv.innerHTML += '<div>' + new Date().toISOString() + ': ' + message + '</div>';
        }
        
        // Test different WebSocket URLs
        const urls = [
            'ws://localhost:8004/ws',
            'ws://localhost:3080/ws'
        ];
        
        async function testWebSocket(url) {
            log('Testing: ' + url);
            
            try {
                const ws = new WebSocket(url);
                
                ws.onopen = function() {
                    log('✅ Connected to: ' + url);
                    statusDiv.textContent = 'Connected to: ' + url;
                    
                    // Send a ping message
                    ws.send(JSON.stringify({
                        type: 'ping',
                        timestamp: new Date().toISOString()
                    }));
                    log('📤 Sent ping message');
                };
                
                ws.onmessage = function(event) {
                    log('📥 Received: ' + event.data);
                };
                
                ws.onerror = function(error) {
                    log('❌ Error on ' + url + ': ' + error);
                };
                
                ws.onclose = function(event) {
                    log('🔌 Closed ' + url + ' - Code: ' + event.code + ', Reason: ' + event.reason);
                };
                
                // Close after 5 seconds
                setTimeout(() => {
                    if (ws.readyState === WebSocket.OPEN) {
                        ws.close();
                    }
                }, 5000);
                
            } catch (error) {
                log('❌ Failed to create WebSocket for ' + url + ': ' + error);
            }
        }
        
        // Test each URL with a delay
        urls.forEach((url, index) => {
            setTimeout(() => testWebSocket(url), index * 6000);
        });
    </script>
</body>
</html>
