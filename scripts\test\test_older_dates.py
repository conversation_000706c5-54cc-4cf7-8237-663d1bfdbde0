"""
Test with older HKEX report date that should definitely exist
"""
import datetime as dt
from hkex_fetcher import safe_http_get_with_firecrawl_fallback

def test_with_older_date():
    """Test with an older date that should definitely have data"""
    
    # Use a date from last year that should definitely exist
    older_dates = [
        "https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/dqe250715.htm"
    ]
    
    print("🕰️ TESTING WITH OLDER DATES (Should have data)")
    print("=" * 60)
    
    for i, test_url in enumerate(older_dates, 1):
        print(f"{i}️⃣ Testing: {test_url}")
        print("-" * 50)
        
        try:
            response = safe_http_get_with_firecrawl_fallback(test_url, timeout=45)
            
            if response and response.status_code == 200:
                content_sample = response.text[:300].replace('\n', ' ')
                
                # Check for various indicators
                if 'ERR_HTTP2_PROTOCOL_ERROR' in response.text:
                    print("❌ Still getting HTTP/2 protocol error")
                elif 'trading day' in response.text.lower():
                    print("✅ SUCCESS: Found 'trading day' - Real HKEX content!")
                    print(f"Content sample: {content_sample}...")
                    break  # Success! No need to test more
                elif 'hong kong exchanges' in response.text.lower():
                    print("✅ SUCCESS: Found 'hong kong exchanges' - Real HKEX content!")
                    print(f"Content sample: {content_sample}...")
                    break  # Success!
                elif 'derivatives market' in response.text.lower():
                    print("✅ SUCCESS: Found 'derivatives market' - Real HKEX content!")
                    print(f"Content sample: {content_sample}...")
                    break  # Success!
                elif len(response.content) > 50000:  # Large file suggests real data
                    print("✅ LIKELY SUCCESS: Large file size suggests real data")
                    print(f"File size: {len(response.content)} bytes")
                    print(f"Content sample: {content_sample}...")
                    break  # Likely success!
                else:
                    print("⚠️ Got 200 response but content unclear")
                    print(f"Content sample: {content_sample}...")
            elif response and response.status_code == 404:
                print("❌ 404 Not Found - This date may not exist")
            elif response:
                print(f"❌ HTTP {response.status_code}")
            else:
                print("❌ No response received")
                
        except Exception as e:
            print(f"❌ Exception: {e}")
        
        print()
    
    print("🎯 ANALYSIS:")
    print("- If any older date worked, the fetcher is functional")
    print("- If all failed, HKEX may have IP-based blocking")
    print("- Consider using VPN or proxy if all dates fail")
    print("- July 18, 2025 may not exist yet (future date)")

if __name__ == "__main__":
    test_with_older_date()
