# HKEX Option Report Processing Dashboard
## Product Requirements Document (PRD)

### Document Information
- **Product Name**: HKEX Dashboard
- **Version**: 1.0
- **Date**: May 24, 2025
- **Author**: GitHub Copilot
- **Document Type**: Product Requirements Document

---

## 1. Executive Summary

### 1.1 Product Overview
The HKEX Dashboard is a comprehensive web-based monitoring and orchestration platform for the Hong Kong Exchange Option Report Processing System. It provides real-time visibility into data pipeline execution, data quality monitoring, and troubleshooting capabilities for the automated option data processing workflows.

### 1.2 Business Objectives
- **Operational Visibility**: Provide real-time monitoring of data pipeline execution status
- **Data Quality Assurance**: Enable proactive identification and resolution of data processing issues
- **Process Optimization**: Streamline workflow orchestration and reduce manual intervention
- **Risk Management**: Ensure timely and accurate option data processing for risk analysis

### 1.3 Target Users
- **Primary**: Data Engineers and System Administrators
- **Secondary**: Quantitative Analysts and Risk Managers
- **Tertiary**: Business Stakeholders requiring system status updates

---

## 2. System Architecture

### 2.1 Current Data Pipeline Components
The dashboard will orchestrate and monitor three critical processing scripts:

#### 2.1.1 UpdateIndexOptionPostgres.py
- **Purpose**: Downloads and processes HKEX index option reports (HSI, HHI, HTI, MHI)
- **Key Functions**: 
  - `getDailyReport()` - Main orchestration
  - `fetchHKEXReport()` - Downloads HTML reports
  - `parseHKEXReport()` - Extracts option data
  - `ProcessStrikeDG()` - Calculates option Greeks
- **Output Tables**: `option_daily_report`, `option_daily_strikedg`, `t_index_delta_all_strikes`
- **Materialized Views**: `index_daily_iv`

#### 2.1.2 UpdateStockOptionReportPostgres.py
- **Purpose**: Processes stock option data with Black-Scholes calculations
- **Key Functions**:
  - `getStockOptionReport()` - Main processing function
  - `ProcessSOStrikeDG()` - Greeks grid generation
  - `updateDeltaAllStrikes()` - Delta aggregation
- **Output Tables**: `stock_option_report`, `stock_option_strikeDG`, `t_delta_all_strikes`
- **Materialized Views**: `option_daily_iv`, `option_daily_volume`

#### 2.1.3 copyViewMultiDB.py
- **Purpose**: Synchronizes data between local and remote databases
- **Key Views**: `v_index_option_value`, `v_weekly_option_value`, `v_stock_option_value`, `v_weekly_iv`, `v_monthly_iv`
- **Remote Databases**: CC_DATABASE, AIVEN_DATABASE_URL, NEON_DATABASE_URL

### 2.2 Database Schema Overview
```
Core Tables:
├── option_daily_report (Index options)
├── weekly_option_daily_report (Weekly index options)  
├── stock_option_report (Stock options)
├── stock_option_strikeDG (Option Greeks)
├── t_delta_all_strikes (Aggregated delta positions)
├── t_index_delta_all_strikes (Index delta aggregations)
└── t_weekly_delta_all_strikes (Weekly delta aggregations)

Materialized Views:
├── option_daily_iv (Implied volatility)
├── option_daily_volume (Volume metrics)
└── index_daily_iv (Index implied volatility)

Analytical Views:
├── v_index_option_value (Index option analysis)
├── v_weekly_option_value (Weekly option analysis)
├── v_stock_option_value (Stock option analysis)
├── v_weekly_iv (Weekly implied volatility)
└── v_monthly_iv (Monthly implied volatility)
```

---

## 3. Functional Requirements

### 3.1 Pipeline Orchestration
#### 3.1.1 Process Management
- **REQ-001**: Execute processing scripts in proper sequence
- **REQ-002**: Support manual and scheduled execution modes
- **REQ-003**: Provide process start/stop/restart capabilities
- **REQ-004**: Handle script dependencies and prerequisites
- **REQ-005**: Support parallel execution where appropriate

#### 3.1.2 Execution Control
- **REQ-006**: Allow selection of date ranges for reprocessing
- **REQ-007**: Support selective processing (index only, stock only, etc.)
- **REQ-008**: Provide dry-run capabilities for testing
- **REQ-009**: Enable process scheduling and automation
- **REQ-010**: Support emergency stop functionality

### 3.2 Monitoring and Visualization
#### 3.2.1 Real-time Status Dashboard
- **REQ-011**: Display current processing status for all pipelines
- **REQ-012**: Show execution progress with percentage completion
- **REQ-013**: Display estimated time remaining for active processes
- **REQ-014**: Provide color-coded status indicators (Running, Success, Error, Warning)
- **REQ-015**: Show last successful execution timestamp

#### 3.2.2 Data Quality Metrics
- **REQ-016**: Display record counts for each major table by transaction date
- **REQ-017**: Show data freshness indicators (last update time)
- **REQ-018**: Provide data completeness metrics (expected vs. actual records)
- **REQ-019**: Display processing performance metrics (execution time, throughput)
- **REQ-020**: Show materialized view refresh status

#### 3.2.3 Historical Trends
- **REQ-021**: Visualize processing volume trends over time
- **REQ-022**: Display success/failure rate charts
- **REQ-023**: Show execution time trends and performance degradation
- **REQ-024**: Provide data growth metrics by table/view
- **REQ-025**: Support customizable time range selection

### 3.3 Data Inspection and Analysis
#### 3.3.1 Table/View Browser
- **REQ-026**: Browse and search all major tables and views
- **REQ-027**: Display sample records with column metadata
- **REQ-028**: Provide data export capabilities (CSV, Excel)
- **REQ-029**: Support SQL query execution interface
- **REQ-030**: Enable data comparison between different dates

#### 3.3.2 Data Validation
- **REQ-031**: Validate data integrity across related tables
- **REQ-032**: Check for missing or duplicate records
- **REQ-033**: Verify calculation accuracy for option Greeks
- **REQ-034**: Monitor for unusual data patterns or outliers
- **REQ-035**: Provide data reconciliation reports

### 3.4 Error Handling and Troubleshooting
#### 3.4.1 Error Detection
- **REQ-036**: Detect and categorize processing errors
- **REQ-037**: Monitor database connection issues
- **REQ-038**: Identify data quality problems
- **REQ-039**: Track materialized view refresh failures
- **REQ-040**: Monitor remote database synchronization issues

#### 3.4.2 Error Resolution
- **REQ-041**: Provide detailed error logs and stack traces
- **REQ-042**: Suggest automatic resolution actions where possible
- **REQ-043**: Support manual error override capabilities
- **REQ-044**: Enable selective reprocessing of failed components
- **REQ-045**: Provide error notification and alerting

### 3.5 System Administration
#### 3.5.1 Configuration Management
- **REQ-046**: Manage database connection settings
- **REQ-047**: Configure processing schedules and parameters
- **REQ-048**: Set up monitoring thresholds and alerts
- **REQ-049**: Manage user access and permissions
- **REQ-050**: Support backup and restore operations

#### 3.5.2 Performance Monitoring
- **REQ-051**: Monitor system resource utilization
- **REQ-052**: Track database performance metrics
- **REQ-053**: Monitor network connectivity to remote databases
- **REQ-054**: Provide capacity planning insights
- **REQ-055**: Generate system health reports

---

## 4. Technical Requirements

### 4.1 Technology Stack
#### 4.1.1 Backend Framework
- **Python 3.8+** with Flask/FastAPI for REST API
- **SQLAlchemy 2.0** for database operations
- **PostgreSQL** as primary database
- **Celery** for background task processing
- **Redis** for caching and task queue

#### 4.1.2 Frontend Framework
- **React.js** with TypeScript for modern UI
- **Material-UI** or **Ant Design** for component library
- **Chart.js** or **Plotly.js** for data visualization
- **WebSocket** for real-time updates

#### 4.1.3 Infrastructure
- **Docker** for containerization
- **Docker Compose** for local development
- **nginx** for reverse proxy and static file serving

### 4.2 Performance Requirements
- **REQ-056**: Dashboard should load within 3 seconds
- **REQ-057**: Real-time updates should occur within 5 seconds
- **REQ-058**: Support concurrent access by up to 10 users
- **REQ-059**: Handle datasets with up to 1M records efficiently
- **REQ-060**: Maintain 99.5% uptime during business hours

### 4.3 Security Requirements
- **REQ-061**: Implement role-based access control
- **REQ-062**: Use HTTPS for all communications
- **REQ-063**: Secure database credentials with environment variables
- **REQ-064**: Implement session management and timeout
- **REQ-065**: Audit log all administrative actions

### 4.4 Integration Requirements
- **REQ-066**: Integrate with existing .env configuration
- **REQ-067**: Support existing database schema without modifications
- **REQ-068**: Maintain compatibility with current processing scripts
- **REQ-069**: Provide API endpoints for external monitoring tools
- **REQ-070**: Support email/SMS notifications

---

## 5. User Interface Requirements

### 5.1 Dashboard Layout
#### 5.1.1 Main Dashboard
- **Header**: Navigation menu, user info, system status indicator
- **Sidebar**: Quick access to major functions
- **Main Content**: 
  - Pipeline status cards
  - Key metrics summary
  - Recent activity timeline
  - Quick action buttons

#### 5.1.2 Navigation Structure
```
Dashboard
├── Overview (Main dashboard)
├── Pipeline Management
│   ├── Process Control
│   ├── Execution History
│   └── Scheduling
├── Data Monitoring
│   ├── Table Browser
│   ├── Record Counts
│   ├── Data Quality
│   └── Trends Analysis
├── Troubleshooting
│   ├── Error Logs
│   ├── System Health
│   └── Diagnostics
└── Administration
    ├── Configuration
    ├── User Management
    └── System Settings
```

### 5.2 Responsive Design
- **REQ-071**: Support desktop (1920x1080) and tablet (1024x768) resolutions
- **REQ-072**: Optimize for Chrome, Firefox, and Edge browsers
- **REQ-073**: Ensure accessibility compliance (WCAG 2.1)

---

## 6. Data Model

### 6.1 Dashboard-Specific Tables
```sql
-- Process execution tracking
CREATE TABLE process_execution_log (
    id SERIAL PRIMARY KEY,
    process_name VARCHAR(100) NOT NULL,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    status VARCHAR(20) NOT NULL, -- 'running', 'success', 'error', 'cancelled'
    parameters JSONB,
    error_message TEXT,
    records_processed INTEGER,
    execution_duration INTERVAL
);

-- System health metrics
CREATE TABLE system_health_log (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMP NOT NULL DEFAULT NOW(),
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(10,2),
    metric_unit VARCHAR(20)
);

-- Data quality checks
CREATE TABLE data_quality_log (
    id SERIAL PRIMARY KEY,
    table_name VARCHAR(100) NOT NULL,
    check_name VARCHAR(100) NOT NULL,
    check_timestamp TIMESTAMP NOT NULL DEFAULT NOW(),
    expected_count INTEGER,
    actual_count INTEGER,
    status VARCHAR(20) NOT NULL, -- 'pass', 'warning', 'error'
    details TEXT
);
```

### 6.2 Monitoring Views
```sql
-- Daily processing summary
CREATE VIEW v_daily_processing_summary AS
SELECT 
    txn_date,
    SUM(CASE WHEN table_name = 'option_daily_report' THEN record_count ELSE 0 END) as index_options,
    SUM(CASE WHEN table_name = 'stock_option_report' THEN record_count ELSE 0 END) as stock_options,
    SUM(CASE WHEN table_name = 'weekly_option_daily_report' THEN record_count ELSE 0 END) as weekly_options
FROM (
    SELECT txn_date, 'option_daily_report' as table_name, COUNT(*) as record_count FROM option_daily_report GROUP BY txn_date
    UNION ALL
    SELECT txn_date, 'stock_option_report' as table_name, COUNT(*) as record_count FROM stock_option_report GROUP BY txn_date
    UNION ALL
    SELECT txn_date, 'weekly_option_daily_report' as table_name, COUNT(*) as record_count FROM weekly_option_daily_report GROUP BY txn_date
) t
GROUP BY txn_date
ORDER BY txn_date DESC;
```

---

## 7. API Specification

### 7.1 REST API Endpoints
#### 7.1.1 Process Management
```
POST /api/processes/start
POST /api/processes/{process_id}/stop
GET  /api/processes/status
GET  /api/processes/{process_id}/logs
POST /api/processes/schedule
```

#### 7.1.2 Data Monitoring
```
GET  /api/tables/{table_name}/count
GET  /api/tables/{table_name}/sample
GET  /api/monitoring/summary
GET  /api/monitoring/trends
POST /api/monitoring/validate
```

#### 7.1.3 System Health
```
GET  /api/health/status
GET  /api/health/metrics
GET  /api/health/database
GET  /api/health/errors
```

### 7.2 WebSocket Events
```javascript
// Real-time process updates
{
  "event": "process_update",
  "data": {
    "process_id": "update_index_options",
    "status": "running",
    "progress": 75,
    "eta": "2025-05-24T15:30:00Z"
  }
}

// Data quality alerts
{
  "event": "data_quality_alert",
  "data": {
    "table": "option_daily_report",
    "issue": "missing_records",
    "severity": "warning",
    "details": "Expected 5000 records, found 4850"
  }
}
```

---

## 8. Success Metrics

### 8.1 Operational Metrics
- **Processing Success Rate**: >99% successful pipeline executions
- **Data Completeness**: >99.5% of expected records processed daily
- **Processing Time**: <2 hours for full daily processing cycle
- **Error Resolution Time**: <30 minutes average time to resolution

### 8.2 User Experience Metrics
- **Dashboard Load Time**: <3 seconds average page load
- **User Adoption**: >90% of target users actively using dashboard monthly
- **Error Detection Speed**: Issues identified within 5 minutes of occurrence
- **User Satisfaction**: >4.5/5 rating from user feedback surveys

### 8.3 System Reliability Metrics
- **Uptime**: >99.5% availability during business hours
- **Data Accuracy**: >99.9% accuracy in calculated option Greeks
- **Sync Success**: >99% successful remote database synchronizations
- **Alert Response Time**: <1 minute for critical system alerts

---

## 9. Risk Assessment

### 9.1 Technical Risks
| Risk | Probability | Impact | Mitigation Strategy |
|------|-------------|--------|-------------------|
| Database performance degradation | Medium | High | Implement query optimization and indexing |
| Processing script failures | Medium | High | Add robust error handling and retry logic |
| Remote database connectivity issues | High | Medium | Implement circuit breakers and failover |
| Data integrity corruption | Low | Very High | Add comprehensive validation checks |

### 9.2 Operational Risks
| Risk | Probability | Impact | Mitigation Strategy |
|------|-------------|--------|-------------------|
| User training requirements | High | Medium | Develop comprehensive documentation and training |
| Change management resistance | Medium | Medium | Involve users in design and gradual rollout |
| Maintenance complexity | Medium | High | Implement automated testing and deployment |
| Scaling limitations | Low | High | Design for horizontal scaling from start |

---

## 10. Implementation Phases

### 10.1 Phase 1: Foundation (Weeks 1-2)
- Set up development environment
- Implement basic dashboard structure
- Create database monitoring APIs
- Develop process execution tracking

### 10.2 Phase 2: Core Features (Weeks 3-4)
- Build main dashboard interface
- Implement process orchestration
- Add real-time monitoring capabilities
- Create basic data visualization

### 10.3 Phase 3: Advanced Features (Weeks 5-6)
- Implement data quality monitoring
- Add troubleshooting tools
- Build historical analysis features
- Create notification system

### 10.4 Phase 4: Polish & Production (Weeks 7-8)
- Implement security features
- Add comprehensive testing
- Create documentation and training materials
- Deploy to production environment

---

## 11. Appendices

### 11.1 Glossary
- **Greeks**: Option sensitivity measures (Delta, Gamma, Theta, Vega, Rho)
- **HKEX**: Hong Kong Exchange
- **IV**: Implied Volatility
- **OI**: Open Interest
- **Materialized View**: Pre-computed database view for performance

### 11.2 References
- HKEX Official Documentation
- Black-Scholes Option Pricing Model
- SQLAlchemy 2.0 Documentation
- PostgreSQL Performance Best Practices

---

**Document Control**
- Version: 1.0
- Last Updated: May 24, 2025
- Next Review: June 24, 2025
- Approval: Pending
