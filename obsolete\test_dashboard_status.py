#!/usr/bin/env python3
"""
HKEX Dashboard Application Test Suite
Tests the current state of the application and identifies any issues.
"""

import sys
import os
import traceback
import json
from pathlib import Path

def test_backend_imports():
    """Test if backend can import without errors"""
    print("🧪 Testing Backend Imports...")
    try:
        # Add backend to path
        backend_path = Path(__file__).parent / "backend"
        sys.path.insert(0, str(backend_path))
        
        # Test main app import
        from app.main import app
        print("✅ Backend imports successfully")
        
        # Test orchestrator
        from app.services.simple_orchestrator import orchestrator
        print("✅ Orchestrator imports successfully")
        
        # Test routes
        from app.api.routes.processes import router
        print("✅ API routes import successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Backend import failed: {e}")
        traceback.print_exc()
        return False

def test_frontend_structure():
    """Test if frontend has proper structure"""
    print("\n🧪 Testing Frontend Structure...")
    
    frontend_path = Path(__file__).parent / "frontend"
    
    # Check key files
    key_files = [
        "package.json",
        "src/App.tsx", 
        "src/components/ProcessStarter.tsx",
        "src/components/RealTimeLogViewer.tsx",
        "src/components/ProcessHistory.tsx"
    ]
    
    missing_files = []
    for file_path in key_files:
        full_path = frontend_path / file_path
        if not full_path.exists():
            missing_files.append(file_path)
        else:
            print(f"✅ Found: {file_path}")
    
    if missing_files:
        print(f"❌ Missing files: {missing_files}")
        return False
    
    print("✅ Frontend structure is complete")
    return True

def test_critical_fixes():
    """Test if critical fixes are in place"""
    print("\n🧪 Testing Critical Fixes...")
    
    # Check the .value property access fix in App.tsx
    app_tsx_path = Path(__file__).parent / "frontend" / "src" / "App.tsx"
    
    if not app_tsx_path.exists():
        print("❌ App.tsx not found")
        return False
    
    content = app_tsx_path.read_text()
    
    # Check if the fix for .value property access is present
    if "pType && pType.value" in content:
        print("✅ Property access safety check found in App.tsx")
    else:
        print("❌ Property access safety check missing in App.tsx")
        return False
    
    # Check if protective code is in place
    if "console.warn('Invalid process type object found:" in content:
        print("✅ Invalid process type handling found")
    else:
        print("⚠️  Invalid process type warning not found")
    
    return True

def test_configuration():
    """Test configuration files"""
    print("\n🧪 Testing Configuration...")
    
    # Check backend config
    config_path = Path(__file__).parent / "backend" / "app" / "core" / "config.py"
    if config_path.exists():
        print("✅ Backend config found")
    else:
        print("❌ Backend config missing")
        return False
    
    # Check if .env files exist
    env_files = [".env.dev", ".env.example", ".env.production"]
    for env_file in env_files:
        env_path = Path(__file__).parent / env_file
        if env_path.exists():
            print(f"✅ Found: {env_file}")
        else:
            print(f"⚠️  Missing: {env_file}")
    
    return True

def test_scripts_integration():
    """Test if HKEX scripts are properly integrated"""
    print("\n🧪 Testing Scripts Integration...")
    
    scripts_path = Path(__file__).parent.parent / "scripts"
    
    key_scripts = [
        "UpdateIndexOptionPostgres.py",
        "UpdateStockOptionReportPostgres.py", 
        "copyViewMultiDB.py"
    ]
    
    missing_scripts = []
    for script in key_scripts:
        script_path = scripts_path / script
        if not script_path.exists():
            missing_scripts.append(script)
        else:
            print(f"✅ Found: {script}")
    
    if missing_scripts:
        print(f"❌ Missing scripts: {missing_scripts}")
        return False
    
    print("✅ All critical scripts found")
    return True

def main():
    """Run all tests"""
    print("🚀 HKEX Dashboard Application Test Suite")
    print("=" * 50)
    
    tests = [
        test_backend_imports,
        test_frontend_structure, 
        test_critical_fixes,
        test_configuration,
        test_scripts_integration
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"🎉 All tests passed! ({passed}/{total})")
        print("\n✅ HKEX Dashboard appears to be in working condition!")
        print("\n🚀 Next steps:")
        print("  1. Start backend: cd backend && python -m uvicorn app.main:app --reload")
        print("  2. Start frontend: cd frontend && npm start") 
        print("  3. Access dashboard at http://localhost:3000")
    else:
        print(f"⚠️  {passed}/{total} tests passed")
        if passed > total // 2:
            print("\n🔧 Dashboard is mostly functional but needs some fixes")
        else:
            print("\n🚨 Dashboard needs significant work")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
