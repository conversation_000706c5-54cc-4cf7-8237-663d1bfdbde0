#!/usr/bin/env python3
"""
Simple curl-like test to debug 400 error
"""

import requests
import json

def test_simple_request():
    """Test with a simple curl-like request"""
    
    url = "http://localhost:8001/api/v1/processes/start"
    
    # Test with exact format that should work
    data = {
        "process": "update_index_options",
        "parameters": {}
    }
    
    print(f"Making request to: {url}")
    print(f"Data: {json.dumps(data, indent=2)}")
    
    try:
        response = requests.post(
            url,
            json=data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        print(f"Response Body: {response.text}")
        
        if response.status_code == 400:
            print("\n=== 400 Bad Request Details ===")
            try:
                error_data = response.json()
                print(f"Error detail: {error_data}")
            except:
                print("Could not parse error as JSO<PERSON>")
                
    except requests.exceptions.ConnectError:
        print("Could not connect to server - is it running?")
    except requests.exceptions.Timeout:
        print("Request timed out")
    except Exception as e:
        print(f"Request failed: {e}")

if __name__ == "__main__":
    test_simple_request()
