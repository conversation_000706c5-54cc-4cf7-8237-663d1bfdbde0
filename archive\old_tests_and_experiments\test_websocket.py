#!/usr/bin/env python3
"""Test WebSocket functionality"""
import asyncio
import websockets
import json

async def test_websocket():
    uri = "ws://localhost:8000/ws"
    print("🔌 Testing WebSocket connection...")
    
    try:
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket connected successfully!")
            
            # Listen for messages for a few seconds
            print("👂 Listening for WebSocket messages...")
            try:
                await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print("✅ Received WebSocket message")
            except asyncio.TimeoutError:
                print("ℹ️  No messages received (this is normal if no processes are running)")
            
            return True
    except Exception as e:
        print(f"❌ WebSocket connection failed: {e}")
        return False

if __name__ == "__main__":
    result = asyncio.run(test_websocket())
