# Product Context - HKEX Dashboard

## Why This Dashboard Exists
The HKEX Option Report Processing System currently operates as a collection of standalone scripts without centralized monitoring or control. This creates several pain points:

1. **Lack of Visibility** - No way to see if daily processing completed successfully
2. **Manual Orchestration** - Scripts must be run manually in correct sequence
3. **Troubleshooting Difficulty** - When something fails, it's hard to diagnose the issue
4. **Data Quality Uncertainty** - No easy way to verify record counts and data completeness

## Problems It Solves

### For Data Analysts
- **Real-time Status** - See at a glance if today's data is ready for analysis
- **Historical Trends** - Track data volume and quality over time
- **Data Validation** - Quickly verify record counts match expectations

### For System Administrators  
- **Centralized Control** - Start/stop processes from one interface
- **Error Monitoring** - Immediate alerts when processes fail
- **Performance Tracking** - Identify bottlenecks and optimization opportunities

### For Risk Managers
- **Timely Updates** - Ensure option data is current for risk calculations
- **Data Completeness** - Verify all required symbols and dates are processed
- **Audit Trail** - Track when data was last updated and by whom

## How It Should Work

### Core User Workflows

1. **Daily Monitoring Workflow**
   - User opens dashboard at start of day
   - Sees status of overnight processing
   - Reviews any errors or warnings
   - Manually triggers missing data if needed

2. **Process Orchestration Workflow**
   - User schedules or manually starts data processing
   - Monitors real-time progress through the pipeline
   - Receives notifications when processing completes
   - Reviews summary statistics and data quality metrics

3. **Troubleshooting Workflow**
   - User receives alert about processing failure
   - Opens dashboard to view detailed error logs
   - Identifies root cause (network, data format, database issues)
   - Retries failed processes or escalates to technical team

### User Experience Goals

- **Intuitive Interface** - Non-technical users can understand status at a glance
- **Responsive Design** - Works well on desktop and mobile devices
- **Real-time Updates** - Status refreshes automatically without page reload
- **Contextual Help** - Tooltips and documentation for complex metrics
- **Customizable Views** - Users can focus on data relevant to their role

## Key Metrics to Display

### Processing Status
- Current status of each script (running, completed, failed, scheduled)
- Progress indicators showing percentage complete
- Estimated time remaining for running processes

### Data Quality Metrics
- Record counts by table and date
- Data freshness indicators
- Missing data alerts
- Processing time trends

### System Health
- Database connection status
- API rate limit status (Yahoo Finance)
- Disk space and system resources
- Error rates and types over time
