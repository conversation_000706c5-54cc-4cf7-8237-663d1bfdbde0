"""Initial dashboard tables

Revision ID: 001
Revises: 
Create Date: 2025-05-24 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create process_executions table
    op.create_table('process_executions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('task_id', sa.String(length=255), nullable=True),
        sa.Column('process_name', sa.String(length=100), nullable=False),
        sa.Column('script_path', sa.String(length=500), nullable=False),
        sa.Column('status', sa.String(length=50), nullable=True),
        sa.Column('start_time', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('end_time', sa.DateTime(timezone=True), nullable=True),
        sa.Column('duration_seconds', sa.Float(), nullable=True),
        sa.Column('return_code', sa.Integer(), nullable=True),
        sa.Column('stdout', sa.Text(), nullable=True),
        sa.Column('stderr', sa.Text(), nullable=True),
        sa.Column('parameters', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_process_executions_id'), 'process_executions', ['id'], unique=False)
    op.create_index(op.f('ix_process_executions_task_id'), 'process_executions', ['task_id'], unique=True)

    # Create system_health table
    op.create_table('system_health',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('timestamp', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('cpu_usage', sa.Float(), nullable=True),
        sa.Column('memory_usage', sa.Float(), nullable=True),
        sa.Column('disk_usage', sa.Float(), nullable=True),
        sa.Column('database_status', sa.String(length=50), nullable=True),
        sa.Column('redis_status', sa.String(length=50), nullable=True),
        sa.Column('active_connections', sa.Integer(), nullable=True),
        sa.Column('queue_size', sa.Integer(), nullable=True),
        sa.Column('metrics_data', sa.JSON(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_system_health_id'), 'system_health', ['id'], unique=False)

    # Create table_metrics table
    op.create_table('table_metrics',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('table_name', sa.String(length=100), nullable=False),
        sa.Column('row_count', sa.Integer(), nullable=True),
        sa.Column('last_updated', sa.DateTime(timezone=True), nullable=True),
        sa.Column('table_size_bytes', sa.Integer(), nullable=True),
        sa.Column('index_size_bytes', sa.Integer(), nullable=True),
        sa.Column('vacuum_stats', sa.JSON(), nullable=True),
        sa.Column('timestamp', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_table_metrics_id'), 'table_metrics', ['id'], unique=False)

    # Create data_quality_checks table
    op.create_table('data_quality_checks',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('check_id', sa.String(length=255), nullable=True),
        sa.Column('check_name', sa.String(length=200), nullable=False),
        sa.Column('table_name', sa.String(length=100), nullable=False),
        sa.Column('check_type', sa.String(length=50), nullable=False),
        sa.Column('status', sa.String(length=50), nullable=True),
        sa.Column('expected_value', sa.String(length=500), nullable=True),
        sa.Column('actual_value', sa.String(length=500), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('check_config', sa.JSON(), nullable=True),
        sa.Column('execution_time_ms', sa.Float(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_data_quality_checks_check_id'), 'data_quality_checks', ['check_id'], unique=True)
    op.create_index(op.f('ix_data_quality_checks_id'), 'data_quality_checks', ['id'], unique=False)

    # Create system_alerts table
    op.create_table('system_alerts',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('alert_id', sa.String(length=255), nullable=True),
        sa.Column('alert_type', sa.String(length=50), nullable=False),
        sa.Column('title', sa.String(length=200), nullable=False),
        sa.Column('message', sa.Text(), nullable=False),
        sa.Column('source', sa.String(length=100), nullable=True),
        sa.Column('severity', sa.String(length=20), nullable=True),
        sa.Column('acknowledged', sa.Boolean(), nullable=True),
        sa.Column('acknowledged_by', sa.String(length=100), nullable=True),
        sa.Column('acknowledged_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('resolved', sa.Boolean(), nullable=True),
        sa.Column('resolved_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('metadata', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_system_alerts_alert_id'), 'system_alerts', ['alert_id'], unique=True)
    op.create_index(op.f('ix_system_alerts_id'), 'system_alerts', ['id'], unique=False)

    # Create process_schedules table
    op.create_table('process_schedules',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('schedule_id', sa.String(length=255), nullable=True),
        sa.Column('process_name', sa.String(length=100), nullable=False),
        sa.Column('script_path', sa.String(length=500), nullable=False),
        sa.Column('cron_expression', sa.String(length=100), nullable=True),
        sa.Column('enabled', sa.Boolean(), nullable=True),
        sa.Column('last_run', sa.DateTime(timezone=True), nullable=True),
        sa.Column('next_run', sa.DateTime(timezone=True), nullable=True),
        sa.Column('run_count', sa.Integer(), nullable=True),
        sa.Column('success_count', sa.Integer(), nullable=True),
        sa.Column('failure_count', sa.Integer(), nullable=True),
        sa.Column('parameters', sa.JSON(), nullable=True),
        sa.Column('timeout_seconds', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_process_schedules_id'), 'process_schedules', ['id'], unique=False)
    op.create_index(op.f('ix_process_schedules_schedule_id'), 'process_schedules', ['schedule_id'], unique=True)


def downgrade() -> None:
    # Drop tables in reverse order
    op.drop_index(op.f('ix_process_schedules_schedule_id'), table_name='process_schedules')
    op.drop_index(op.f('ix_process_schedules_id'), table_name='process_schedules')
    op.drop_table('process_schedules')
    
    op.drop_index(op.f('ix_system_alerts_id'), table_name='system_alerts')
    op.drop_index(op.f('ix_system_alerts_alert_id'), table_name='system_alerts')
    op.drop_table('system_alerts')
    
    op.drop_index(op.f('ix_data_quality_checks_id'), table_name='data_quality_checks')
    op.drop_index(op.f('ix_data_quality_checks_check_id'), table_name='data_quality_checks')
    op.drop_table('data_quality_checks')
    
    op.drop_index(op.f('ix_table_metrics_id'), table_name='table_metrics')
    op.drop_table('table_metrics')
    
    op.drop_index(op.f('ix_system_health_id'), table_name='system_health')
    op.drop_table('system_health')
    
    op.drop_index(op.f('ix_process_executions_task_id'), table_name='process_executions')
    op.drop_index(op.f('ix_process_executions_id'), table_name='process_executions')
    op.drop_table('process_executions')
