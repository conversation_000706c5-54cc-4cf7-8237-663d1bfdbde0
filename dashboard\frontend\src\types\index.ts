export interface ProcessStatus {
  task_id: string;
  process: string;
  process_type?: string;
  status: 'pending' | 'running' | 'success' | 'failed' | 'cancelled' | 'starting';
  progress?: number;
  message?: string;
  started_at?: string;
  completed_at?: string;
  records_processed?: number;
  parameters?: Record<string, any>;
  output?: string[];
  error?: string;
}

export interface ProcessType {
  UPDATE_INDEX_OPTIONS: 'update_index_options';
  UPDATE_STOCK_OPTIONS: 'update_stock_options';
  COPY_VIEW_MULTIDB: 'copy_view_multidb';
}

export interface SystemHealth {
  database_connection: boolean;
  redis_connection: boolean;
  last_processing_date?: string;
  active_processes: number;
  disk_space_gb?: number;
  memory_usage_percent?: number;
  cpu_usage?: number;
  memory_usage?: number;
  timestamp?: string;
}

export interface TableMetric {
  table_name: string;
  record_count: number;
  table_size_bytes?: number;
  last_updated?: string;
  data_quality_score?: number;
  data_quality_status: 'good' | 'warning' | 'error' | 'unknown';
}

export interface TableMetricsResponse {
  metrics: TableMetric[];
  updated_at: string;
}

export interface DataQualityCheck {
  check_type: string;
  table_name: string;
  status: string;
  score?: number;
  message?: string;
  details?: Record<string, any>;
}

export interface DataQualityResponse {
  overall_score: number;
  checks: DataQualityCheck[];
  updated_at: string;
}

export interface ErrorLogEntry {
  id: string;
  timestamp: string;
  severity: 'INFO' | 'WARNING' | 'ERROR';
  process?: string;
  message: string;
  details?: Record<string, any>;
}

export interface ErrorLogResponse {
  errors: ErrorLogEntry[];
  total_count: number;
  page: number;
  page_size: number;
}

export interface ProcessStartRequest {
  process: string;
  parameters: Record<string, any>;
}

export interface WebSocketMessage {
  type: 'process_update' | 'system_alert' | 'data_quality_update';
  task_id?: string;
  data: any;
}
