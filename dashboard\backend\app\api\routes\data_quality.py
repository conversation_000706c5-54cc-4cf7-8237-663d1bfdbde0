from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import Optional, List
from datetime import date, datetime, timedelta
from ...core.database import get_db
from ...services.data_quality import DataQualityService
from ...models.schemas import DataQualityResponse, ErrorLogResponse, ErrorLogEntry

router = APIRouter()

@router.get("/checks", response_model=DataQualityResponse)
async def run_data_quality_checks(
    txn_date: Optional[date] = Query(None, description="Date to check (defaults to today)"),
    db: Session = Depends(get_db)
):
    """Run comprehensive data quality checks"""
    if txn_date is None:
        txn_date = date.today()
    
    dq_service = DataQualityService(db)
    
    try:
        return await dq_service.run_comprehensive_checks(txn_date)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to run data quality checks: {str(e)}")

@router.get("/errors", response_model=ErrorLogResponse)
async def get_error_logs(
    severity: Optional[str] = Query(None, description="Filter by severity"),
    process: Optional[str] = Query(None, description="Filter by process"),
    start_date: Optional[date] = Query(None, description="Start date for filtering"),
    end_date: Optional[date] = Query(None, description="End date for filtering"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(50, ge=1, le=100, description="Items per page"),
    db: Session = Depends(get_db)
):
    """Get error logs with filtering and pagination"""
    
    # Mock error logs for now - in production, these would come from a logging table
    mock_errors = [
        ErrorLogEntry(
            id="err_001",
            timestamp=datetime.now() - timedelta(hours=2),
            severity="ERROR",
            process="update_index_options",
            message="Failed to download HSI option data",
            details={"url": "https://www.hkex.com.hk/eng/derivatives/dqe.htm", "error": "Connection timeout"}
        ),
        ErrorLogEntry(
            id="err_002", 
            timestamp=datetime.now() - timedelta(hours=5),
            severity="WARNING",
            process="update_stock_options",
            message="Some records had invalid settlement prices",
            details={"invalid_count": 5, "total_count": 1250}
        ),
        ErrorLogEntry(
            id="err_003",
            timestamp=datetime.now() - timedelta(days=1),
            severity="INFO",
            process="copy_view_multidb",
            message="Database synchronization completed",
            details={"records_synced": 15000, "duration_seconds": 45}
        )
    ]
    
    # Apply filters
    filtered_errors = mock_errors
    
    if severity:
        filtered_errors = [e for e in filtered_errors if e.severity == severity.upper()]
    
    if process:
        filtered_errors = [e for e in filtered_errors if e.process == process]
    
    if start_date:
        filtered_errors = [e for e in filtered_errors if e.timestamp.date() >= start_date]
    
    if end_date:
        filtered_errors = [e for e in filtered_errors if e.timestamp.date() <= end_date]
    
    # Pagination
    total_count = len(filtered_errors)
    start_idx = (page - 1) * page_size
    end_idx = start_idx + page_size
    paginated_errors = filtered_errors[start_idx:end_idx]
    
    return ErrorLogResponse(
        errors=paginated_errors,
        total_count=total_count,
        page=page,
        page_size=page_size
    )

@router.post("/reprocess")
async def reprocess_data(
    process: str,
    txn_date: date,
    db: Session = Depends(get_db)
):
    """Trigger data reprocessing for a specific date"""
    # This would integrate with the process orchestrator
    # For now, return a mock response
    return {
        "message": f"Reprocessing {process} for {txn_date} has been queued",
        "task_id": f"reprocess_{process}_{txn_date.strftime('%Y%m%d')}"
    }
