#!/usr/bin/env python3
"""
Simple test to download HKEX file
"""

import os
import requests

def test_download():
    # Create output directory
    output_dir = "output"
    os.makedirs(output_dir, exist_ok=True)
    print(f"Created output directory: {output_dir}")
    
    # Download URL
    url = 'https://www.hkex.com.hk/chi/services/trading/securities/securitieslists/ListOfSecurities_c.xlsx'
    filename = 'ListOfSecurities_c.xlsx'
    file_path = os.path.join(output_dir, filename)
    
    print(f"Downloading from: {url}")
    print(f"Saving to: {file_path}")
    
    try:
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        with open(file_path, 'wb') as f:
            f.write(response.content)
        
        file_size = os.path.getsize(file_path)
        print(f"✓ Download successful! File size: {file_size:,} bytes")
        
        # Test reading with pandas
        import pandas as pd
        df = pd.read_excel(file_path, skiprows=0, header=2)
        print(f"✓ File is valid Excel with {len(df)} rows")
        print(f"✓ Sample columns: {list(df.columns)[:5]}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error: {e}")
        return False

if __name__ == "__main__":
    print("HKEX Download Test")
    print("=" * 30)
    success = test_download()
    print(f"\nResult: {'SUCCESS' if success else 'FAILED'}")
