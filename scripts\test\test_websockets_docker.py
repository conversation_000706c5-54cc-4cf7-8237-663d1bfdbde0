#!/usr/bin/env python3
"""
Test script to verify websocket packages are working correctly in Docker
"""

def test_websockets_import():
    """Test basic websockets import"""
    try:
        import websockets
        print(f"✅ websockets imported successfully, version: {websockets.__version__}")
        return True
    except ImportError as e:
        print(f"❌ Failed to import websockets: {e}")
        return False

def test_websockets_legacy():
    """Test websockets.legacy.handshake import (required by uvicorn)"""
    try:
        import websockets.legacy.handshake
        print("✅ websockets.legacy.handshake imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Failed to import websockets.legacy.handshake: {e}")
        return False

def test_websocket_client():
    """Test websocket-client import (required by Selenium)"""
    try:
        import websocket
        from websocket import WebSocketApp
        print("✅ websocket-client with WebSocketApp imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Failed to import websocket-client: {e}")
        return False

def test_selenium_websocket():
    """Test Selenium with websocket support"""
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        print("✅ Selenium imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Failed to import Selenium: {e}")
        return False

def main():
    """Run all websocket tests"""
    print("🧪 Testing WebSocket packages in Docker container...")
    print("=" * 60)
    
    tests = [
        ("Basic websockets", test_websockets_import),
        ("Legacy handshake", test_websockets_legacy),
        ("WebSocket client", test_websocket_client),
        ("Selenium WebSocket", test_selenium_websocket),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 Testing {test_name}:")
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 60)
    print("📊 Test Summary:")
    all_passed = True
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status}: {test_name}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All websocket tests passed! Backend should work now.")
    else:
        print("\n💥 Some tests failed. Backend may have issues.")
    
    return all_passed

if __name__ == "__main__":
    main()
