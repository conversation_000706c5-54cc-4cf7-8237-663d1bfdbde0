# Technical Context - HKEX Dashboard

## ✅ IMPLEMENTED Technology Stack

### Dashboard Technologies - OPERATIONAL
- **FastAPI 0.104.1** - Backend API framework (IMPLEMENTED)
- **React 18.2** - Frontend framework with TypeScript (IMPLEMENTED)
- **Material-UI 5.14** - UI component library (IMPLEMENTED)
- **WebSocket** - Real-time communication (IMPLEMENTED)
- **Docker** - Containerization for deployment (IMPLEMENTED)

### Backend Technologies
- **Python 3.11** - Core processing language (UPGRADED)
- **SQLAlchemy 2.0** - Database ORM (recently upgraded)
- **PostgreSQL** - Primary database for option data
- **Pandas** - Data manipulation and analysis
- **BeautifulSoup4** - HTML parsing for HKEX reports
- **Requests** - HTTP client for data downloads
- **Uvicorn** - ASGI server for FastAPI
- **Redis** - Caching and session management

### Key Dependencies
```
pandas==2.2.3
sqlalchemy==2.0.41
psycopg2==2.9.10
beautifulsoup4==4.13.4
requests==2.32.3
scipy==1.15.3
yfinance==0.2.61
plotly==6.1.1
```

### Database Schema
- **option_daily_report** - Index option data (HSI, HHI, HTI, MHI)
- **weekly_option_daily_report** - Weekly index option data
- **stock_option_report** - Stock option data
- **stock_option_strikeDG** - Option Greeks grid
- **t_delta_all_strikes** - Aggregated delta positions
- **Materialized Views** - option_daily_iv, option_daily_volume

## Existing Processing Scripts

### UpdateIndexOptionPostgres.py
- **Purpose**: Downloads and processes HKEX index option reports
- **Key Functions**:
  - `getDailyReport()` - Main orchestration
  - `fetchHKEXReport()` - Downloads HTML reports
  - `parseHKEXReport()` - Extracts option data
  - `ProcessStrikeDG()` - Calculates option Greeks
- **Output Tables**: option_daily_report, option_daily_strikedg

### UpdateStockOptionReportPostgres.py
- **Purpose**: Processes stock option data with Black-Scholes calculations
- **Key Functions**:
  - `getStockOptionReport()` - Main processing function
  - `ProcessSOStrikeDG()` - Greeks grid generation
  - `updateDeltaAllStrikes()` - Delta aggregation
- **Output Tables**: stock_option_report, stock_option_strikeDG, t_delta_all_strikes

### copyViewMultiDB.py
- **Purpose**: Synchronizes data between local and remote databases
- **Key Functions**:
  - Data type conversion and validation
  - Truncate and reload operations
  - Multi-database synchronization
- **Output**: Updated remote database views

## Technical Constraints

### SQLAlchemy 2.0 Migration
- Recent upgrade broke some database operations
- Must use `text()` for raw SQL queries
- Connection handling changed from `engine.execute()` to `engine.connect()`
- Parameterized queries required for security

### Yahoo Finance API Limitations
- Rate limiting causes YFRateLimitError
- Need to minimize API calls by batching requests
- Should cache price data to reduce API usage

### Database Performance
- Large datasets require chunked processing
- Materialized views need concurrent refresh
- Connection pooling needed for concurrent operations

## Development Environment
- **OS**: Windows (based on pywin32 dependency)
- **IDE**: VSCode (based on workspace structure)
- **Virtual Environment**: Located in ./env/
- **Database**: Local PostgreSQL instance

## Recommended Dashboard Technologies

### Frontend Framework Options
1. **Streamlit** - Rapid prototyping, Python-native
2. **Dash (Plotly)** - Rich visualizations, already have plotly dependency
3. **FastAPI + React** - More scalable, better separation of concerns
4. **Flask + Bootstrap** - Lightweight, familiar to Python developers

### Real-time Updates
- **WebSockets** - For real-time progress updates
- **Server-Sent Events** - Simpler alternative for one-way updates
- **Polling** - Fallback option, easier to implement

### Monitoring Integration
- **APScheduler** - Python job scheduling
- **Celery** - Distributed task queue (if scaling needed)
- **Simple subprocess** - Direct script execution monitoring

## Security Considerations
- Database credentials in environment variables
- Parameterized queries to prevent SQL injection
- Input validation for user-triggered processes
- Audit logging for process execution

## Performance Requirements
- Dashboard should load within 2 seconds
- Real-time updates with <5 second latency
- Support for concurrent users (5-10 expected)
- Historical data queries should complete within 10 seconds
