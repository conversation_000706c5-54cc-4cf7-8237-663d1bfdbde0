# Stock Option Market Report POSTGRESQL
# Updated for SQLAlchemy 2.0 compatibility

"""
Stock Option Market Report Processing System

This module processes Hong Kong Exchange (HKEX) stock option data by:
1. Downloading daily option reports from HKEX website
2. Calculating option Greeks (Delta, Gamma) using Black-Scholes model
3. Updating aggregated position tables for analysis
4. Refreshing materialized views for reporting

Key Features:
- SQLAlchemy 2.0 compatible database operations
- Parameterized queries for security
- Comprehensive error handling and logging
- Modular function design for maintainability
"""

# %%
# Standard Library Imports
import os
import logging
import datetime as dt
from datetime import datetime

# Third-party Library Imports
import pandas as pd
from sqlalchemy import create_engine
from pandas.tseries.offsets import MonthEnd, BMonthEnd
from scipy.stats import norm
from dotenv import load_dotenv

# Local Module Imports
from Storacle import d1, gamma, listMonths, getWODay2Expiry

# HKEX Pipeline Imports
from hkex_pipeline import HKEXPipeline
from hkex_fetcher import test_hkex_connection, test_specific_report_url, check_environment, is_debug_mode

# ================================================================================
# Configuration and Environment Setup
# ================================================================================

# Load environment variables from .env file
load_dotenv()

# Database and logging configuration
LOG_LEVEL = int(os.environ.get('LOG_LEVEL'))
SQL_ECHO = int(os.environ.get('SQL_ECHO'))

# Configure logging
logging.basicConfig()
logging.getLogger('sqlalchemy.dialects.postgresql').setLevel(LOG_LEVEL)

db = os.environ.get('WILL9700_DB')
pathname = os.getenv('out_path')
# Create database connection engine with SQLAlchemy 2.0 compatibility
cnx = create_engine(db, isolation_level="AUTOCOMMIT", echo=SQL_ECHO)

# Trading calendar configuration
weekmask = 'Mon Tue Wed Thu Fri'
exchange_holidays = []  # Can be populated with specific exchange holidays

# ================================================================================
# Configuration and Environment Setup
# ================================================================================

# ================================================================================
# Core Database Query Functions
# ================================================================================

def getSOContract(isymbmonth, d):
    """
    Retrieve stock option contract data from the database.
    
    This function queries the stock_option_report table to get option contract details
    for a specific symbol/month combination after a given date. It filters for contracts
    with significant open interest and delta values.
    
    Args:
        isymbmonth (str): Symbol and month identifier (e.g., 'A50.OCT20')
        d (datetime.date): Starting date for the query
        
    Returns:
        pandas.DataFrame: DataFrame containing option contract data with columns:
            - txn_id: Transaction ID
            - txn_date: Transaction date
            - symb: Symbol (first 3 characters)
            - cont_month: Contract month
            - strike: Strike price
            - inst: Full instrument name
            - cp: Call/Put indicator ('C' or 'P')
            - close: Closing price
            - iv: Implied volatility (decimal)
            - oi: Open interest
            - stock_price: Underlying stock price
            - delta: Option delta
            
    Note:
        Uses SQLAlchemy 2.0 parameterized queries to prevent SQL injection.
        Filters for options where oi * delta > 10 to focus on significant positions.
    """
    from sqlalchemy import text

    # Use parameterized query with text() for SQLAlchemy 2.0 compatibility
    query = text("""
        SELECT
            a.txn_id, a.txn_date,
            substr(a.inst_name,1,3) as symb,
            substr(a.inst_name,5,7) as cont_month,
            to_number(substr(a.inst_name,13,6), '9999.99') as strike,
            substr(a.inst_name,1,18) as inst,
            substr(a.inst_name,20,1) as cp,
            a.close, a.iv::decimal/100 as iv, a.oi,
            a.stock_price, a.delta
        FROM stock_option_report a
        WHERE a.oi * a.delta > 10
        AND a.txn_date > CAST(:date AS DATE)
        AND left(a.inst_name,11) = :isymbmonth
    """)

    # Execute query with parameters using connection context manager
    with cnx.connect() as conn:
        lines = pd.read_sql(query, conn, params={"date": str(d), "isymbmonth": isymbmonth})

    print(f"{len(lines)=} rows retrieved from getContract {isymbmonth}, {d}")    
    return lines


# ================================================================================
# Database Insert Functions
# ================================================================================

def insert_SO_strikeDG(itxn_id, ip, idelta, ig):
    """
    Insert calculated delta and gamma values for a specific strike price.
    
    This function stores option Greeks (delta and gamma) for different strike prices
    in the stock_option_strikeDG table. This data is used for "what-if" analysis
    to understand how option positions would behave at different underlying prices.
    
    Args:
        itxn_id (int): Transaction ID from the original option report
        ip (float): Strike price for the calculation
        idelta (float): Calculated delta value at this strike
        ig (float): Calculated gamma value at this strike
        
    Returns:
        int: 1 if insert successful, 0 if failed
        
    Note:
        Uses SQLAlchemy 2.0 connection context manager and parameterized queries.
        Handles exceptions gracefully and prints error details for debugging.
    """
    from sqlalchemy import text

    x = 0
    sql = text("INSERT INTO stock_option_strikeDG(txn_id, strike, delta, gamma) VALUES (:txn_id, :strike, :delta, :gamma)")

    # Create parameter dictionary for safe SQL execution
    params = {
        "txn_id": itxn_id,
        "strike": ip,
        "delta": float(idelta),
        "gamma": float(ig)
    }

    try:
        # Use connection context manager for SQLAlchemy 2.0 compatibility
        with cnx.connect() as conn:
            conn.execute(sql, params)
            conn.commit()
            x = 1
    except Exception as e:
        print(itxn_id, ip, idelta, ig, str(e), flush=True)

    return x


# ================================================================================
# Option Greeks Processing Functions
# ================================================================================

def ProcessSOStrikeDG(isymbmonth):
    """
    Process and calculate option Greeks (Delta/Gamma) for all strikes.
    
    This is a core function that performs "what-if" analysis by calculating
    how option deltas and gammas would change across different underlying
    stock prices. This creates a comprehensive grid of Greeks values that
    can be used for risk analysis and portfolio optimization.
    
    Process:
    1. Determines the date range for processing (last updated + 1)
    2. Retrieves all option contracts for the symbol/month
    3. Defines a strike price range (80% to 120% of current stock price)
    4. For each option contract:
       - Calculates time to expiry
       - For each strike in the range:
         - Computes delta using Black-Scholes model
         - Computes gamma using Black-Scholes model
         - Stores results in stock_option_strikeDG table
    
    Args:
        isymbmonth (str): Symbol and month identifier (e.g., 'A50.OCT20')
        
    Returns:
        int: Total number of strikeDG records inserted
        
    Mathematical Models:
        - Uses Black-Scholes d1 formula for delta calculation
        - Call delta = N(d1), Put delta = N(d1) - 1
        - Gamma calculation uses the probability density function
        
    Note:
        Filters out positions with oi * delta < 10 to focus on significant positions.
        Uses SQLAlchemy 2.0 compatible database operations throughout.
    """
    # Parse symbol and contract month from input
    inputs = isymbmonth.split('.')
    symb = inputs[0]
    cmonth = inputs[1]

    from sqlalchemy import text

    # Determine starting date: either last processed date or 30 days ago
    query = text("""
        SELECT greatest((CURRENT_DATE - 30), max(txn_date))
        FROM stock_option_report a, stock_option_strikedg b
        WHERE a.txn_id = b.txn_id
        AND left(a.inst_name, 11) = :isymbmonth
    """)

    with cnx.connect() as conn:
        q_result = pd.read_sql(query, conn, params={"isymbmonth": isymbmonth})

    # print(f'Query result: {q_result}')    
    start_date = q_result.iloc[0,0]
    
    # Handle case where no previous data exists
    if start_date == None:
        start_date = dt.date.today() - dt.timedelta(weeks=1)
        print(f'Process StrikeDG, Override start date: {start_date}')    
    
    # Get all option contracts for processing
    lines = getSOContract(isymbmonth, start_date)
    
    # Define strike price range for analysis (80% to 120% of current stock price)
    min_strike = 0.8 * lines.stock_price.min()
    max_strike = 1.2 * lines.stock_price.max()
    all_strikes = sorted(set(lines[(lines.strike >= min_strike) & (lines.strike <= max_strike)].strike))
    
    # Process each option contract
    strikeDG_count = 0
    for i, l in lines.iterrows():
        # Skip contracts outside our strike range or with insufficient data
        if l.strike < min_strike or l.strike > max_strike:
            continue
        if l.oi < 1 or l.iv < 0.01:
            continue
            
        # Calculate time to expiry in years
        expiry_date = l.cont_month[0:2] + '-' + l.cont_month[2:5] + '-' + l.cont_month[-2:]
        tx = getWODay2Expiry(expiry_date, l.txn_date) / 247
        
        # Calculate current delta and gamma for reference
        d = d1(l.strike, l.stock_price, l.iv, tx) 
        cdelta = norm.cdf(d)
        
        if l.cp == 'C':
            delta = cdelta
        else:
            delta = 1 - cdelta
            
        g = gamma(d, l.stock_price, l.iv, tx)
        
        # Store calculated values in the dataframe
        lines.loc[i, 'cdelta'] = cdelta
        lines.loc[i, 'gamma'] = g
        
        # print(l.txn_date, l.inst, l.cp)
        strike_count = 0
        
        # Calculate delta/gamma for all strike prices in our range
        for p in all_strikes:
            # Calculate option delta when stock price = p
            d = d1(l.strike, p, l.iv, tx) 
            cdelta = norm.cdf(d)
            
            if l.cp == 'C':
                delta = cdelta
            else:
                delta = 1 - cdelta
                
            g = gamma(d, p, l.iv, tx)
            
            # Skip if the position size is too small to be significant
            if (l.oi * delta) < 10:
                continue
                
            # Insert the calculated Greeks for this strike price
            if insert_SO_strikeDG(l.txn_id, p, delta, g) == 1:
                strike_count += 1     
                
        strikeDG_count += strike_count
        # print(f'{strike_count} StrikeDG rows processed for {l.inst}')
        
    print(f'Total {strikeDG_count} StrikeDG rows inserted')
    return strikeDG_count


# ================================================================================
# Delta Aggregation Functions
# ================================================================================

def updateDeltaAllStrikes(t_date):
    """
    Aggregate option deltas across all strikes for a specific date.
    
    This function creates a comprehensive summary of option positioning by aggregating
    delta exposures across all strike prices. This is essential for understanding
    market maker hedging requirements and potential price pressure points.
    
    Process:
    1. Delete existing records for the target date
    2. Query all option positions and their calculated deltas
    3. Aggregate by contract month, date, and strike price:
       - Call delta: Sum of (call_positions * delta)
       - Put delta: Sum of (put_positions * delta) 
       - Absolute delta: Sum of all |position * delta|
       - Total gamma: Sum of all (position * gamma)
    4. Insert aggregated results into t_delta_all_strikes table
    5. Verify results by querying the inserted data
    
    Args:
        t_date (datetime.date): Date for which to calculate delta aggregations
        
    Returns:
        None (results are stored in database)
        
    Database Tables:
        - Input: stock_option_report, stock_option_strikeDG
        - Output: t_delta_all_strikes
        
    Note:
        Uses SQLAlchemy 2.0 compatible operations throughout.
        Prints progress and verification information for monitoring.
    """
    from sqlalchemy import text

    # Convert date to string format for database operations
    i_date = t_date.strftime("%Y-%m-%d")
    print(f"updateDeltaAllStrikes: {i_date}")

    # Clean up existing records for this date
    delete_query = text("DELETE FROM t_delta_all_strikes WHERE txn_date = :date")
    with cnx.connect() as conn:
        conn.execute(delete_query, {"date": i_date})
        conn.commit()
    print(f"Deleted existing records for date: {i_date}")

    # Aggregate delta exposures across all strikes
    query = text("""
        SELECT substr(a.inst_name,1,11) AS cmonth,
            a.txn_date AS txn_date, b.strike AS strike,
            sum(((case when substr(a.inst_name,20,1) = 'C' then 1 else 0 end) * a.oi) * b.delta) AS call_delta,
            sum(((CASE WHEN substr(a.inst_name,20,1) = 'P' THEN 1 ELSE 0 END) * a.oi) * b.delta) AS put_delta,
            sum((a.oi * b.delta)) AS abs_delta,
            sum((a.oi * b.gamma)) AS total_gamma
        FROM stock_option_report a, stock_option_strikedg b
        WHERE (a.txn_id = b.txn_id)
        AND a.txn_date = CAST(:date AS DATE)
        GROUP BY substr(a.inst_name,1,11), a.txn_date, b.strike
    """)

    # Execute the aggregation query
    with cnx.connect() as conn:
        df = pd.read_sql(query, conn, params={"date": i_date})

    print(f"Retrieved {len(df)} rows of delta data for aggregation")

    # Insert aggregated results
    try:
        print("Sample of aggregated data:")
        print(df.head() if len(df) > 0 else "No data to insert")
        
        # Use SQLAlchemy 2.0 style with connection context manager
        with cnx.connect() as conn:
            df.to_sql(name='t_delta_all_strikes', con=conn, if_exists='append', index=False)
        print(f"Successfully inserted {len(df)} aggregated delta records")
        
    except Exception as e:
        print(f"Error inserting delta aggregations: {str(e)}", flush=True)
        print("DataFrame that caused the error:")
        print(df)

    # Verify results by checking what was inserted
    check_query = text("SELECT cmonth, count(*) FROM t_delta_all_strikes WHERE txn_date = :date GROUP BY cmonth")
    with cnx.connect() as conn:
        result_df = pd.read_sql(check_query, conn, params={"date": i_date})
        print("Verification - Records inserted by contract month:")
        print(result_df)

#%% From HKEXDailyMarketReport

#Stock Options
def insert_SO_Report(iinst_name, itxn_date, iopen, ihigh, ilow, iclose, ivolume, iiv, ioi, ioi_change, istock_price, idelta, ig):
    """
    Insert a stock option report record into the database.
    
    This function stores a complete option contract record including pricing data,
    volume, open interest, and calculated Greeks into the stock_option_report table.
    This is the primary data storage function for raw option market data.
    
    Args:
        iinst_name (str): Full instrument name (e.g., 'XCC.28NOV24.123456.C')
        itxn_date (datetime.date): Transaction date
        iopen (float): Opening price
        ihigh (float): High price
        ilow (float): Low price  
        iclose (float): Closing price
        ivolume (int): Trading volume
        iiv (float): Implied volatility (as percentage)
        ioi (int): Open interest
        ioi_change (int): Change in open interest
        istock_price (float): Underlying stock price
        idelta (float): Calculated option delta
        ig (float): Calculated option gamma
        
    Returns:
        int: 1 if insert successful, 0 if failed
        
    Database Schema:
        Table: stock_option_report
        Primary Key: Auto-generated txn_id
        
    Note:
        Uses SQLAlchemy 2.0 parameterized queries for security.
        Handles exceptions gracefully with error logging.
    """
    # Use SQLAlchemy 2.0 style with text() for SQL statements
    from sqlalchemy import text

    x = 0
    sql = text("""
        INSERT INTO stock_option_report(
            inst_name, txn_date, open, high, low, close, volume, iv, oi, oi_change,
            stock_price, delta, gamma
        ) VALUES (
            :inst_name, :txn_date, :open, :high, :low, :close, :volume, :iv, :oi, :oi_change,
            :stock_price, :delta, :gamma
        )
    """)

    # Create parameter dictionary for safe SQL execution
    params = {
        "inst_name": iinst_name,
        "txn_date": itxn_date,
        "open": iopen,
        "high": ihigh,
        "low": ilow,
        "close": iclose,
        "volume": ivolume,
        "iv": iiv,
        "oi": ioi,
        "oi_change": ioi_change,
        "stock_price": float(istock_price),
        "delta": float(idelta),
        "gamma": float(ig)
    }

    try:
        # Use connection context manager for SQLAlchemy 2.0 compatibility
        with cnx.connect() as conn:
            conn.execute(sql, params)
            conn.commit()
            x = 1
    except Exception as e:
        print(iinst_name, itxn_date, str(e), flush=True)

    return x

def insert_SO_strikeDG(itxn_id, ip, idelta, ig):
    from sqlalchemy import text

    x = 0
    sql = text("INSERT INTO stock_option_strikeDG(txn_id, strike, delta, gamma) VALUES (:txn_id, :strike, :delta, :gamma)")

    # Create parameter dictionary
    params = {
        "txn_id": itxn_id,
        "strike": ip,
        "delta": float(idelta),
        "gamma": float(ig)
    }

    try:
        # Use connection context manager
        with cnx.connect() as conn:
            conn.execute(sql, params)
            conn.commit()
            x = 1
    except Exception as e:
        print(itxn_id, ip, idelta, ig, str(e), flush=True)

    return x


# ================================================================================
# HKEX Data Extraction Functions  
# ================================================================================

def saveStockOptionDataToDatabase(processed_data, report_type='stock_option'):
    """Save processed stock option data to database"""
    success_count = 0
    for item in processed_data:
        if insert_SO_Report(
            item['inst_name'], item['txn_date'],
            item['curr_open'], item['curr_high'], item['curr_low'], item['curr_close'], item['curr_volume'],
            item['iv_percent'], item['open_interest'], item['oi_change'],
            item['stock_price'], item['delta'], item['gamma']
        ) == 1:
            success_count += 1

    return success_count


def getStockOptionReport(symb_list, trade_date):
    """
    Main function to orchestrate stock option processing using new pipeline

    Args:
        symb_list (list or None): List of symbols to process, or None for all symbols
        trade_date (datetime.date): Date of the report to download

    Returns:
        list: List of [symbol.month, date, count] for each processed contract
    """
    # Create pipeline instance (debug mode auto-detected from env)
    pipeline = HKEXPipeline(pathname, saveStockOptionDataToDatabase)

    # Process using pipeline
    results = pipeline.process_stock_option_report(symb_list, trade_date)

    # Return summary counts for backward compatibility
    return results.get('summary_counts', [])

# %%
def main():
    """
    Main execution function for Stock Option Report processing.

    This function orchestrates the entire stock option data processing workflow:
    1. Fetches the latest stock option reports from HKEX
    2. Processes option Greeks (Delta, Gamma) calculations
    3. Updates aggregated delta tables
    4. Refreshes materialized views
    """
    print("=== Stock Option Report Processing Started ===")

    # Check environment variables
    if not check_environment():
        print("Environment check failed. Please set required environment variables.")
        return

    # Test HKEX connection (only when debug mode is enabled)
    if is_debug_mode():
        print("🐛 DEBUG MODE: Running connection tests")
        if not test_hkex_connection():
            print("HKEX connection test failed. Check your internet connection.")
            print("Continuing anyway, but downloads may fail...")

        # Test specific report URL for today's date
        print(f"\nTesting specific report URL for today...")
        if not test_specific_report_url(dt.date.today()):
            print("⚠️  Today's report URL is not accessible. This may be normal if the report isn't published yet.")
            print("Continuing with historical data processing...")
    else:
        print("⏭️  Skipping connection tests (debug mode disabled)")

    # Configuration: Symbol to process (can be expanded to process multiple symbols)
    test_symb = 'XCC'  # Test symbol for processing
    total_insert_txn_count = []

    # ================================================================================
    # STEP 1: Determine Date Range for Processing
    # ================================================================================
    print(f"Step 1: Determining processing date range for symbol: {test_symb}")
    
    # Check Last updated date + 1 using parameterized query
    from sqlalchemy import text
    query = text("""
        SELECT greatest((CURRENT_DATE - 30), (max(txn_date) + 1))
        FROM stock_option_report
        WHERE left(inst_name, 3) = :test_symb
    """)

    with cnx.connect() as conn:
        result = conn.execute(query, {"test_symb": test_symb})
        q_result = result.fetchone()
        start_date = q_result[0] if q_result else None

    print(f'Query result: {start_date}')
    
    # If no previous data, start from one week ago
    if start_date == None:
        start_date = dt.date.today() - dt.timedelta(weeks=1)
        print(f'No previous data found. Override start date: {start_date}')    
    
    end_date = dt.date.today() - dt.timedelta(days=1)
    bdaterange = pd.bdate_range(start_date, end_date)
    print(f"Processing date range: {start_date} to {end_date}")
    print(f"Business dates to process: {len(bdaterange)} days")

    # ================================================================================
    # STEP 2: Download and Process Daily Stock Option Reports
    # ================================================================================
    print("\nStep 2: Downloading and processing daily stock option reports")
    
    for single_date in bdaterange:
        print(f"Processing date: {single_date.strftime('%Y-%m-%d')}")
        try:
            # Download and parse HKEX stock option report for the date
            insert_day_count = getStockOptionReport(None, single_date)
            print(f'Records inserted for {single_date.strftime("%Y-%m-%d")}: {len(insert_day_count)}')
            total_insert_txn_count.extend(insert_day_count)
        except Exception as e:
            print(f"Error processing {single_date.strftime('%Y-%m-%d')}: {e}")
            continue

    # ================================================================================
    # STEP 3: Process Option Greeks (Delta/Gamma) for All Strikes
    # ================================================================================
    if len(total_insert_txn_count) > 0:
        print(f"\nStep 3: Processing option Greeks for {len(total_insert_txn_count)} contracts")
        
        # Get unique contract months that were processed
        x = pd.DataFrame(total_insert_txn_count)
        contract_list = set(x[0])  # Column 0 contains contract identifiers
        
        print(f"Processing Greeks for {len(contract_list)} unique contracts")
        
        # Process Delta/Gamma calculations for each contract
        for symbmonth in contract_list:
            try:
                print(f"Processing Greeks for contract: {symbmonth}")
                ProcessSOStrikeDG(symbmonth)
            except Exception as e:
                print(f"Error processing Greeks for {symbmonth}: {e}")
                continue
        
        # ================================================================================
        # STEP 4: Update Aggregated Delta Tables
        # ================================================================================
        print("\nStep 4: Updating aggregated delta tables")
        
        for single_date in bdaterange:
            try:
                print(f"Updating delta aggregates for: {single_date.strftime('%Y-%m-%d')}")
                updateDeltaAllStrikes(single_date)
            except Exception as e:
                print(f"Error updating deltas for {single_date.strftime('%Y-%m-%d')}: {e}")
                continue
    else:
        print("\nNo new data to process - skipping Greeks and delta calculations")

    # ================================================================================
    # STEP 5: Generate Processing Summary
    # ================================================================================
    print("\nStep 5: Generating processing summary")
    
    summary_df = pd.DataFrame(total_insert_txn_count)
    print(f"Processing Summary:")
    print(summary_df)
    
    if len(total_insert_txn_count) > 0:
        # Check results for the last processed date
        last_processed_date = dt.datetime.strftime(total_insert_txn_count[-1][1], '%Y-%m-%d')
        print(f"\nVerifying results for last processed date: {last_processed_date}")
        
        query = text("SELECT cmonth, count(*) FROM t_delta_all_strikes WHERE txn_date = :date GROUP BY cmonth")
        with cnx.connect() as conn:
            result = conn.execute(query, {"date": last_processed_date})
            result_df = pd.DataFrame(result.fetchall(), columns=['cmonth', 'count'])
        print("Delta aggregation results:")
        print(result_df)
    
    # ================================================================================
    # STEP 6: Refresh Materialized Views
    # ================================================================================
    print("\nStep 6: Refreshing materialized views")
    
    materialized_views = ['option_daily_iv', 'option_daily_volume']
    for view_name in materialized_views:
        try:
            print(f"Refreshing materialized view: {view_name}")
            sql = text(f'REFRESH MATERIALIZED VIEW CONCURRENTLY {view_name}')

            start_time = pd.Timestamp.now()
            with cnx.connect() as conn:
                conn.execute(sql)
                conn.commit()
            end_time = pd.Timestamp.now()
            
            print(f"Successfully refreshed {view_name} in {end_time - start_time}")
        except Exception as e:
            print(f"Error refreshing materialized view {view_name}: {e}")

    # ================================================================================
    # STEP 7: Final Verification
    # ================================================================================
    if len(total_insert_txn_count) > 0:
        print("\nStep 7: Final verification")
        
        last_processed_date = dt.datetime.strftime(total_insert_txn_count[-1][1], '%Y-%m-%d')
        
        # Note: Using the last materialized view name from the loop above
        query = text(f"SELECT txn_date, count(*) FROM {view_name} WHERE txn_date >= :date GROUP BY txn_date")
        with cnx.connect() as conn:
            result = conn.execute(query, {"date": last_processed_date})
            verification_df = pd.DataFrame(result.fetchall(), columns=['txn_date', 'count'])
        
        print(f"Final verification - Records in {view_name} from {last_processed_date}:")
        print(verification_df)

    print("\n=== Stock Option Report Processing Completed ===")

#%%
if __name__ == "__main__":
    """
    Entry point for the Stock Option Report processing system.
    
    This script processes Hong Kong Exchange (HKEX) stock option data:
    - Downloads daily option reports
    - Calculates option Greeks (Delta, Gamma)
    - Updates aggregated position tables
    - Refreshes materialized views for analysis
    
    Usage:
        python UpdateStockOptionReportPostgres.py
    
    Requirements:
        - PostgreSQL database connection configured via environment variables
        - Internet connection to download HKEX reports
        - Required Python packages: pandas, sqlalchemy, requests, etc.
    """
    try:
        main()
    except Exception as e:
        print(f"Fatal error in main execution: {e}")
        print(f"Error type: {type(e).__name__}")
        if hasattr(e, '__traceback__'):
            import traceback
            traceback.print_exc()
        raise


# %%
# Recalc all past min delta
# start_date = dt.date(2021, 4, 1)
# end_date = dt.date.today()
# #end_date = date(2020, 10,12)
# bdaterange = pd.bdate_range(start_date, end_date)
# print (bdaterange)
# for single_date in bdaterange:
#     updateMinDelta(single_date)






# %%
# Recalc all past min delta
# start_date = dt.date(2024, 4, 2)
# end_date = dt.date.today()
# # end_date = date(2020, 10,12)
# bdaterange = pd.bdate_range(start_date, end_date)
# print (bdaterange)
# for single_date in bdaterange:
#     updateDeltaAllStrikes(single_date)


# %%
# t_date ='2021-04-30'
# print('updateDeltaAllStrikes CHECK: ', t_date)
# cnx = create_cnx()
# sql='SELECT cmonth, count(*) FROM t_delta_all_strikes     where txn_date ="{}"     group by cmonth'.format(t_date)
# print(sql)
# df = pd.read_sql(sql , cnx)
# print(df)

# SELECT txn_date, left(inst_name,3), count(*) FROM stock_option_report a, stock_option_strikeDG b 
# where txn_date >='2024-04-04' 
# and a.txn_id=b.txn_id
# group by 1,2
# order by 2,1


# %%
#'SEP21' not in { 'JUN21', 'JUL21'}
#getStockOptionReport(['ALB'], dt.date(2021, 9, 17))
#ProcessSOStrikeDG('CMB.SEP21')
# %%
# delete from stock_option_report where  txn_date> '2024-03-28' ;
# SELECT cmonth, count(*) FROM t_delta_all_strikes   
# where txn_date ='2024-03-28' 
# group by 1

def getHistReport(start_date,end_date ):
    # BulkgetDailyReport
    if start_date == None:
        start_date = dt.date(2023,10,1)
    if end_date == None:
        end_date = dt.date.today()
    # end_date = dt.date(2024,1,1)
    #end_date = date(2020, 10,12)
    bdaterange = pd.bdate_range(start_date, end_date)
    print (bdaterange)
    os.makedirs('html', exist_ok=True)
    print("=== Stock Option Report Processing Completed ===")


