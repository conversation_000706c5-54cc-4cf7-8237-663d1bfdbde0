#!/usr/bin/env python3
"""Test the orchestrator subprocess functionality directly"""
import asyncio
import sys
import os
sys.path.append('app')

async def test_orchestrator_subprocess():
    """Test that the orchestrator can create subprocess on Windows"""
    print("Testing HKEX Orchestrator Windows Subprocess Fix...")
    
    # Import after setting path
    from services.simple_orchestrator import orchestrator
    
    print("✅ Orchestrator imported successfully")
    
    # Test a simple process start
    try:
        # Set up minimal parameters for testing
        test_parameters = {
            'txn_date': '2024-12-20',
            'dry_run': True,
            'python_interpreter': sys.executable
        }
        
        print(f"🧪 Testing process start with parameters: {test_parameters}")
        
        # Start a test process
        task_id = await orchestrator.start_process('update_index_options', test_parameters)
        print(f"✅ Process started successfully with task_id: {task_id}")
        
        # Wait a bit and check status
        await asyncio.sleep(2)
        status = orchestrator.get_process_status(task_id)
        
        if status:
            print(f"📊 Process status: {status['status']}")
            print(f"📝 Process message: {status['message']}")
            print(f"📈 Process progress: {status['progress']}%")
            
            # Check if it's not failed with NotImplementedError
            if 'NotImplementedError' in str(status.get('error', '')):
                print("❌ Still getting NotImplementedError - Windows fix not working")
                return False
            else:
                print("✅ No NotImplementedError - Windows fix is working!")
                return True
        else:
            print("⚠️  Could not get process status")
            return False
            
    except Exception as e:
        if 'NotImplementedError' in str(e):
            print(f"❌ NotImplementedError still occurring: {e}")
            return False
        else:
            print(f"ℹ️  Other error (may be expected): {e}")
            return True  # Other errors are okay for this test

if __name__ == "__main__":
    success = asyncio.run(test_orchestrator_subprocess())
    if success:
        print("\n🎉 Windows subprocess fix is working in the orchestrator!")
    else:
        print("\n💥 Windows subprocess fix failed in the orchestrator!")
    sys.exit(0 if success else 1)
