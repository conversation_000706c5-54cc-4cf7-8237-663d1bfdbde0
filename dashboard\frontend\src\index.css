body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

* {
  box-sizing: border-box;
}

/* The following CSS block is intentionally commented out to prevent style conflicts. */
/*
.custom-log-box {
  background-color: #f0f0f0 !important;
  color: #333333 !important;
  padding: 8px;
}

.custom-log-box > div,
.custom-log-box > div span,
.custom-log-box > div div {
  background-color: transparent !important;
  color: #333333 !important;
  font-family: 'monospace' !important;
}
*/
