# Simple Process Orchestrator
print("DEBUG: Starting simple_orchestrator.py")
import asyncio
import logging
import subprocess
import json
import os
from typing import Dict, Any, Optional, List
from datetime import datetime
from pathlib import Path

print("DEBUG: Imports completed")
logger = logging.getLogger(__name__)
print("DEBUG: Logger created")

# Windows asyncio fix - apply ProactorEventLoop for subprocess support
# Must be set at module level before any asyncio operations
if os.name == 'nt':  # Windows
    try:
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        logger.info("Applied Windows ProactorEventLoop policy for subprocess support at module level")
        print("DEBUG: Applied Windows ProactorEventLoop policy")
    except Exception as e:
        logger.warning(f"Could not set Windows ProactorEventLoop policy at module level: {e}")
        print(f"DEBUG: Failed to set Windows policy: {e}")

class ProcessOrchestratorService:
    """Simple orchestrator for HKEX processing scripts."""
    def __init__(self):
        self.active_processes = {}
        self.websocket_manager = None
        
        # Get the root directory (where the HKEX scripts are located)
        self.scripts_dir = Path(__file__).parent.parent.parent.parent.parent  # Go up to MaxPain2024
        logger.info(f"Scripts directory: {self.scripts_dir}")
        
        self.process_configs = {
            'update_index_options': {
                'script': 'UpdateIndexOptionPostgres.py',
                'description': 'Index Option data in PostgreSQL',
                'timeout': 1800,
                'requires_params': [],
                'optional_params': ['txn_date', 'dry_run', 'batch_size']
            },
            'update_stock_options': {
                'script': 'UpdateStockOptionReportPostgres.py', 
                'description': 'Stock Option Report data in PostgreSQL',
                'timeout': 3600,
                'requires_params': [],
                'optional_params': ['txn_date', 'dry_run', 'batch_size']
            },
            'copy_view_multidb': {
                'script': 'copyViewMultiDB.py',
                'description': 'Copy views across multiple databases',
                'timeout': 2700,
                'requires_params': [],
                'optional_params': ['source_db', 'target_db', 'view_names', 'dry_run']
            }        }
    
    def set_websocket_manager(self, manager):
        """Set WebSocket manager for broadcasting updates."""
        self.websocket_manager = manager
    
    def get_process_types(self):
        """Get all available process types."""
        return {
            name: {
                'description': config['description'],
                'timeout': config['timeout'],
                'requires_params': config['requires_params'],
                'optional_params': config.get('optional_params', [])
            }
            for name, config in self.process_configs.items()
        }

    async def start_process(self, process_type: str, parameters: Dict[str, Any]) -> str:
        """Start a new process and return task ID."""
        if process_type not in self.process_configs:
            raise ValueError(f"Unknown process type: {process_type}")
        
        config = self.process_configs[process_type]
        # Generate a globally unique task_id (avoid duplicates even if process finishes quickly)
        import secrets
        rand_suffix = secrets.token_hex(2)  # 4 hex digits
        task_id = f"{process_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{rand_suffix}"

        # Validate required parameters
        for param in config['requires_params']:
            if param not in parameters:
                raise ValueError(f"Required parameter '{param}' missing for {process_type}")

        # Store process info
        process_info = {
            'task_id': task_id,
            'process_type': process_type,
            'status': 'starting',
            'parameters': parameters,
            'started_at': datetime.utcnow(),
            'progress': 0,
            'message': 'Process initialized',
            'output': [],
            'error': None
        }

        self.active_processes[task_id] = {
            'info': process_info,
            'process': None
        }

        # Broadcast the initial 'starting' status
        # This ensures the frontend receives a WebSocket message for the 'starting' state.
        await self._update_process_status(task_id, 
                                          process_info['status'], 
                                          process_info['message'], 
                                          process_info['progress'])

        # Start the actual process
        asyncio.create_task(self._execute_process(task_id, process_type, parameters))

        logger.info(f"Started process {task_id} of type {process_type}")
        return task_id
    
    async def _execute_process(self, task_id: str, process_type: str, parameters: Dict[str, Any]):
        """Execute the actual script process."""
        logger.info(f"[_execute_process] Entered for task_id: {task_id}, process_type: {process_type}")
        try:
            config = self.process_configs[process_type]
            script_path = self.scripts_dir / config['script']
            logger.info(f"[_execute_process] Task {task_id}: Attempting to use script at {script_path}")
            
            # Check if script exists
            if not script_path.exists():
                logger.error(f"[_execute_process] Task {task_id}: Script not found at {script_path}. scripts_dir: {self.scripts_dir}, script_name: {config['script']}")
                raise FileNotFoundError(f"Script not found: {script_path}")
            
            # Update status to running
            await self._update_process_status(task_id, 'running', 'Executing script...', 10)
            
            # Build command arguments
            cmd_args = ['python', str(script_path)]
              # Add parameters based on script type
            if process_type == 'update_index_options':
                if 'txn_date' in parameters:
                    cmd_args.extend(['--date', parameters['txn_date']])
                if parameters.get('dry_run'):
                    cmd_args.append('--dry-run')
                    
            elif process_type == 'update_stock_options':
                if 'txn_date' in parameters:
                    cmd_args.extend(['--date', parameters['txn_date']])
                if parameters.get('batch_size'):
                    cmd_args.extend(['--batch-size', str(parameters['batch_size'])])
                if parameters.get('dry_run'):
                    cmd_args.append('--dry-run')
                    
            elif process_type == 'copy_view_multidb':
                if 'source_db' in parameters:
                    cmd_args.extend(['--source', parameters['source_db']])
                if 'target_db' in parameters:
                    cmd_args.extend(['--target', parameters['target_db']])
                if parameters.get('dry_run'):
                    cmd_args.append('--dry-run')
            
            logger.info(f"Executing command: {' '.join(cmd_args)}")
              # Windows-specific subprocess handling with fallback
            if os.name == 'nt':  # Windows
                loop = asyncio.get_running_loop()
                if not isinstance(loop, asyncio.ProactorEventLoop):
                    logger.warning(f"Current event loop is {type(loop)}, not ProactorEventLoop. Using subprocess fallback for Windows.")
                    # Use subprocess module with asyncio thread pool executor as fallback
                    import concurrent.futures
                    import subprocess
                    
                    def run_subprocess():
                        return subprocess.run(
                            cmd_args,
                            cwd=self.scripts_dir,
                            capture_output=True,
                            text=True,
                            timeout=timeout or 3600
                        )
                    
                    # Run subprocess in thread pool
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = loop.run_in_executor(executor, run_subprocess)
                        result = await future
                    
                    # Convert result to match asyncio subprocess format
                    stdout = result.stdout.encode() if result.stdout else b''
                    stderr = result.stderr.encode() if result.stderr else b''
                    returncode = result.returncode
                    
                    # Create a mock process object
                    class MockProcess:
                        def __init__(self, stdout, stderr, returncode):
                            self.stdout = stdout
                            self.stderr = stderr
                            self.returncode = returncode
                        
                        async def communicate(self):
                            return self.stdout, self.stderr
                    
                    process = MockProcess(stdout, stderr, returncode)
                else:
                    # Use asyncio subprocess if we have ProactorEventLoop
                    process = await asyncio.create_subprocess_exec(
                        *cmd_args,
                        stdout=asyncio.subprocess.PIPE,
                        stderr=asyncio.subprocess.PIPE,
                        cwd=self.scripts_dir
                    )
            else:
                # Non-Windows systems
                process = await asyncio.create_subprocess_exec(
                    *cmd_args,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    cwd=self.scripts_dir
                )            self.active_processes[task_id]['process'] = process
            await self._update_process_status(task_id, 'running', 'Script executing...', 25)
            
            # Handle output differently for MockProcess vs real asyncio subprocess
            if hasattr(process, 'stdout') and hasattr(process.stdout, 'readline'):
                # Real asyncio subprocess - read in real-time
                output_lines = []
                while True:
                    try:
                        line = await asyncio.wait_for(process.stdout.readline(), timeout=1.0)
                        if line:
                            line_str = line.decode().strip()
                            output_lines.append(line_str)
                            
                            # Store all output for log retrieval
                            self.active_processes[task_id]['output'] = output_lines
                            # Keep last 10 lines in info for status display
                            self.active_processes[task_id]['info']['output'] = output_lines[-10:]
                            
                            # Update progress based on output keywords
                            progress = self._estimate_progress(line_str, len(output_lines))
                            await self._update_process_status(task_id, 'running', line_str, progress)
                            logger.info(f"Process {task_id}: {line_str}")
                        else:
                            break
                    except asyncio.TimeoutError:
                        # Check if process is still running
                        if hasattr(process, 'returncode') and process.returncode is not None:
                            break
                        continue
                    except Exception as e:
                        logger.error(f"Error reading stdout: {e}")
                        break
                
                # Get final output
                stdout, stderr = await process.communicate()
            else:
                # MockProcess - output is already available
                stdout, stderr = await process.communicate()
            
            # Process the final output
            if stdout:
                output_lines = stdout.decode().split('\n')
                self.active_processes[task_id]['output'] = [line for line in output_lines if line.strip()]
                # Keep last 10 lines in info for status display
                self.active_processes[task_id]['info']['output'] = output_lines[-10:]
            
            # Store stderr if present
            if stderr:
                error_lines = stderr.decode().split('\n')
                self.active_processes[task_id]['error_output'] = [line for line in error_lines if line.strip()]
            
            # Process completed
            returncode = getattr(process, 'returncode', 0)
            if returncode == 0:
                await self._update_process_status(task_id, 'completed', 'Process completed successfully', 100)
                logger.info(f"Process {task_id} completed successfully")
            else:
                error_msg = stderr.decode() if stderr else 'Process failed with unknown error'
                await self._update_process_status(task_id, 'failed', f'Process failed: {error_msg}', 0)
                self.active_processes[task_id]['info']['error'] = error_msg
                logger.error(f"Process {task_id} failed: {error_msg}")
                
        except Exception as e:
            error_msg = str(e)
            logger.error(f"[_execute_process] Exception caught for task {task_id}. Error: {error_msg}", exc_info=True) # Log with stack trace

            if task_id in self.active_processes:
                await self._update_process_status(task_id, 'failed', f'Execution error: {error_msg}', 0)
                if 'info' in self.active_processes[task_id]: # Ensure 'info' key exists
                    self.active_processes[task_id]['info']['error'] = error_msg
                else:
                    logger.warning(f"[_execute_process] Task {task_id} missing 'info' dict during exception handling.")
                logger.info(f"[_execute_process] Updated status for failed task {task_id}.")
            else:
                logger.warning(f"[_execute_process] Task {task_id} not found in active_processes during exception handling.")
            # logger.error(f"Process {task_id} execution error: {error_msg}") # Original log, now covered by the one with exc_info
    
    def _estimate_progress(self, line: str, line_count: int) -> int:
        """Estimate progress based on output content."""
        line_lower = line.lower()
        
        # Basic progress estimation
        if 'starting' in line_lower or 'initializing' in line_lower:
            return 10
        elif 'connecting' in line_lower or 'login' in line_lower:
            return 20
        elif 'downloading' in line_lower or 'fetching' in line_lower:
            return 30 + min(40, line_count * 2)  # Progress during download
        elif 'processing' in line_lower or 'inserting' in line_lower:
            return 50 + min(40, line_count)  # Progress during processing
        elif 'completed' in line_lower or 'finished' in line_lower:
            return 90
        elif 'error' in line_lower or 'failed' in line_lower:
            return 0
        else:
            return min(80, 10 + line_count)  # General progress based on activity
    
    async def _update_process_status(self, task_id: str, status: str, message: str, progress: int):
        """Update process status and broadcast via WebSocket. Remove finished processes after a delay. Add debug logging for status transitions and removals."""
        if task_id in self.active_processes:
            process_info = self.active_processes[task_id]['info']
            old_status = process_info.get('status', None)
            process_info['status'] = status
            process_info['message'] = message
            process_info['progress'] = progress
            process_info['updated_at'] = datetime.utcnow()
            logger.info(f"[orchestrator] Task {task_id} status changed: {old_status} -> {status} | message: {message}")

            # Broadcast update via WebSocket
            if self.websocket_manager:
                await self.websocket_manager.broadcast({
                    'type': 'process_update',
                    'data': {
                        'task_id': task_id,
                        'status': status,
                        'message': message,
                        'progress': progress,
                        'process_type': process_info['process_type'],
                        'start_time': process_info['started_at'].isoformat(),
                        'current_step': message
                    }
                })

            # Remove from active_processes if process is finished (completed, failed, or cancelled) after a short delay
            if status in ('completed', 'failed', 'cancelled'):
                async def remove_later():
                    await asyncio.sleep(5)  # Wait 5 seconds so frontend can see the final state
                    removed = self.active_processes.pop(task_id, None)
                    if removed:
                        logger.info(f"[orchestrator] Removed finished process {task_id} from active_processes after status {status}")
                    else:
                        logger.warning(f"[orchestrator] Tried to remove process {task_id} but it was already gone.")
                asyncio.create_task(remove_later())
    
    async def cancel_process(self, task_id: str) -> bool:
        """Cancel a running process."""
        if task_id not in self.active_processes:
            return False
        
        self.active_processes[task_id]['info']['status'] = 'cancelled'
        logger.info(f"Cancelled process {task_id}")
        return True
    
    def get_process_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get the current status of a process."""
        if task_id not in self.active_processes:
            return None
        
        return self.active_processes[task_id]['info'].copy()
    
    def list_active_processes(self) -> List[Dict[str, Any]]:
        """List all currently active processes."""
        return [
            data['info'].copy() 
            for data in self.active_processes.values()
        ]

    def get_active_processes(self) -> List[Dict[str, Any]]:
        """Alias for list_active_processes to match API expectations."""
        return self.list_active_processes()

    def get_log_tail(self, task_id: str, lines: int = 50) -> Dict[str, Any]:
        """Get the last N lines of a process log for real-time viewing."""
        if task_id not in self.active_processes:
            return {"error": "Process not found", "log_lines": []}
        
        process_data = self.active_processes[task_id]
        output_lines = process_data.get('output', [])
        
        # Get the last N lines
        tail_lines = output_lines[-lines:] if len(output_lines) > lines else output_lines
        
        return {
            "task_id": task_id,
            "lines_requested": lines,
            "lines_returned": len(tail_lines),
            "log_lines": tail_lines,
            "total_lines": len(output_lines)
        }

    def get_full_log_content(self, task_id: str) -> Dict[str, Any]:
        """Get the complete log content for a process."""
        if task_id not in self.active_processes:
            return {"error": "Process not found", "log_content": ""}
        
        process_data = self.active_processes[task_id]
        output_lines = process_data.get('output', [])
        
        return {
            "task_id": task_id,
            "log_content": "\n".join(output_lines),
            "total_lines": len(output_lines)
        }

    def get_process_logs(self, task_id: str) -> Dict[str, Any]:
        """Get detailed logs for a specific process."""
        if task_id not in self.active_processes:
            return None
        
        process_data = self.active_processes[task_id]
        
        return {
            "task_id": task_id,
            "status": process_data['info']['status'],
            "start_time": process_data['info']['start_time'],
            "message": process_data['info']['message'],
            "progress": process_data['info']['progress'],
            "output": process_data.get('output', []),
            "error_output": process_data.get('error_output', [])
        }

    def get_process_history(self) -> List[Dict[str, Any]]:
        """Get list of all completed processes from history."""
        # For now, return completed processes from current session
        # In a production system, this would query a database
        completed_processes = []
        
        for task_id, data in self.active_processes.items():
            task = data['task']
            if task.done():
                info = data['info'].copy()
                info['completed'] = True
                if task.exception():
                    info['exception'] = str(task.exception())
                completed_processes.append(info)
        
        return completed_processes
# Global instance
print("DEBUG: About to create orchestrator instance")
orchestrator = ProcessOrchestratorService()
print("DEBUG: Orchestrator created successfully")
