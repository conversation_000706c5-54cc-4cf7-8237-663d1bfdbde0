# Development Environment WebSocket Fix

## Summary
The recent changes to fix WebSocket CSP violations in production would have broken the development environment. This document explains the fix implemented.

## The Problem
- **Production Setup**: Uses nginx proxy on port 80, WebSocket connections go to `ws://localhost/ws`
- **Development Setup**: Direct frontend (port 3000) and backend (port 8000) connections, no nginx
- **Issue**: Hardcoded WebSocket URLs to `ws://localhost/ws` would fail in development (no service on port 80)

## The Solution
Created an environment-aware configuration system:

### 1. Environment Configuration File
- **File**: `frontend/src/config/environment.ts`
- **Purpose**: Automatically detects environment and returns correct WebSocket/API URLs
- **Logic**:
  - Development: `ws://localhost:8000/ws` (direct to backend)
  - Production: `ws://localhost/ws` (through nginx proxy)

### 2. Updated Frontend Components
Updated all React components to use `getWebSocketUrl()` instead of hardcoded URLs:
- `App.tsx`
- `App_complex.tsx` 
- `App_working.tsx`
- `pages/MainDashboard.tsx`

### 3. Development Docker Compose Fix
- **File**: `docker-compose.dev.yml`
- **Change**: Updated environment variables to use `localhost:8000` for development
- **Before**: `REACT_APP_WS_URL=ws://backend:8000` (container-to-container)
- **After**: `REACT_APP_WS_URL=ws://localhost:8000/ws` (browser-accessible)

## Environment URLs

### Development Mode (`NODE_ENV=development`)
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000
- WebSocket: ws://localhost:8000/ws
- No nginx proxy

### Production Mode
- Frontend: http://localhost (through nginx)
- Backend API: http://localhost/api (through nginx)
- WebSocket: ws://localhost/ws (through nginx)
- CSP: `connect-src 'self' ws: wss:` allows WebSocket connections

## How to Run Development

```bash
# Start development environment
cd dashboard
./start-dev.sh        # Linux/Mac
# or
start-dev.bat         # Windows

# Services will be available at:
# - Frontend: http://localhost:3000
# - Backend: http://localhost:8000
# - API Docs: http://localhost:8000/docs
```

## Verification
The environment configuration automatically:
1. Detects if running in development vs production
2. Uses environment variables if available
3. Falls back to sensible defaults based on mode
4. Ensures WebSocket connections work in both environments

## Benefits
- ✅ Development environment works correctly
- ✅ Production environment works correctly  
- ✅ No manual configuration needed
- ✅ Environment variables override defaults
- ✅ CSP violations resolved in production
- ✅ WebSocket connections work in both modes
