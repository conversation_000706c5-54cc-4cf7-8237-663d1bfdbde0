import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  CardContent,
  Typography,
  Box,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  LinearProgress,
  IconButton,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Di<PERSON>r,
  <PERSON>ert,
  Paper
} from '@mui/material';
import {
  PlayArrow,
  Pause,
  Stop,
  CheckCircle,
  Error,
  Info,
  Warning,
  ExpandMore,
  Refresh
} from '@mui/icons-material';

interface ProcessInfo {
  task_id: string;
  process_type: string;
  status: string;
  start_time: string;
  end_time?: string;
  progress?: number;
  current_step?: string;
  error_message?: string;
  logs?: string[];
  parameters?: Record<string, any>;
}

interface ProcessMonitorProps {
  processes: ProcessInfo[];
  onRefresh?: () => void;
  onStop?: (taskId: string) => void;
}

const ProcessMonitor: React.FC<ProcessMonitorProps> = ({
  processes,
  onRefresh,
  onStop
}) => {
  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'running':
        return <PlayArrow color="primary" />;
      case 'completed':
        return <CheckCircle color="success" />;
      case 'failed':
      case 'error':
        return <Error color="error" />;
      case 'paused':
        return <Pause color="warning" />;
      case 'starting':
        return <Info color="info" />;
      default:
        return <Info />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'running':
        return 'primary' as const;
      case 'completed':
        return 'success' as const;
      case 'failed':
      case 'error':
        return 'error' as const;
      case 'paused':
        return 'warning' as const;
      case 'starting':
        return 'info' as const;
      default:
        return 'default' as const;
    }
  };

  const formatDuration = (startTime: string, endTime?: string) => {
    const start = new Date(startTime);
    const end = endTime ? new Date(endTime) : new Date();
    const duration = end.getTime() - start.getTime();
    
    const seconds = Math.floor(duration / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  };

  return (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">
            Process Monitor ({processes.length} active)
          </Typography>
          {onRefresh && (
            <IconButton onClick={onRefresh} size="small">
              <Refresh />
            </IconButton>
          )}
        </Box>
        
        {processes.length === 0 ? (
          <Alert severity="info">No active processes</Alert>
        ) : (
          <List>
            {processes.map((process, index) => (
              <React.Fragment key={process.task_id}>
                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMore />}>
                    <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                      <ListItemIcon sx={{ mr: 1 }}>
                        {getStatusIcon(process.status)}
                      </ListItemIcon>
                      
                      <Box sx={{ flexGrow: 1 }}>
                        <Typography variant="subtitle1">
                          {process.process_type}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Task ID: {process.task_id}
                        </Typography>
                      </Box>
                      
                      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', mr: 2 }}>
                        <Chip 
                          label={process.status} 
                          color={getStatusColor(process.status)}
                          size="small"
                          sx={{ mb: 1 }}
                        />
                        <Typography variant="caption" color="text.secondary">
                          {formatDuration(process.start_time, process.end_time)}
                        </Typography>
                      </Box>
                      
                      {onStop && (process.status === 'running' || process.status === 'starting') && (
                        <IconButton 
                          size="small" 
                          color="error"
                          onClick={(e) => {
                            e.stopPropagation();
                            onStop(process.task_id);
                          }}
                        >
                          <Stop />
                        </IconButton>
                      )}
                    </Box>
                  </AccordionSummary>
                  
                  <AccordionDetails>
                    <Box>
                      {/* Progress Bar */}
                      {process.progress !== undefined && (
                        <Box sx={{ mb: 2 }}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                            <Typography variant="body2">Progress</Typography>
                            <Typography variant="body2">{Math.round(process.progress)}%</Typography>
                          </Box>
                          <LinearProgress 
                            variant="determinate" 
                            value={process.progress} 
                            sx={{ height: 6, borderRadius: 3 }}
                          />
                        </Box>
                      )}
                      
                      {/* Current Step */}
                      {process.current_step && (
                        <Box sx={{ mb: 2 }}>
                          <Typography variant="body2" fontWeight="medium">
                            Current Step:
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {process.current_step}
                          </Typography>
                        </Box>
                      )}
                      
                      {/* Error Message */}
                      {process.error_message && (
                        <Alert severity="error" sx={{ mb: 2 }}>
                          {process.error_message}
                        </Alert>
                      )}

                      {/* Parameters */}
                      {process.parameters && Object.keys(process.parameters).length > 0 && (
                        <Box sx={{ mb: 2 }}>
                          <Typography variant="body2" fontWeight="medium">
                            Parameters:
                          </Typography>
                          <Paper variant="outlined" sx={{ p: 1, mt: 0.5, maxHeight: 100, overflowY: 'auto', backgroundColor: 'grey.100' }}>
                            <List dense disablePadding>
                              {Object.entries(process.parameters).map(([key, value]) => (
                                <ListItem key={key} disableGutters sx={{pb: 0.5}}>
                                  <ListItemText 
                                    primaryTypographyProps={{ variant: 'caption', fontWeight: 'medium' }} 
                                    secondaryTypographyProps={{
                                      variant: 'caption', 
                                      color: 'text.secondary', 
                                      sx: { whiteSpace: 'pre-wrap', wordBreak: 'break-all' } // Apply whiteSpace and wordBreak via sx prop
                                    }}
                                    primary={`${key}:`}
                                    secondary={typeof value === 'object' ? JSON.stringify(value) : String(value)}
                                  />
                                </ListItem>
                              ))}
                            </List>
                          </Paper>
                        </Box>
                      )}
                      
                      {/* Logs */}
                      <Typography variant="body2" fontWeight="medium" sx={{ mb: 1 }}>
                        Logs:
                      </Typography>
                      <Box
                        sx={{
                          minHeight: '100px',
                          maxHeight: '300px',
                          overflowY: 'auto',
                          backgroundColor: '#f5f5f5', // Light grey background
                          color: '#333', // Dark text
                          p: 1,
                          borderRadius: 1,
                          fontFamily: 'monospace',
                          fontSize: '0.875rem',
                          whiteSpace: 'pre-wrap', // Ensures lines wrap and formatting is kept
                          wordBreak: 'break-all', // Breaks long words to prevent overflow
                        }}
                      >
                        {process.logs && process.logs.length > 0 ? (
                          process.logs.map((log, logIndex) => (
                            <div key={logIndex}>{log}</div> // Each log line in a div
                          ))
                        ) : (
                          <Typography variant="caption" color="text.secondary">
                            No logs available for this process.
                          </Typography>
                        )}
                      </Box>
                    </Box>
                  </AccordionDetails>
                </Accordion>
                {index < processes.length - 1 && <Divider sx={{ my: 1 }} />}
              </React.Fragment>
            ))}
          </List>
        )}
      </CardContent>
    </Card>
  );
};

export default ProcessMonitor;
