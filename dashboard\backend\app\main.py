from fastapi import <PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
import logging
import os
import sys
import asyncio
import json

# Ensure the current directory is in Python path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from core.config import settings

# Fix Windows asyncio subprocess issue - MUST be done before any other imports
if os.name == 'nt':  # Windows
    asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    print("DEBUG: Set ProactorEventLoop policy in main.py")
    
    # Force creation of ProactorEventLoop if one doesn't exist
    try:
        loop = asyncio.get_event_loop()
        if not isinstance(loop, asyncio.ProactorEventLoop):
            print("DEBUG: Current loop is not Proactor<PERSON>ventLoop, creating new one")
            loop.close()
            new_loop = asyncio.ProactorEventLoop()
            asyncio.set_event_loop(new_loop)
            print("DEBUG: Successfully created new ProactorEventLoop")
    except RuntimeError:
        print("DEBUG: No event loop running, policy will take effect")

from api.routes import processes  # monitoring, data_quality
from ws_manager.manager import ConnectionManager, manager as ws_manager
from services.simple_orchestrator import orchestrator

# Configure logging
try:
    log_level = getattr(logging, settings.log_level.upper()) if isinstance(settings.log_level, str) else logging.INFO
except (AttributeError, TypeError):
    log_level = logging.INFO
logging.basicConfig(level=log_level)
logger = logging.getLogger(__name__)

# Create logs directory if it doesn't exist
os.makedirs(os.path.dirname(settings.log_file), exist_ok=True)

# Create FastAPI app
app = FastAPI(
    title=settings.project_name,
    description="HKEX Option Report Processing System Dashboard API",
    version="1.0.0",
    openapi_url=f"{settings.api_v1_str}/openapi.json"
)

# WebSocket connection managers
connection_manager = ConnectionManager()
websocket_manager = ws_manager

# Set up CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.backend_cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add security middleware
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["localhost", "127.0.0.1", "backend", "*.yourdomain.com"]
)

# Include routers
# app.include_router(
#     monitoring.router,
#     prefix=f"{settings.api_v1_str}/monitoring",
#     tags=["monitoring"]
# )

app.include_router(
    processes.router,
    prefix=f"{settings.api_v1_str}/processes",
    tags=["processes"]
)

# app.include_router(
#     data_quality.router,
#     prefix=f"{settings.api_v1_str}/data-quality",
#     tags=["data-quality"]
# )

@app.get("/")
async def root():
    return {
        "message": "HKEX Dashboard API",
        "version": "1.0.0",
        "docs_url": "/docs"
    }

@app.get(f"{settings.api_v1_str}/")
async def api_v1_root():
    return {
        "message": "HKEX Dashboard API v1",
        "version": "1.0.0",
        "endpoints": {
            "processes": f"{settings.api_v1_str}/processes",
            "health": "/health",
            "docs": "/docs"
        }
    }

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time updates."""
    client_id = f"client_{id(websocket)}"
    
    try:
        await websocket_manager.connect(websocket, client_id, "monitoring")

        # Send initial connection message
        await websocket_manager.send_personal_message({
            "type": "connection_established",
            "data": {"client_id": client_id},
            "timestamp": asyncio.get_event_loop().time()
        }, client_id)

        while True:
            # Wait for messages from client
            data = await websocket.receive_text()
            try:
                message = json.loads(data)

                # Handle different message types
                if message.get("type") == "subscribe":
                    # Client subscribing to specific updates
                    room = message.get("room", "monitoring")
                    await websocket_manager.broadcast_to_room({
                        "type": "subscription_confirmed",
                        "data": {"room": room},
                        "timestamp": asyncio.get_event_loop().time()
                    }, room)

                elif message.get("type") == "ping":
                    # Respond to ping with pong
                    await websocket_manager.send_personal_message({
                        "type": "pong",
                        "timestamp": asyncio.get_event_loop().time()
                    }, client_id)

                else:
                    # Echo unknown messages for debugging
                    await websocket_manager.send_personal_message({
                        "type": "echo",
                        "data": message,
                        "timestamp": asyncio.get_event_loop().time()
                    }, client_id)

            except json.JSONDecodeError:
                # Handle plain text messages
                await websocket_manager.send_personal_message({
                    "type": "echo",
                    "data": {"message": data},
                    "timestamp": asyncio.get_event_loop().time()
                }, client_id)

    except WebSocketDisconnect:
        websocket_manager.disconnect(client_id)
        logger.info(f"Client {client_id} disconnected from WebSocket")

# Background task to send periodic system health updates
async def send_periodic_updates():
    """Send periodic system health updates to all connected clients."""
    while True:
        try:
            # Send system health update every 30 seconds
            health_data = {
                "database_connection": True,
                "redis_connection": True,
                "active_processes": 0,
                "memory_usage_percent": 45.2,
                "disk_space_gb": 128.5
            }

            await websocket_manager.broadcast_system_alert({
                "system_health": health_data
            })

            await asyncio.sleep(30)
        except Exception as e:
            logger.error(f"Error in periodic updates: {e}")
            await asyncio.sleep(60)

@app.on_event("startup")
async def startup_event():
    """Initialize services on startup."""
    logger.info(f"Starting {settings.project_name} API server")
    logger.info(f"Environment: {settings.environment}")
    logger.info(f"Debug mode: {settings.debug}")

    # Initialize database tables
    try:
        from core.database import init_db
        init_db()
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        # Don't raise here as we want the app to continue starting
        # The error will be logged and the user can check database connectivity

    # Connect the orchestrator to the WebSocket manager
    orchestrator.set_websocket_manager(websocket_manager)
    logger.info("Process orchestrator connected to WebSocket manager")

    # Start background task for periodic updates
    asyncio.create_task(send_periodic_updates())

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown."""
    logger.info("Shutting down HKEX Dashboard API server")

@app.post("/start-process")
async def start_process_compatibility(request: dict):
    logger.info(f"Received /start-process request. Body: {request}") # Log the whole request body
    """
    Compatibility endpoint for frontend requests.
    Translates old-style requests to the new API format.
    """
    try:
        # Validate the process_type first
        process_type = request.get("process_type")
        if not process_type:
            raise HTTPException(status_code=400, detail="process_type is required")

        # Validate that it's a valid process type using dynamic configuration
        from services.script_config_service import script_config_service
        is_valid = await script_config_service.validate_process_type(process_type)
        if not is_valid:
            valid_types = await script_config_service.get_valid_process_types()
            raise HTTPException(status_code=400, detail=f"Invalid process_type '{process_type}'. Valid types: {valid_types}")

        # Transform request to match the expected structure
        process_request = {
            "process": process_type,
            "parameters": request.get("parameters", {})
        }
        logger.info(f"Initial parameters extracted from request: {process_request['parameters']}")

        # Handle python_interpreter if provided at the root of the request
        python_interpreter_from_request_root = request.get("python_interpreter")
        logger.info(f"Extracted 'python_interpreter' from request root: {python_interpreter_from_request_root}")

        if python_interpreter_from_request_root:
            # Normalize path for OS compatibility (e.g. for os.path.exists)
            path_to_check = os.path.normpath(python_interpreter_from_request_root)
            logger.info(f"Checking existence of normalized path: {path_to_check}")

            if os.path.exists(path_to_check) and os.path.isfile(path_to_check):
                logger.info(f"Path '{path_to_check}' exists and is a file. Setting as interpreter in parameters.")
                process_request["parameters"]["python_interpreter"] = python_interpreter_from_request_root # Use original form for params
            else:
                logger.warning(f"Path '{path_to_check}' does NOT exist or is not a file. Falling back to system Python.")
                process_request["parameters"]["python_interpreter"] = sys.executable
                logger.info(f"Falling back to system Python: {sys.executable}")
        elif "python_interpreter" not in process_request["parameters"]:
            # If not in root and not already in parameters, use system default
            logger.info("No python_interpreter in request root or parameters. Using system default.")
            process_request["parameters"]["python_interpreter"] = sys.executable
        else:
            logger.info(f"python_interpreter already present in parameters: {process_request['parameters']['python_interpreter']}")


        logger.info(f"Parameters being sent to orchestrator: {process_request['parameters']}")

        # Create a proper ProcessStartRequest and validate it
        from models.schemas import ProcessStartRequest
        validated_request = ProcessStartRequest(
            process=process_request["process"],
            parameters=process_request["parameters"]
        )

        # Forward to the orchestrator
        task_id = await orchestrator.start_process(
            validated_request.process,
            validated_request.parameters
        )

        return {"task_id": task_id, "message": "Process started successfully"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except FileNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to start process: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to start process: {str(e)}")

@app.websocket("/ws/")
async def websocket_endpoint_slash(websocket: WebSocket):
    """WebSocket endpoint for real-time updates (trailing slash)."""
    await websocket_endpoint(websocket)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
