#%%
import warnings
warnings.simplefilter(action='ignore', category=FutureWarning)
from plotly.subplots import make_subplots
import plotly.graph_objects as go
from sqlalchemy import create_engine
import pandas as pd
import ta
import time
from datetime import datetime
import os
from dotenv import load_dotenv
import yahoo_utils  # Import the new utility module
load_dotenv()

pd.options.mode.chained_assignment = None
date_fmt = '%Y-%m-%d'
pathname = os.getenv('out_path') + 'SROCFS_221101/'
today = datetime.today()
d1 = today.strftime("%y%m%d")
#Create directory if not exist
d1_path = f'{pathname}{d1}'
os.makedirs(d1_path, exist_ok=True)
print("Direcotry Created: ", d1_path)
#%%
# dft = pd.read_csv(pathname+'temp.csv', header=None)
dft = pd.read_csv(pathname+'tickers.csv', header=None)
dft.head()
# Index DF Benchmark 2021-02-18 Recent Index Peak
d0 = datetime.strptime('2022-11-01', date_fmt)
iticker ='^HSI'
roc_f=10    # ROC Period
roc_s=20
ema_n = 10 # EMA Period

# Get all tickers to process upfront
all_tickers = dft.sort_values(by=0, ascending=True)[0].tolist()
# Add the index ticker to the list
all_tickers.append(iticker)

# Download all ticker data in a single batch at the beginning
print(f"Downloading data for all {len(all_tickers)} tickers in a single batch...")
all_ticker_data = yahoo_utils.batch_download_and_cache(all_tickers, start=d0)

# Get the index data from the batch results
dfi = all_ticker_data[iticker]
dfi = dfi[~dfi.index.duplicated(keep='first')]
dfi.columns = dfi.columns.str.replace(' ', '_').str.lower()
dfi['roc_f'] = ta.momentum.ROCIndicator(dfi.close, window=roc_f).roc()
dfi['roc_s'] = ta.momentum.ROCIndicator(dfi.close, window=roc_s).roc()
dfi['sroc_f'] = ta.trend.EMAIndicator(dfi.roc_f, window=ema_n).ema_indicator()
dfi['sroc_s'] = ta.trend.EMAIndicator(dfi.roc_s, window=ema_n).ema_indicator()
dfi['i_sroc_f'] = dfi['sroc_f']
dfi['i_sroc_s'] = dfi['sroc_s']
# 0 relative to itself
dfi['r_roc_s'] = dfi['r_sroc_f'] = 0
dfi['ticker'] = iticker
# ### B Band
bb_n=10
dev=2
b_band = ta.volatility.BollingerBands(dfi.close, window=bb_n, window_dev=dev)
dfi['high_band'] = b_band.bollinger_hband().round(0)
dfi['mid_band'] = b_band.bollinger_mavg().round(0)
dfi['low_band'] = b_band.bollinger_lband().round(0)
dfi['close_PREV'] = dfi.close.shift(1)
dfi['under_low_band'] = dfi.apply(lambda x: 1 if (x.low < x.low_band) else 0,axis=1)
dfi['over_high_band'] = dfi.apply(lambda x: 1 if (x.high > x.high_band) else 0,axis=1)


#%%
df_sum=[]
# tickerStrings = [['2015.HK'], ['0270.HK'], ['0116.HK']]
# for r in tickerStrings:

# Process each ticker using the pre-downloaded data
# No need for batches since we already have all the data
for ticker in all_tickers:
    # Skip the index ticker as we've already processed it
    if ticker == iticker:
        continue

    try:
        symb_name = ticker

        # Get the ticker data from our pre-downloaded batch
        df = all_ticker_data[ticker].copy()

        df = df[~df.index.duplicated(keep='first')]
        df['ticker'] = ticker  # add this column because the dataframe doesn't contain a column with the ticker
        df.columns = df.columns.str.replace(' ', '_').str.lower()
        df['i_sroc_f'] = dfi.sroc_f
        df['i_roc_f'] = dfi.roc_f
        df['i_roc_s'] = dfi.roc_s
        df['i_sroc_s'] = dfi.sroc_s
        df['roc_f'] = ta.momentum.ROCIndicator(df.close, window=roc_f).roc()
        df['roc_s'] = ta.momentum.ROCIndicator(df.close, window=roc_s).roc()
        df['sroc_f'] = ta.trend.EMAIndicator(df.roc_f, window=ema_n).ema_indicator()
        df['sroc_s'] = ta.trend.EMAIndicator(df.roc_s, window=ema_n).ema_indicator()
        df['r_roc_f'] = df.roc_f-df.i_roc_f
        df['r_roc_s'] = df.roc_s-df.i_roc_s
        df['r_sroc_f'] = ta.trend.EMAIndicator(df.r_roc_f, window=ema_n).ema_indicator()
        df['r_sroc_s'] = ta.trend.EMAIndicator(df.r_roc_s, window=ema_n).ema_indicator()
        # ### B Band
        # bb_n=20
        # dev=2
        b_band = ta.volatility.BollingerBands(df.close, window=bb_n, window_dev=dev)
        df['high_band'] = b_band.bollinger_hband().round(2)
        df['mid_band'] = b_band.bollinger_mavg().round(2)
        df['low_band'] = b_band.bollinger_lband().round(2)
        df['close_PREV'] = df.close.shift(1)
        df['under_low_band'] = df.apply(lambda x: 1 if (x.low < x.low_band) else 0,axis=1)
        df['over_high_band'] = df.apply(lambda x: 1 if (x.high > x.high_band) else 0,axis=1)
        # df['long'] = (df.close <= df.low_band) # & (df.close_PREV > df.low_band)
        # df['long_price'] = df.low_band
        # df['short'] = (df.close >= df.high_band) # & (df.close_PREV < df.high_band)
        # df['short_price'] = df.high_band

        # Add latest row to df_sum
        # symb_sum = df.iloc[-1]
        # symb_sum = df.iloc[-1]
        symb_sum = df[['ticker', 'close', 'high_band', 'mid_band', 'low_band', 'under_low_band', 'over_high_band']].iloc[-1]
        symb_sum.at['txn_date']=df.index[-1]
        symb_sum.at['p_beat_index_f']=round((len(df[df.roc_f>df.i_roc_f])/len(df)), 2)
        symb_sum.at['p_beat_index_s']=round((len(df[df.roc_s>df.i_roc_s])/len(df)), 2)
        symb_sum.at['avg_r_roc_f']=round( df.r_roc_f.mean(), 2)
        symb_sum.at['avg_r_roc_s']=round( df.r_roc_s.mean(), 2)
        df_sum.append(symb_sum)
        # p_beatIndex=len(df[df.roc>df.i_roc])/len(df)
        # avg_r_roc= df.r_roc.mean()

        # Create traces
        fig = make_subplots(rows=3, cols=1,
                        shared_xaxes=True,
                        vertical_spacing=0.01)
        fig.add_trace(go.Ohlc(x=df.index,
                            name=ticker,
                            open=df['open'],
                            high=df['high'],
                            low=df['low'],
                            close=df['close']),row=1, col=1)
        fig.add_trace(go.Scatter(x=df.index, y=df.high_band,
                            line_dash="dot", line_color="blue", line_width=1,
                            name=f'BBand Hi'),row=1, col=1)
        fig.add_trace(go.Scatter(x=df.index, y=df.mid_band,
                            mode='lines', line_color='blue',
                            name=f'BBand Mid'),row=1, col=1)
        fig.add_trace(go.Scatter(x=df.index, y=df.low_band,
                            line_dash="dot", line_color="blue",line_width=1,
                            name=f'BBand Lo'),row=1, col=1)
        fig.add_hline(y=df['close'][0],
            annotation_text=f"{ticker} Open@{d0.strftime(date_fmt)} = {round(df['close'][0],2)}", annotation_position="bottom right",
            line_width=1, line_dash="dot", line_color="blue", row=1, col=1)
        fig.update_xaxes(rangeslider= {'visible':False}, row=1, col=1)
        # fig 2
        fig.add_trace(go.Ohlc(x=dfi.index,
                            name=iticker,
                            open=dfi['open'],
                            high=dfi['high'],
                            low=dfi['low'],
                            close=dfi['close']),row=2, col=1)
        fig.add_hline(y=dfi['close'][0],
            annotation_text=f"{iticker} Open@{d0.strftime(date_fmt)} = {round(dfi['close'][0])}", annotation_position="bottom right",
            line_width=1, line_dash="dot", line_color="green", row=2, col=1)
        fig.add_trace(go.Scatter(x=dfi.index, y=dfi.high_band,
                            line_dash="dot", line_color="green",line_width=1,
                            name=f'BBand Hi'),row=2, col=1)
        fig.add_trace(go.Scatter(x=dfi.index, y=dfi.mid_band,
                            mode='lines', line_color='green',
                            name=f'BBand Mid'),row=2, col=1)
        fig.add_trace(go.Scatter(x=dfi.index, y=dfi.low_band,
                            line_dash="dot", line_color="green",line_width=1,
                            name=f'BBand Lo'),row=2, col=1)
        fig.update_xaxes(rangeslider= {'visible':False}, row=2, col=1)
        # fig 3
        fig.add_trace(go.Scatter(x=df.index, y=df.r_sroc_s,
                            mode='lines', line_color='red',
                            name=f'Relative SROC {roc_s}d'),row=3, col=1)
        fig.add_trace(go.Scatter(x=df.index, y=df.r_sroc_f,
                            line_dash="dot", line_color="red",
                            name=f'Relative SROC {roc_f}d'),row=3, col=1)
        fig.add_trace(go.Scatter(x=df.index, y=df.i_sroc_s,
                            mode='lines', line_color="green", line_width=1,
                            name=f'{iticker} SROC_S'),row=3, col=1)
        # fig.add_trace(go.Scatter(x=df.index, y=df.sroc_f,
        #                     line_dash="dot", line_color='blue',
        #                     name=f'{ticker} SROC_F'),row=3, col=1)
        fig.add_trace(go.Scatter(x=df.index, y=df.sroc_s,
                            mode='lines', line_color='blue', line_width=1,
                            name=f'{ticker} SROC_S'),row=3, col=1)
        # fig.add_trace(go.Scatter(x=df.index, y=df.roc_s,
        #                     mode='markers', marker_color = 'blue',
        #                     marker_size = 3, opacity=0.5,
        #                     name=f'{ticker} ROC'),row=3, col=1)
        # fig.add_trace(go.Scatter(x=df.index, y=df.i_roc_s,
        #                     mode='markers', marker_color = 'green',
        #                     marker_size = 3, opacity=0.5,
        #                     name=f'{iticker} ROC'),row=3, col=1)
        fig.add_hline(y=0, line_width=1, line_color="black", row=3, col=1)
        # Layout
        fig.update_layout(
            width=1920,
            height=1080,
            title={
                'text': f"{symb_name}({ticker}) vs {iticker} ({d0.strftime(date_fmt)} - {today.strftime(date_fmt)}) " +\
                f"Days above {iticker} {roc_f}d/{roc_s}d ={symb_sum.at['p_beat_index_f']*100:.2f}%/{symb_sum.at['p_beat_index_s']*100:.2f}%    " + \
                f"Avg Relative ROC {roc_f}d/{roc_s}d ={symb_sum.at['avg_r_roc_f']:.2f}%/{symb_sum.at['avg_r_roc_s']:.2f}%]",
                # 'y':0.88,
                # 'x':0.18,
                'xanchor': 'left',
                'yanchor': 'top'},
                title_font_color="red",
            xaxis3_title = 'Date',
            yaxis1_title = ticker ,
            yaxis2_title = iticker,
            yaxis3_title = 'ROC %',
            xaxis=dict(tickformat="%y-%m"),
            legend=dict(
                yanchor="bottom",
                y=0.03,
                xanchor="left",
                x=0.05))
        fig.update_xaxes(dtick="M1",tickformat="%b\n%Y")
        fig.write_image( f'{d1_path}/{d1}_{ticker}.png')
        print( f'{d1_path}/{d1}_{ticker}.png')
        # fig.show()
    except Exception as e:
        print(f'{ticker}-{iticker} {e}')
        continue

# %%
symb_sum = dfi[['ticker', 'close', 'high_band', 'mid_band', 'low_band', 'under_low_band', 'over_high_band']].iloc[-1]
symb_sum.at['txn_date']=dfi.index[-1]
symb_sum.at['p_beat_index_f']=0.5
symb_sum.at['p_beat_index_s']=0.5
symb_sum.at['avg_r_roc_f']=0
symb_sum.at['avg_r_roc_s']=0
df_sum.append( symb_sum)
df_sum=pd.DataFrame(df_sum)
df_sum
# %%
os.makedirs(f'{pathname}xls', exist_ok=True)
print("Direcotry Created: ",f'{pathname}xls')
xls_path= f'{pathname}xls/{d1}_{roc_f}_{roc_s}_summary.xlsx'
df_sum.to_excel(xls_path, index=False)
print(f'XLS Saved: {xls_path}')
# %%
# Write to DB
db = os.environ.get('WILL6700_DB')
print(f"{db=}")
remote_db = create_engine(db)
tname='stock_sroc_fs'

# Use SQLAlchemy 2.0 style with connection context manager
with remote_db.connect() as conn:
    df_sum.to_sql(name=tname, con=conn, if_exists='replace', index=False)
    conn.commit()

print(f"Data written to database table: {tname}")

# %%
