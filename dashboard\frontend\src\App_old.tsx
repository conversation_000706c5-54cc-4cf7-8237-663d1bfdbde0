import React from 'react';
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate
} from 'react-router-dom';
import {
  ThemeProvider,
  createTheme,
  CssBaseline,
  AppBar,
  Toolbar,
  Typography,
  Container,
  Box,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider
} from '@mui/material';
import {
  Dashboard,
  Settings,
  BugReport,
  Assessment,
  PlayArrow
} from '@mui/icons-material';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import MainDashboard from './pages/MainDashboard';
import ProcessControl from './pages/ProcessControl';
import DataQuality from './pages/DataQuality';
import Troubleshooting from './pages/Troubleshooting';

const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
});

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3,
      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
  },
});

const drawerWidth = 240;

const App: React.FC = () => {
  const navigationItems = [
    { text: 'Dashboard', icon: <Dashboard />, path: '/' },
    { text: 'Process Control', icon: <PlayArrow />, path: '/process-control' },
    { text: 'Data Quality', icon: <Assessment />, path: '/data-quality' },
    { text: 'Troubleshooting', icon: <BugReport />, path: '/troubleshooting' },
  ];

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <Router>
          <Box sx={{ display: 'flex' }}>
            {/* App Bar */}
            <AppBar
              position="fixed"
              sx={{ zIndex: (theme) => theme.zIndex.drawer + 1 }}
            >
              <Toolbar>
                <Typography variant="h6" noWrap component="div">
                  HKEX Option Processing Dashboard
                </Typography>
              </Toolbar>
            </AppBar>

            {/* Side Navigation */}
            <Drawer
              variant="permanent"
              sx={{
                width: drawerWidth,
                flexShrink: 0,
                [`& .MuiDrawer-paper`]: {
                  width: drawerWidth,
                  boxSizing: 'border-box',
                },
              }}
            >
              <Toolbar />
              <Box sx={{ overflow: 'auto' }}>
                <List>
                  {navigationItems.map((item) => (
                    <ListItem
                      button
                      key={item.text}
                      component="a"
                      href={item.path}
                    >
                      <ListItemIcon>{item.icon}</ListItemIcon>
                      <ListItemText primary={item.text} />
                    </ListItem>
                  ))}
                </List>
                <Divider />
              </Box>
            </Drawer>

            {/* Main Content */}
            <Box component="main" sx={{ flexGrow: 1, p: 3 }}>
              <Toolbar />
              <Container maxWidth="xl">
                <Routes>
                  <Route path="/" element={<MainDashboard />} />
                  <Route path="/process-control" element={<ProcessControl />} />
                  <Route path="/data-quality" element={<DataQuality />} />
                  <Route path="/troubleshooting" element={<Troubleshooting />} />
                  <Route path="*" element={<Navigate to="/" replace />} />
                </Routes>
              </Container>
            </Box>
          </Box>
        </Router>
      </ThemeProvider>
    </QueryClientProvider>
  );
};

export default App;
