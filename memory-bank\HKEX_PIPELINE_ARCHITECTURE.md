# HKEX Pipeline Architecture

## Overview

The HKEX data processing system has been refactored into a modular pipeline architecture with clear separation of concerns. This document describes the new architecture and how to use it.

## Architecture Principles

### 1. **Single Responsibility Principle**
Each module has one clear responsibility:
- **Fetcher**: Only downloads and saves HTML reports
- **Parser**: Only extracts raw data from HTML files  
- **Processor**: Only performs calculations (Black-Scholes, etc.)
- **Pipeline**: Only orchestrates the workflow

### 2. **Dependency Injection**
Functions receive their dependencies as parameters rather than importing them directly, making the system more testable and flexible.

### 3. **Data Flow Separation**
Clear data flow through the pipeline:
```
HTML Reports → Raw Data → Processed Data → Database
```

## Module Structure

```
scripts/
├── hkex_fetcher.py      # HTTP operations and file saving
├── hkex_parser.py       # HTML parsing and data extraction
├── hkex_processor.py    # Black-Scholes calculations
├── hkex_pipeline.py     # Workflow orchestration
└── UpdateIndexOptionPostgres.py  # Main application (updated)
```

## Module Details

### 1. `hkex_fetcher.py` - Data Fetching

**Responsibility**: Download HKEX reports and save to local files

**Key Functions**:
- `fetch_daily_report(symb, trade_date, pathname)` - Download daily option reports
- `fetch_weekly_report(symb, trade_date, pathname)` - Download weekly option reports  
- `fetch_hti_report(symb, trade_date, pathname)` - Download HTI reports
- `fetch_historical_reports(symb, start_date, end_date, pathname)` - Bulk download
- `test_hkex_connection()` - Test HKEX website connectivity
- `safe_http_get(url, ...)` - Robust HTTP requests with retry logic

**What it knows**: HTTP protocols, HKEX URLs, file I/O
**What it doesn't know**: HTML structure, option calculations, database operations

### 2. `hkex_parser.py` - Data Parsing

**Responsibility**: Extract raw trading data from saved HTML files

**Key Classes**:
- `HKEXReportParser` - Main parser class

**Key Functions**:
- `parse_daily_report_file(file_path, symb, trade_date)` - Parse daily reports
- `parse_weekly_report_file(file_path, symb, trade_date)` - Parse weekly reports
- `parse_hti_report_file(file_path, symb, trade_date)` - Parse HTI reports

**Data Output Format**:
```python
{
    'inst_name': 'HSI.DEC.25000.C',
    'contract_month': 'DEC',
    'strike_price': 25000,
    'call_put': 'C',
    'txn_date': datetime(2024, 12, 13),
    'prev_date': datetime(2024, 12, 12),
    'prev_open': 1250, 'prev_high': 1300, 'prev_low': 1200, 'prev_close': 1275, 'prev_volume': 100,
    'curr_open': 1280, 'curr_high': 1320, 'curr_low': 1260, 'curr_close': 1300, 'curr_volume': 150,
    'iv_percent': 18,  # Implied volatility as percentage
    'open_interest': 5000,
    'oi_change': 100
}
```

**What it knows**: HTML structure, HKEX report formats, data extraction
**What it doesn't know**: Option pricing, calculations, database schemas

### 3. `hkex_processor.py` - Data Processing

**Responsibility**: Add calculated fields using Black-Scholes and other option models

**Key Classes**:
- `HKEXOptionProcessor` - Main processor class

**Key Functions**:
- `process_daily_option_data(parsed_data, symb, trade_date, get_price_func)`
- `process_weekly_option_data(parsed_data, symb, trade_date, get_price_func)`
- `process_hti_option_data(parsed_data, symb, trade_date, get_price_func)`

**Added Fields**:
```python
{
    # ... all fields from parser ...
    'stock_price': 25000.0,
    'time_to_expiry': 0.0821,  # Years
    'iv_decimal': 0.18,        # IV as decimal
    'd1': 0.1234,              # Black-Scholes d1
    'delta': 0.5234,           # Option delta
    'gamma': 0.0012,           # Option gamma
    'charm': -0.0001,          # Delta decay
    'moneyness': 1.0,          # Strike/Spot ratio
    'option_type': 'ATM',      # ITM/ATM/OTM
    'theoretical_value': 1250.5,
    'iv_rank': 'MEDIUM'        # HIGH/MEDIUM/LOW
}
```

**What it knows**: Option pricing models, mathematical calculations, risk metrics
**What it doesn't know**: HTML parsing, HTTP operations, database operations

### 4. `hkex_pipeline.py` - Workflow Orchestration

**Responsibility**: Coordinate the entire data processing workflow

**Key Classes**:
- `HKEXPipeline` - Main pipeline orchestrator

**Key Methods**:
- `process_daily_report(symb, trade_date)` - Complete daily workflow
- `process_weekly_report(symb, trade_date)` - Complete weekly workflow  
- `process_hti_report(symb, trade_date)` - Complete HTI workflow
- `process_historical_reports(symb, start_date, end_date)` - Bulk processing
- `run_health_check()` - System health verification

**Workflow Steps**:
1. **Fetch**: Download report from HKEX
2. **Parse**: Extract raw data from HTML
3. **Process**: Add calculated fields
4. **Store**: Save to database
5. **Report**: Return processing statistics

**What it knows**: Workflow coordination, error handling, progress tracking
**What it doesn't know**: Implementation details of individual steps

## Usage Examples

### Basic Usage

```python
from hkex_pipeline import HKEXPipeline
from datetime import date

# Create pipeline instance (debug mode auto-detected from .env)
pipeline = HKEXPipeline(
    pathname="./data/",
    get_price_func=your_price_function,
    save_data_func=your_save_function
)

# Process a single daily report
results = pipeline.process_daily_report('HSI', date(2024, 12, 13))
print(f"Processed {results['records_saved']} records")

# Run health check (respects debug mode)
health = pipeline.run_health_check()
```

### Debug Mode Usage

```python
from hkex_pipeline import HKEXPipeline

# Explicitly enable debug mode (overrides .env setting)
pipeline_debug = HKEXPipeline(
    pathname="./data/",
    get_price_func=your_price_function,
    save_data_func=your_save_function,
    debug_mode=True  # Disables connection testing
)

# Explicitly disable debug mode (overrides .env setting)
pipeline_prod = HKEXPipeline(
    pathname="./data/",
    get_price_func=your_price_function,
    save_data_func=your_save_function,
    debug_mode=False  # Enables connection testing
)

# Auto-detect from environment (default behavior)
pipeline_auto = HKEXPipeline(
    pathname="./data/",
    get_price_func=your_price_function,
    save_data_func=your_save_function
    # debug_mode=None (default) - reads from HKEX_DEBUG env var
)
```

### Advanced Usage - Custom Processing

```python
from hkex_fetcher import fetch_daily_report
from hkex_parser import parse_daily_report_file
from hkex_processor import process_daily_option_data

# Step-by-step processing for custom workflows
file_path, success = fetch_daily_report('HSI', date(2024, 12, 13), "./data/")
if success:
    raw_data, summary = parse_daily_report_file(file_path, 'HSI', date(2024, 12, 13))
    processed_data = process_daily_option_data(raw_data, 'HSI', date(2024, 12, 13), get_price_func)
    # Custom processing here...
```

### Backward Compatibility

```python
# Old functions still work but now use the new pipeline internally
from UpdateIndexOptionPostgres import getDailyMarketReport

summary_counts = getDailyMarketReport('HSI', date(2024, 12, 13))
```

## Configuration

### Environment Variables
- `out_path` - Base path for file operations
- `WILL9700_DB` - Database connection string
- `HKEX_DEBUG` - Debug mode flag (true/false) - **NEW**

### Debug Mode Configuration

**Purpose**: Disable connection testing for production daily runs to improve performance and avoid unnecessary network calls.

**Environment Variable**: `HKEX_DEBUG`
**Valid Values**: `true`, `false`, `1`, `0`, `yes`, `no`, `on`, `off` (case insensitive)
**Default**: `false` (production mode with connection testing enabled)

**Usage**:
```bash
# In your .env file

# For daily production runs (recommended)
HKEX_DEBUG=true

# For development and testing
HKEX_DEBUG=false
```

**What gets disabled in debug mode**:
- HKEX website connectivity tests
- Specific report URL accessibility tests
- Network-based health checks

**What still works in debug mode**:
- All data fetching operations
- Report parsing and processing
- Database operations
- File system operations
- Environment variable checks

### Dependencies
- `requests` - HTTP operations
- `beautifulsoup4` - HTML parsing
- `scipy` - Statistical functions
- `pandas` - Data manipulation
- `sqlalchemy` - Database operations

## Error Handling

### Robust HTTP Operations
- Automatic retries with exponential backoff
- Bot detection avoidance
- Timeout handling
- Connection pooling

### Graceful Degradation
- Continue processing if individual records fail
- Detailed error logging
- Partial success reporting

### Data Validation
- Input validation at each stage
- Type checking and conversion
- Missing data handling

## Testing

### Unit Tests
```bash
python scripts/test_hkex_module.py
```

### Health Checks
```python
pipeline = HKEXPipeline(pathname, get_price_func, save_data_func)
health_results = pipeline.run_health_check()
```

### Integration Tests
- Environment setup verification
- HKEX connectivity testing
- End-to-end workflow validation

## Performance Considerations

### Caching
- HTTP session reuse
- Connection pooling
- Parsed data caching

### Batch Processing
- Bulk historical data fetching
- Parallel processing capabilities
- Memory-efficient streaming

### Database Optimization
- Batch inserts
- Transaction management
- Index optimization

## Migration Guide

### From Old Architecture
1. **Immediate**: Old functions continue to work
2. **Gradual**: Replace individual function calls with pipeline methods
3. **Complete**: Use new pipeline architecture for all new development

### Breaking Changes
- Function signatures updated to use dependency injection
- Data format standardized across all report types
- Error handling improved with detailed reporting

## Future Enhancements

### Planned Features
- Real-time data streaming
- Advanced analytics pipeline
- Machine learning integration
- Multi-exchange support

### Extensibility Points
- Custom processors for new calculations
- Additional data sources
- Alternative storage backends
- Custom workflow orchestration

## Support

### Documentation
- Inline code documentation
- Type hints throughout
- Example usage in docstrings

### Debugging
- Comprehensive logging
- Error tracking
- Performance monitoring

### Maintenance
- Modular design for easy updates
- Clear separation of concerns
- Comprehensive test coverage
