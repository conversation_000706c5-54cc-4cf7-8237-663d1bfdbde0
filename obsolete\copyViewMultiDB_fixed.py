#%%
import pandas as pd
import os
import psycopg2
from dotenv import load_dotenv
import pymysql
from sqlalchemy import create_engine, text
from sqlalchemy.types import String, Integer, Float, Date, DECIMAL
load_dotenv()

local_db_url= os.environ.get('WILL9700_DB')

db = os.environ.get('CC_DATABASE')
print(f"{db=}")
#db ="postgresql://vdpbbxpmswyyfo:<EMAIL>:5432/d27tclro6qeosh"

db2 = os.environ.get('AIVEN_DATABASE_URL')
print(f"{db2=}")
#db="postgresql://avnadmin:<EMAIL>:16762/defaultdb?sslmode=require"

db3 = os.environ.get('NEON_DATABASE_URL')
print(f"{db3=}")

remote_db_urls = [db, db2, db3]
# remote_db_urls = [db2]

def get_sqlalchemy_dtype_mapping(df, table_name):
    """
    Create SQLAlchemy data type mapping based on DataFrame dtypes and table structure.
    This prevents pandas from incorrectly inferring column types during to_sql().
    """
    dtype_mapping = {}
    
    for column in df.columns:
        # Get pandas dtype
        pd_dtype = str(df[column].dtype)
        
        # Map pandas dtypes to SQLAlchemy types
        if pd_dtype.startswith('int'):
            dtype_mapping[column] = Integer()
        elif pd_dtype.startswith('float'):
            # Use DECIMAL for financial data to preserve precision
            if any(keyword in column.lower() for keyword in ['price', 'value', 'delta', 'gamma', 'theta', 'vega', 'rho', 'strike', 'spot', 'iv']):
                dtype_mapping[column] = DECIMAL(precision=15, scale=6)
            else:
                dtype_mapping[column] = Float()
        elif pd_dtype == 'object':
            # Check if it's actually numeric data stored as object
            try:
                # Try to convert to numeric to see if it's really numeric data
                pd.to_numeric(df[column], errors='raise')
                dtype_mapping[column] = DECIMAL(precision=15, scale=6)
            except (ValueError, TypeError):
                # It's really text data
                max_length = df[column].astype(str).str.len().max() if len(df) > 0 else 50
                dtype_mapping[column] = String(max(max_length, 50))
        elif 'datetime' in pd_dtype or 'date' in pd_dtype:
            dtype_mapping[column] = Date()
        else:
            # Default to String for unknown types
            dtype_mapping[column] = String(255)
    
    return dtype_mapping

def ensure_numeric_types(df):
    """
    Ensure numeric columns are properly typed before saving to database.
    """
    df_copy = df.copy()
    
    # Common numeric columns in option data
    numeric_columns = [
        'price', 'value', 'delta', 'gamma', 'theta', 'vega', 'rho',
        'strike', 'spot', 'iv', 'volume', 'oi', 'open', 'high', 'low', 'close'
    ]
    
    for col in df_copy.columns:
        # Check if column name suggests it should be numeric
        if any(num_col in col.lower() for num_col in numeric_columns):
            try:
                # Convert to numeric, handling errors gracefully
                df_copy[col] = pd.to_numeric(df_copy[col], errors='coerce')
            except Exception as e:
                print(f"Warning: Could not convert {col} to numeric: {e}")
    
    return df_copy

#q= f"select * from public.test;"
#q=f"select txn_date from option_daily_report where txn_id = (select max(txn_id) from option_daily_report);"
#txn_date= pd.read_sql(q, local_db)

#%%
# Check Last Txn Date for option_daily_report
# avoid lookup view due to performance
local_db = create_engine(local_db_url)
start = pd.Timestamp.now()
q_date=f"select max(txn_date) from option_daily_report;"
print(q_date)
index_txn_date= pd.read_sql(q_date, local_db).iloc[0][0]
q_date=f"select max(txn_date) from stock_option_report;"
print(q_date)
stock_txn_date= pd.read_sql(q_date, local_db).iloc[0][0]
end = pd.Timestamp.now()
print(f"Time to select last txn date: {end - start}")    
print(f'option_daily_report Last Txn Date: {index_txn_date=}, {stock_txn_date=}')

# option_value_tables =['v_index_option_value','v_weekly_option_value','v_stock_option_value' , 'v_weekly_iv','v_monthly_iv'  ]
option_value_tables =['v_weekly_iv','v_monthly_iv'  ]
# option_value_tables =['v_index_option_value','v_weekly_option_value']
# option_value_tables =['v_stock_option_value' ]
for t in option_value_tables:
    print(f"[{t}] ################## Processing from Local DB: {local_db}")
    # Read from Local
    # Keep Last day's Txn Only due to Heroku Free Plan Limitation
    if t=='v_stock_option_value':
        txn_date=stock_txn_date
    else:
        txn_date=index_txn_date
    q=f"select * from {t} where txn_date='{txn_date}';"
    print(q)
    start = pd.Timestamp.now()
    df= pd.read_sql(q, local_db)
    end = pd.Timestamp.now()
    print(f"Rows = {len(df)} Selected {t} from Local db: {local_db} ")
    print(f"Time to read {t} from Local db: {end - start}")

    # Apply numeric type conversion before saving
    df = ensure_numeric_types(df)
    
    # Get SQLAlchemy data type mapping for proper column types
    dtype_mapping = get_sqlalchemy_dtype_mapping(df, t)
    
    print(f"Data types for {t}:")
    for col, dtype in dtype_mapping.items():
        print(f"  {col}: {dtype}")

    for remote_db_url in remote_db_urls:
        try:
            remote_db = create_engine(remote_db_url)
            print(f">>>>>>>>>>>> Created Remote DB: {remote_db}")
            
            # Truncate Remote
            try:
                start = pd.Timestamp.now()
                with remote_db.connect() as conn:
                    conn.execute(text(f'TRUNCATE TABLE {t};'))
                    conn.commit()
                end = pd.Timestamp.now()
                print(f"Time to Truncate {t} from Remote db: {end - start}")
            except Exception as e:
                print(f"Error Truncate table {t} =>" + str(e), flush=True)
                # print("Skip Truncate table")
            
            # Save to Remote
            print(f"Save {t} to Remote DB: {remote_db}")
            start = pd.Timestamp.now()
            df.to_sql(name=t, con=remote_db, if_exists='append', index=False, dtype=dtype_mapping)
            end = pd.Timestamp.now()
            print(f"Time to Save {t} to Remote db: {end - start}")

            # Check Results
            q_insert = f"select txn_date, count(*) from {t} where txn_date>='{txn_date}' group by txn_date;"
            start = pd.Timestamp.now()
            check_insert = pd.read_sql(q_insert, remote_db)
            end = pd.Timestamp.now()
            print(q_insert)
            print(check_insert)
            print(f"Time to Check {t} from Remote db: {end - start}")
            remote_db.dispose()
            print(f"<<<< Disposed Remote DB: {remote_db}")
        except Exception as e:
            print(f"Error in processing table {t} =>" + str(e), flush=True)

local_db.dispose()

#%%
# remote_db2.dispose()

# q=f"select * from v_weekly_option_value where txn_date>'{txn_date}';"
# df= pd.read_sql(q, local_db)
# df.to_sql(name='v_weekly_option_value', con=remote_db, if_exists = 'append', index=False)    
#df2= pd.read_sql(q, remote_db)

#%%
# q=f"select * from v_stock_option_value where txn_date>'{txn_date}';"
# df= pd.read_sql(q, local_db)
# df.to_sql(name='v_stock_option_value', con=remote_db, if_exists = 'append', index=False)    
#df2= pd.read_sql(q, remote_db)

#%%
# C:\Users\<USER>\Users\willp>heroku run -a cbbc-db python saveCBBC_XLS.py
