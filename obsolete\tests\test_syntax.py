#!/usr/bin/env python3
"""Test imports to verify syntax is correct."""
import sys
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

try:
    print("Testing imports...")
    from app.services.simple_orchestrator import orchestrator
    print("✓ Simple orchestrator imported successfully")
    
    from app.main import app
    print("✓ Main app imported successfully")
    
    print("✓ All imports successful - no syntax errors!")
    
except Exception as e:
    print(f"✗ Import error: {e}")
    import traceback
    traceback.print_exc()
