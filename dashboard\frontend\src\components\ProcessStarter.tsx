import React, { useState } from 'react';
import {
  Card,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  Box,
  Button,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  Alert,
  Switch,
  FormControlLabel
} from '@mui/material';
import { <PERSON><PERSON>rrow, Settings } from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';

// interface ProcessType {
//   value: string;
//   label: string;
//   description: string;
//   required_parameters: string[];
//   optional_parameters: string[];
// }

// interface ProcessConfiguration {
//   description: string;
//   timeout: number;
//   requires_params: string[];
//   optional_params: string[];
// }

interface ProcessStarterProps {
  processTypes: any;
  onProcessStarted?: () => void;
  onStartProcess?: (processType: string, parameters?: Record<string, any>) => Promise<void>;
  disabled?: boolean;
}

const ProcessStarter: React.FC<ProcessStarterProps> = ({
  processTypes,
  onProcessStarted,
  onStartProcess,
  disabled = false,
}) => {
  const [selectedProcess, setSelectedProcess] = useState<string>('');
  const [dialogOpen, setDialogOpen] = useState(false);
  const [parameters, setParameters] = useState<Record<string, any>>({});
  const [isStarting, setIsStarting] = useState(false);

  const openDialog = (processType: string) => {
    setSelectedProcess(processType);
    setParameters({});
    setDialogOpen(true);
  };

  const closeDialog = () => {
    setDialogOpen(false);
    setSelectedProcess('');
    setParameters({});
  };

  const handleParameterChange = (paramName: string, value: any) => {
    setParameters(prev => ({
      ...prev,
      [paramName]: value
    }));
  };
  const handleStartProcess = async () => {
    if (!selectedProcess) return;
    
    setIsStarting(true);
    try {
      // Use external start process function if provided, otherwise use internal API call
      if (onStartProcess) {
        await onStartProcess(selectedProcess, parameters);
      } else {
        // Make API call to start the process
        const apiParameters = {
          ...parameters
        };

        console.log("ProcessStarter attempting to send payload:", { process: selectedProcess, parameters: apiParameters }); // Log payload before fetch

        const response = await fetch(`${process.env.REACT_APP_API_BASE_URL}/api/v1/processes/start`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            process: selectedProcess, // Changed from process_type to process
            parameters: apiParameters // Pass the combined parameters
          })
        });

        if (!response.ok) {
          let errorDetail = response.statusText;
          let rawResponseText = ''; // Variable to store raw response
          try {
            // Clone the response to be able to read it multiple times (once as text, once as JSON)
            const responseCloneForText = response.clone();
            rawResponseText = await responseCloneForText.text(); // Read as text first

            const errorData = await response.json(); // Try to parse original response as JSON
            console.error("Detailed 422 error from backend (JSON):", JSON.stringify(errorData, null, 2)); // Log the full error object
            if (errorData && errorData.detail) {
              if (Array.isArray(errorData.detail)) {
                // Format Pydantic's list of error objects
                errorDetail = errorData.detail.map((err: any) => { // Added :any type for err
                  const loc = err.loc ? err.loc.join(' -> ') : 'N/A';
                  return `Field: '${loc}', Message: ${err.msg} (type: ${err.type})`;
                }).join('; ');
              } else if (typeof errorData.detail === 'string') {
                errorDetail = errorData.detail;
              } else {
                errorDetail = JSON.stringify(errorData.detail);
              }
            }
          } catch (e) {
            console.error("Could not parse error JSON from 422 response. Raw response text:", rawResponseText, "Error during JSON parsing:", e);
            // If JSON parsing failed, but we have rawResponseText, use it for the error detail.
            if (rawResponseText) {
              errorDetail = rawResponseText;
            }
          }
          throw new Error(`Failed to start process: ${response.status} - ${errorDetail}`);
        }

        const result = await response.json();
        console.log('Process started:', result);
      }
      
      closeDialog();
      
      // Call the callback if provided
      if (onProcessStarted) {
        onProcessStarted();
      }
    } catch (error) {
      console.error('Failed to start process:', error);
    } finally {
      setIsStarting(false);
    }
  };

  const getSelectedProcessInfo = () => {
    if (!processTypes || !selectedProcess) return null;
    return processTypes[selectedProcess];
  };

  const renderParameterInput = (paramName: string, isRequired: boolean) => {
    const value = parameters[paramName] || '';
    
    // Special handling for common parameter types
    if (paramName.includes('date')) {
      return (
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          <DatePicker
            label={paramName}
            value={value ? new Date(value) : null}
            onChange={(newValue) => {
              if (newValue) {
                const dateStr = newValue.toISOString().split('T')[0];
                handleParameterChange(paramName, dateStr);
              }
            }}
            slots={{
              textField: TextField
            }}
            slotProps={{
              textField: {
                fullWidth: true,
                required: isRequired,
                margin: 'normal'
              }
            }}
          />
        </LocalizationProvider>
      );
    }
    
    if (paramName === 'dry_run') {
      return (
        <FormControlLabel
          control={
            <Switch
              checked={Boolean(value)}
              onChange={(e) => handleParameterChange(paramName, e.target.checked)}
            />
          }
          label="Dry Run (preview only)"
        />
      );
    }
    
    if (paramName.includes('db') || paramName.includes('database')) {
      return (
        <FormControl fullWidth margin="normal" required={isRequired}>
          <InputLabel>{paramName}</InputLabel>
          <Select
            value={value}
            onChange={(e) => handleParameterChange(paramName, e.target.value)}
            label={paramName}
          >
            <MenuItem value="production">Production</MenuItem>
            <MenuItem value="staging">Staging</MenuItem>
            <MenuItem value="development">Development</MenuItem>
          </Select>
        </FormControl>
      );
    }
    
    if (paramName.includes('size') || paramName.includes('batch')) {
      return (
        <TextField
          fullWidth
          margin="normal"
          label={paramName}
          type="number"
          value={value}
          onChange={(e) => handleParameterChange(paramName, parseInt(e.target.value) || '')}
          required={isRequired}
          inputProps={{ min: 1, max: 10000 }}
        />
      );
    }
    
    // Default text input
    return (
      <TextField
        fullWidth
        margin="normal"
        label={paramName}
        value={value}
        onChange={(e) => handleParameterChange(paramName, e.target.value)}
        required={isRequired}
      />
    );
  };
  const selectedProcessInfo = getSelectedProcessInfo();

  return (
    <>
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Process Control
          </Typography>
          <Typography variant="body2" color="text.secondary" component="div" sx={{ mb: 3 }}>
            Select and configure processes to run
          </Typography>
          
          {!processTypes ? (
            <Typography variant="body2" color="text.secondary" component="div">
              Loading process types...
            </Typography>
          ) : (
            <Grid container spacing={2}>
              {Object.entries(processTypes).map(([key, process]: [string, any]) => {
                // Defensive check for process object
                if (!process || typeof process !== 'object') {
                  console.warn(`ProcessStarter: Skipping rendering for key '${key}' because process is not a valid object:`, process);
                  return null; // Don't render this item if process is not a valid object
                }

                return (
                  <Grid item xs={12} sm={6} md={4} key={key}>
                    <Card variant="outlined" sx={{ height: '100%' }}>
                      <CardContent sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
                        <Typography variant="subtitle1" gutterBottom>
                          {process.label || process.description || key}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ flexGrow: 1, mb: 2 }}>
                          {process.description || 'No description available'}
                        </Typography>
                        
                        {/* Parameter chips */}
                        <Box sx={{ mb: 2 }}>
                          {(process.required_parameters || []).map((param: string) => (
                            <Chip
                              key={param}
                              label={param}
                              size="small"
                              color="primary"
                              variant="outlined"
                              sx={{ mr: 0.5, mb: 0.5 }}
                            />
                          ))}
                          {(process.optional_parameters || []).map((param: string) => (
                            <Chip
                              key={param}
                              label={param}
                              size="small"
                              color="default"
                              variant="outlined"
                              sx={{ mr: 0.5, mb: 0.5 }}
                            />
                          ))}
                        </Box>
                        
                        <Button
                          variant="contained"
                          startIcon={<PlayArrow />}
                          onClick={() => openDialog(key)}
                          disabled={disabled}
                          fullWidth
                        >
                          Configure & Start
                        </Button>
                      </CardContent>
                    </Card>
                  </Grid>
                );
              })}
            </Grid>
          )}
        </CardContent>
      </Card>

      {/* Parameter Configuration Dialog */}
      <Dialog open={dialogOpen} onClose={closeDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Settings sx={{ mr: 1 }} />
            Configure {selectedProcessInfo?.label || selectedProcess}
          </Box>
        </DialogTitle>
        
        <DialogContent>
          {selectedProcessInfo && (
            <>
              <Alert severity="info" sx={{ mb: 3 }}>
                {selectedProcessInfo.description || 'Configure the parameters for this process'}
              </Alert>
              
              {/* Required Parameters */}
              {selectedProcessInfo.required_parameters && selectedProcessInfo.required_parameters.length > 0 && (
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" gutterBottom color="primary">
                    Required Parameters
                  </Typography>
                  {selectedProcessInfo.required_parameters.map((param: string) => (
                    <Box key={param}>
                      {renderParameterInput(param, true)}
                    </Box>
                  ))}
                </Box>
              )}
              
              {/* Optional Parameters */}
              {selectedProcessInfo.optional_parameters && selectedProcessInfo.optional_parameters.length > 0 && (
                <Box>
                  <Typography variant="subtitle1" gutterBottom>
                    Optional Parameters
                  </Typography>
                  {selectedProcessInfo.optional_parameters.map((param: string) => (
                    <Box key={param}>
                      {renderParameterInput(param, false)}
                    </Box>
                  ))}
                </Box>
              )}
            </>
          )}
        </DialogContent>
        
        <DialogActions>
          <Button onClick={closeDialog}>Cancel</Button>
          <Button
            onClick={handleStartProcess}
            variant="contained"
            disabled={isStarting}
            startIcon={<PlayArrow />}
          >
            {isStarting ? 'Starting...' : 'Start Process'}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ProcessStarter;
