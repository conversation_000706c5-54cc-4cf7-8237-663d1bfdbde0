import asyncio
import j<PERSON>
from typing import Dict, List, Set
from fastapi import WebSocket, WebSocketDisconnect
import logging

logger = logging.getLogger(__name__)

class ConnectionManager:
    """Simple WebSocket connection manager for basic connectivity."""
    
    def __init__(self):
        self.active_connections: List[WebSocket] = []
    
    async def connect(self, websocket: WebSocket):
        """Accept a new WebSocket connection"""
        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info(f"WebSocket client connected. Total connections: {len(self.active_connections)}")
    
    def disconnect(self, websocket: WebSocket):
        """Remove a WebSocket connection"""
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        logger.info(f"WebSocket client disconnected. Total connections: {len(self.active_connections)}")
    
    async def send_personal_message(self, message: str, websocket: WebSocket):
        """Send a message to a specific client"""
        try:
            await websocket.send_text(message)
        except Exception as e:
            logger.error(f"Error sending message: {e}")
            self.disconnect(websocket)
    
    async def broadcast(self, message: str):
        """Broadcast a message to all connected clients"""
        disconnected = []
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except Exception as e:
                logger.error(f"Error broadcasting message: {e}")
                disconnected.append(connection)
        
        # Clean up disconnected clients
        for connection in disconnected:
            self.disconnect(connection)

class WebSocketManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.room_connections: Dict[str, Set[str]] = {}
    
    async def connect(self, websocket: WebSocket, client_id: str, room: str = "default"):
        """Accept a new WebSocket connection"""
        await websocket.accept()
        self.active_connections[client_id] = websocket
        
        # Add to room
        if room not in self.room_connections:
            self.room_connections[room] = set()
        self.room_connections[room].add(client_id)
        
        logger.info(f"Client {client_id} connected to room {room}")
    
    def disconnect(self, client_id: str):
        """Remove a WebSocket connection"""
        if client_id in self.active_connections:
            del self.active_connections[client_id]
        
        # Remove from all rooms
        for room, clients in self.room_connections.items():
            clients.discard(client_id)
        
        logger.info(f"Client {client_id} disconnected")
    
    async def send_personal_message(self, message: dict, client_id: str):
        """Send a message to a specific client"""
        if client_id in self.active_connections:
            websocket = self.active_connections[client_id]
            try:
                await websocket.send_text(json.dumps(message))
            except WebSocketDisconnect:
                logger.warning(f"Client {client_id} disconnected before message could be sent.")
                self.disconnect(client_id)
            except Exception as e:
                # Log the full exception details
                logger.error(f"Error sending message to {client_id}: {e!r}", exc_info=True)
                self.disconnect(client_id)
    
    async def broadcast_to_room(self, message: dict, room: str = "default"):
        """Broadcast a message to all clients in a room"""
        if room in self.room_connections:
            disconnected_clients = []
            
            for client_id in self.room_connections[room].copy():
                if client_id in self.active_connections:
                    try:
                        websocket = self.active_connections[client_id]
                        await websocket.send_text(json.dumps(message))
                    except WebSocketDisconnect:
                        logger.warning(f"Client {client_id} in room {room} disconnected before broadcast.")
                        disconnected_clients.append(client_id)
                    except Exception as e:
                        logger.error(f"Error broadcasting to {client_id} in room {room}: {e!r}", exc_info=True)
                        disconnected_clients.append(client_id)
                else:
                    disconnected_clients.append(client_id)
            
            # Clean up disconnected clients
            for client_id in disconnected_clients:
                self.disconnect(client_id)
    
    async def broadcast_process_update(self, task_id: str, process_data: dict):
        """Broadcast process status update to all connected clients"""
        message = {
            "type": "process_update",
            "task_id": task_id,
            "data": process_data,
            "timestamp": asyncio.get_event_loop().time()
        }
        await self.broadcast_to_room(message, "monitoring")
    
    async def broadcast_system_alert(self, alert_data: dict):
        """Broadcast system alert to all connected clients"""
        message = {
            "type": "system_alert",
            "data": alert_data,
            "timestamp": asyncio.get_event_loop().time()
        }
        await self.broadcast_to_room(message, "monitoring")
    
    async def broadcast_data_quality_update(self, quality_data: dict):
        """Broadcast data quality update to all connected clients"""
        message = {
            "type": "data_quality_update", 
            "data": quality_data,
            "timestamp": asyncio.get_event_loop().time()
        }
        await self.broadcast_to_room(message, "monitoring")
    
    async def broadcast(self, message: dict):
        """Broadcast a message to all connected clients"""
        await self.broadcast_to_room(message, "monitoring")

# Global managers
manager = WebSocketManager()
connection_manager = ConnectionManager()
