@echo off
REM Set default mode to prod, allow override by argument
SET MODE=prod
IF NOT "%1"=="" (
  SET MODE=%1
)
echo MODE is set to [%MODE%]

REM Load environment variables from .env file
if exist .env (
  for /f "delims== tokens=1,2" %%a in (.env) do (
    set "%%a=%%b"
    ECHO %%a=%%b
  )
)

REM Set default ports if not in environment
if not defined FRONTEND_PORT set FRONTEND_PORT=3080
if not defined BACKEND_PORT set BACKEND_PORT=8004

echo Starting application in %MODE% mode...

IF /I "%MODE%"=="dev" (
  echo Using docker-compose.dev.yml
  docker-compose -f docker-compose.dev.yml up -d --build
  IF ERRORLEVEL 1 (
    echo Failed to start development services.
  ) ELSE (
    echo Development services started successfully.
    echo Backend should be available at http://localhost:%BACKEND_PORT%
    echo Frontend should be available at http://localhost:%FRONTEND_PORT%
  )
) ELSE IF /I "%MODE%"=="prod" (
  echo Using docker-compose.yml
  docker-compose -f docker-compose.yml up -d --build
  IF ERRORLEVEL 1 (
    echo Failed to start production services.
  ) ELSE (
    echo Production services started successfully.
    echo Application should be available at:
    echo   - Main application: http://localhost
    echo   - API documentation: http://localhost/docs
    echo   - API health check: http://localhost/health
  )
) ELSE (
  echo Invalid mode specified. Use 'dev' or 'prod'.
  exit /b 1
)

echo.
echo To view logs, use:
echo   Development: docker-compose -f docker-compose.dev.yml logs -f
echo   Production:  docker-compose logs -f
echo.
echo To stop services, use:
echo   Development: docker-compose -f docker-compose.dev.yml down
echo   Production:  docker-compose down

echo %date:~-6,2%%date:~0,2%%date:~3,2%
