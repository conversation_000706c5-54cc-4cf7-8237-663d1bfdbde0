"""
Test Immediate Fallback Functionality

This script demonstrates the immediate fallback to Firecrawl when direct HTTP fails.
"""

import os
import sys
from pathlib import Path

# Add the scripts directory to Python path
script_dir = Path(__file__).parent
sys.path.append(str(script_dir))

# Set required environment variables for testing
os.environ['out_path'] = str(script_dir / 'output') + '/'
os.environ['LOG_LEVEL'] = '30'  # WARNING level
os.environ['SQL_ECHO'] = '0'
os.environ['WILL9700_DB'] = 'postgresql://dummy:dummy@localhost/dummy'  # Dummy DB for testing

# Import the enhanced functions
from UpdateStockOptionReportPostgres import safe_http_get_with_firecrawl_fallback

def test_immediate_fallback():
    """Test immediate fallback with very short timeout to force failure"""
    print("⚡ Testing Immediate Fallback to Firecrawl")
    print("=" * 60)
    
    # Use the URL that was originally failing
    test_url = "https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/dqe250616.htm"
    
    print(f"URL: {test_url}")
    print(f"Using very short timeout (3 seconds) to force direct HTTP failure...")
    print()
    
    try:
        # Use very short timeout to force direct HTTP to fail
        response = safe_http_get_with_firecrawl_fallback(test_url, timeout=3)
        
        if response and response.status_code == 200:
            print(f"✅ SUCCESS! Immediate fallback worked!")
            print(f"📏 Content length: {len(response.content)} bytes")
            
            # Check if it's a valid HKEX report
            content_str = response.text.lower() if hasattr(response, 'text') else str(response.content).lower()
            
            if any(keyword in content_str for keyword in ['stock option', 'hkex', 'hong kong exchange', 'derivatives']):
                print(f"✅ Content appears to be a valid HKEX stock option report")
                return True
            else:
                print(f"⚠️  Content may not be a valid HKEX report")
                return False
        else:
            print(f"❌ FAILED: Both methods failed")
            if response:
                print(f"Status code: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_normal_timeout():
    """Test with normal timeout to see if direct HTTP works"""
    print("\n🔄 Testing with Normal Timeout")
    print("=" * 60)
    
    test_url = "https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/dqe250616.htm"
    
    print(f"URL: {test_url}")
    print(f"Using normal timeout (30 seconds)...")
    print()
    
    try:
        response = safe_http_get_with_firecrawl_fallback(test_url, timeout=30)
        
        if response and response.status_code == 200:
            print(f"✅ SUCCESS! Normal timeout worked!")
            print(f"📏 Content length: {len(response.content)} bytes")
            return True
        else:
            print(f"❌ FAILED: Normal timeout failed")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def main():
    """Main test function"""
    print("⚡ Immediate Fallback Test Suite")
    print("=" * 60)
    
    # Test 1: Force fallback with short timeout
    test1_result = test_immediate_fallback()
    
    # Test 2: Normal timeout
    test2_result = test_normal_timeout()
    
    # Summary
    print("\n" + "=" * 60)
    print("🏁 RESULTS")
    print("=" * 60)
    
    tests = [
        ("Immediate Fallback (3s timeout)", test1_result),
        ("Normal Timeout (30s timeout)", test2_result)
    ]
    
    passed = sum(1 for _, result in tests if result)
    total = len(tests)
    
    for test_name, result in tests:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<35}: {status}")
    
    print(f"\n📊 Overall: {passed}/{total} tests passed")
    
    if passed >= 1:
        print("\n🎉 IMMEDIATE FALLBACK IS WORKING!")
        print("✅ The system now switches to Firecrawl immediately on timeout")
        print("✅ No more wasted time on retries")
        print("\n💡 Benefits:")
        print("   1. Faster response when direct HTTP fails")
        print("   2. No multiple retry attempts")
        print("   3. Immediate switch to reliable Firecrawl")
        print("   4. Better user experience with faster fallback")
        print("\n🚀 Your main script will now:")
        print("   1. Try direct HTTP once (fast when it works)")
        print("   2. Switch to Firecrawl immediately on failure")
        print("   3. Continue processing without delays")
    else:
        print("\n⚠️  ISSUES DETECTED")
        print("💡 Check your network connection and Firecrawl API key")

if __name__ == "__main__":
    main()
