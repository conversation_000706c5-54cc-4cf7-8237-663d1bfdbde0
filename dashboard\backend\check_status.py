#!/usr/bin/env python3
import sys
sys.path.append('app')
from app.services.simple_orchestrator import orchestrator

print("Checking orchestrator status...")
active_processes = orchestrator.list_active_processes()
print(f"Active processes: {len(active_processes)}")

for process in active_processes:
    print(f"Process {process['task_id']}: {process['status']} - {process['message']}")
    if process.get('error'):
        print(f"  Error: {process['error']}")
