#!/usr/bin/env python3
"""
Complete test to verify the backend server and all fixed endpoints work correctly.
This will test the actual API endpoints that the React frontend calls.
"""
import sys
import os
import time
import requests
import threading
import subprocess
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

def test_with_real_server():
    """Test the endpoints with a real server instance"""
    base_url = "http://127.0.0.1:8000"
    
    try:
        # Test 1: Health endpoint
        print("🔍 Testing health endpoint...")
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print(f"✅ Health check: {response.json()}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
            
        # Test 2: API v1 root
        print("\n🔍 Testing API v1 root...")
        response = requests.get(f"{base_url}/api/v1/", timeout=5)
        if response.status_code == 200:
            print(f"✅ API v1 root: {response.json()}")
        else:
            print(f"❌ API v1 root failed: {response.status_code}")
            
        # Test 3: Get active processes
        print("\n🔍 Testing active processes endpoint...")
        response = requests.get(f"{base_url}/api/v1/processes/active", timeout=5)
        if response.status_code == 200:
            processes = response.json()
            print(f"✅ Active processes: {len(processes)} processes")
        else:
            print(f"❌ Active processes failed: {response.status_code}")
            
        # Test 4: Get process types
        print("\n🔍 Testing process types endpoint...")
        response = requests.get(f"{base_url}/api/v1/processes/types", timeout=5)
        if response.status_code == 200:
            types = response.json()
            print(f"✅ Process types: {len(types)} types available")
        else:
            print(f"❌ Process types failed: {response.status_code}")
            
        # Test 5: Log-tail with non-existent task (should handle gracefully)
        print("\n🔍 Testing log-tail endpoint (non-existent task)...")
        response = requests.get(f"{base_url}/api/v1/processes/test-task-123/log-tail", timeout=5)
        if response.status_code == 200:
            log_data = response.json()
            print(f"✅ Log-tail endpoint working: {log_data}")
        else:
            print(f"⚠️  Log-tail response: {response.status_code} (may be expected)")
            try:
                print(f"Response: {response.json()}")
            except:
                print(f"Response text: {response.text}")
                
        # Test 6: Start a real task and test its logs
        print("\n🔍 Testing task start and log retrieval...")
        task_data = {
            "script_type": "test",
            "script_path": "echo 'Hello from test task'",
            "description": "Test task for log verification"
        }
        response = requests.post(f"{base_url}/api/v1/processes/start", json=task_data, timeout=10)
        if response.status_code == 200:
            result = response.json()
            task_id = result.get('task_id')
            print(f"✅ Task started: {task_id}")
            
            # Wait for task to execute
            time.sleep(3)
            
            # Test log-tail with real task
            if task_id:
                print(f"\n🔍 Testing log-tail with real task: {task_id}")
                response = requests.get(f"{base_url}/api/v1/processes/{task_id}/log-tail", timeout=5)
                if response.status_code == 200:
                    log_data = response.json()
                    print(f"✅ Real task log-tail working")
                    print(f"Log data keys: {list(log_data.keys())}")
                    if 'log_lines' in log_data:
                        print(f"Log lines count: {len(log_data['log_lines'])}")
                else:
                    print(f"❌ Real task log-tail failed: {response.status_code}")
                    
                # Test full logs
                print(f"\n🔍 Testing full logs for task: {task_id}")
                response = requests.get(f"{base_url}/api/v1/processes/{task_id}/log-full", timeout=5)
                if response.status_code == 200:
                    log_data = response.json()
                    print(f"✅ Full logs working")
                    print(f"Full log data keys: {list(log_data.keys())}")
                else:
                    print(f"❌ Full logs failed: {response.status_code}")
        else:
            print(f"❌ Task start failed: {response.status_code}")
            try:
                print(f"Error response: {response.json()}")
            except:
                print(f"Error text: {response.text}")
                
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to server - server may not be running")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def check_orchestrator_methods():
    """Test the orchestrator methods directly"""
    try:
        print("\n🔍 Testing orchestrator methods directly...")
        from app.services.simple_orchestrator import orchestrator
        
        # Test the methods that were causing 500 errors
        result = orchestrator.get_log_tail("test_task", 10)
        print(f"✅ get_log_tail: {result}")
        
        result = orchestrator.get_process_logs("test_task") 
        print(f"✅ get_process_logs: {result}")
        
        result = orchestrator.get_active_processes()
        print(f"✅ get_active_processes: {len(result)} processes")
        
        result = orchestrator.get_process_history()
        print(f"✅ get_process_history: {len(result)} completed processes")
        
        result = orchestrator.get_full_log_content("test_task")
        print(f"✅ get_full_log_content: {result}")
        
        print("✅ All orchestrator methods working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Orchestrator test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Complete Backend Server Test")
    print("=" * 50)
    
    # Test orchestrator methods first
    orchestrator_ok = check_orchestrator_methods()
    
    if orchestrator_ok:
        print("\n" + "=" * 50)
        print("🚀 Testing with real server...")
        print("Make sure the server is running: python -m uvicorn app.main:app --reload")
        print("=" * 50)
        
        server_ok = test_with_real_server()
        
        print("\n" + "=" * 50)
        if server_ok:
            print("✅ ALL TESTS PASSED! Backend is working correctly.")
            print("🎉 The 500 Internal Server Error should be fixed!")
        else:
            print("⚠️  Server tests had issues. Check if server is running.")
    else:
        print("❌ Orchestrator methods failed - need to fix these first.")
        
    print("=" * 50)
