# Database Persistence Fix for HKEX Dashboard

## Problem Analysis

The user reported that completed processes were not being saved to PostgreSQL and not showing up in the history page. Analysis revealed two main issues:

### Issue 1: No Database Persistence
- The `simple_orchestrator.py` had database imports but never used them
- Processes were only stored in memory (`active_processes` dictionary)
- The `get_process_history()` method only returned in-memory data
- The existing `ProcessHistoryService` was never called by the orchestrator

### Issue 2: Race Condition in Log Tailing
- The `_tail_log_files()` method was overriding completed status back to "running"
- After a process completed, log tailing continued to send status updates
- This caused the WebSocket to show "running" status even after completion

## Solution Implemented

### 1. Fixed Race Condition in Log Tailing

**File:** `dashboard/backend/app/services/simple_orchestrator.py`

**Changes:**
- Modified `_tail_log_files()` to check for terminal states before updating status
- Added `_send_log_message()` method for log-only updates that preserve terminal status
- Prevents overriding "completed" status with "running" status

**Code Changes:**
```python
# Check if process is in terminal state - if so, don't override status
current_status = process_info.get('status', '').lower() if process_info else ''
is_terminal_state = current_status in ['completed', 'failed', 'cancelled', 'error']

# Don't override terminal status - just send log message without changing status
if is_terminal_state:
    await self._send_log_message(task_id, f'[STDOUT] {line_strip}')
else:
    await self._update_process_status(task_id, 'running', f'[STDOUT] {line_strip}', process_info.get('progress', 25))
```

### 2. Added Database Persistence

**File:** `dashboard/backend/app/services/simple_orchestrator.py`

**Changes:**
- Added import for `ProcessHistoryService`
- Modified `start_process()` to create database records
- Modified `_update_process_status()` to update database for terminal states
- Added `_update_database_record()` method to handle database updates
- Updated `get_process_history()` to query database instead of memory

**Key Methods Added/Modified:**

#### A. Process Creation (start_process)
```python
# Create database record for persistence
try:
    with SessionLocal() as db:
        script_path = str(self.scripts_dir / config['script'])
        ProcessHistoryService.create_process_execution(
            task_id=task_id,
            process_name=process_type,
            script_path=script_path,
            parameters=parameters,
            db=db
        )
        logger.info(f"Created database record for process {task_id}")
except Exception as e:
    logger.error(f"Failed to create database record for process {task_id}: {e}")
    # Continue anyway - don't fail the process start due to DB issues
```

#### B. Process Completion (_update_process_status)
```python
# Set end_time if the process is entering a terminal state and end_time is not already set
if normalized_status in ['completed', 'failed', 'cancelled', 'error'] and process_info.get('end_time') is None:
    process_info['end_time'] = now_utc
    logger.info(f"Set end_time for process {task_id} with status {normalized_status}: {now_utc.isoformat()}")
    
    # Update database record for terminal states
    await self._update_database_record(task_id, normalized_status, message, now_utc)
```

#### C. Database Update (_update_database_record)
```python
async def _update_database_record(self, task_id: str, status: str, message: str, end_time: datetime):
    """Update the database record for a process in terminal state."""
    try:
        with SessionLocal() as db:
            # Read log files to get stdout/stderr content
            # ... (reads log files)
            
            # Determine return code based on status
            return_code = 0 if status == 'completed' else 1
            
            ProcessHistoryService.update_process_status(
                task_id=task_id,
                status=status,
                stdout=stdout_content,
                stderr=stderr_content,
                return_code=return_code,
                end_time=end_time,
                db=db
            )
            logger.info(f"Updated database record for process {task_id} with status {status}")
    except Exception as e:
        logger.error(f"Failed to update database record for process {task_id}: {e}")
        # Don't fail the process update due to DB issues
```

#### D. History Retrieval (get_process_history)
```python
def get_process_history(self) -> List[Dict[str, Any]]:
    """Get list of all completed processes from database history"""
    try:
        with SessionLocal() as db:
            # Get process history from database
            db_processes = ProcessHistoryService.get_process_history(db, limit=100)
            
            history = []
            for process in db_processes:
                # Convert database model to dictionary format expected by frontend
                process_dict = {
                    'task_id': process.task_id,
                    'process_type': process.process_name,
                    'status': process.status,
                    'message': f"Process {process.status}",
                    'progress': 100 if process.status == 'completed' else 0,
                    'parameters': process.parameters or {},
                    'started_at': process.start_time.isoformat() if process.start_time else None,
                    'completed_at': process.end_time.isoformat() if process.end_time else None,
                    'duration_seconds': process.duration_seconds,
                    'return_code': process.return_code,
                    'error': process.stderr if process.status == 'failed' and process.stderr else None
                }
                history.append(process_dict)
            
            logger.info(f"Retrieved {len(history)} processes from database history")
            return history
            
    except Exception as e:
        logger.error(f"Failed to get process history from database: {e}")
        # Fallback to in-memory history if database fails
        return self._get_in_memory_history()
```

## Expected Results

After these changes:

1. **Processes will be saved to PostgreSQL** when they start and complete
2. **Completed processes will maintain their "completed" status** without being overridden by log tailing
3. **The history page will show completed processes** from the database
4. **Process logs will be saved** to the database for future reference
5. **Robust error handling** ensures the system continues working even if database operations fail

## Testing

To test the fix:

1. Start a hello_world process via the dashboard
2. Wait for it to complete
3. Verify the status stays "completed" in the WebSocket updates
4. Check the history page to see the completed process
5. Verify the process record exists in the PostgreSQL `process_executions` table

## Files Modified

- `dashboard/backend/app/services/simple_orchestrator.py` - Main orchestrator with database persistence
- `test_database_persistence.py` - Test script to verify the implementation
- `DATABASE_PERSISTENCE_FIX.md` - This documentation file

## Dependencies

The fix uses existing components:
- `ProcessHistoryService` (already existed)
- `ProcessExecution` model (already existed)
- `SessionLocal` database session (already existed)

No new dependencies were added.
