#!/usr/bin/env python3
"""Simple websocket test for Docker container"""

try:
    import websockets
    print(f"SUCCESS: websockets {websockets.__version__} imported")
    
    import websockets.legacy.handshake
    print("SUCCESS: websockets.legacy.handshake imported")
    
    import websocket
    from websocket import WebSocketApp
    print("SUCCESS: websocket-client imported")
    
    print("ALL WEBSOCKET TESTS PASSED!")
    
except Exception as e:
    print(f"ERROR: {e}")
    import traceback
    traceback.print_exc()
