import axios from 'axios';
import {
  SystemHealth,
  TableMetricsResponse,
  DataQualityResponse,
  ErrorLogResponse,
  ProcessStatus,
  ProcessStartRequest
} from '../types';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

class ApiService {
  private baseUrl = `${API_BASE_URL}/api/v1`;
  // Monitoring endpoints
  async getSystemStatus(): Promise<SystemHealth> {
    const response = await axios.get(`${this.baseUrl}/monitoring/status`);
    return response.data;
  }

  async getSystemHealth(): Promise<SystemHealth> {
    const response = await axios.get(`${this.baseUrl}/monitoring/status`);
    return response.data;
  }

  async getTableMetrics(): Promise<TableMetricsResponse> {
    const response = await axios.get(`${this.baseUrl}/monitoring/tables/metrics`);
    return response.data;
  }

  // Process management endpoints
  async startProcess(processData: ProcessStartRequest): Promise<{ task_id: string; message: string }> {
    const response = await axios.post(`${this.baseUrl}/processes/start`, processData);
    return response.data;
  }

  async getProcessStatus(taskId: string): Promise<ProcessStatus> {
    const response = await axios.get(`${this.baseUrl}/processes/${taskId}/status`);
    return response.data;
  }

  async cancelProcess(taskId: string): Promise<{ message: string }> {
    const response = await axios.post(`${this.baseUrl}/processes/${taskId}/cancel`);
    return response.data;
  }

  async getActiveProcesses(): Promise<any[]> {
    const response = await axios.get(`${this.baseUrl}/processes/active`);
    return response.data;
  }

  async getProcessTypes(): Promise<any> {
    const response = await axios.get(`${this.baseUrl}/processes/types`);
    return response.data;
  }

  // Data quality endpoints
  async getDataQualityChecks(txnDate?: string): Promise<DataQualityResponse> {
    const params = txnDate ? { txn_date: txnDate } : {};
    const response = await axios.get(`${this.baseUrl}/data-quality/checks`, { params });
    return response.data;
  }

  async getErrorLogs(filters: {
    severity?: string;
    process?: string;
    start_date?: string;
    end_date?: string;
    page?: number;
    page_size?: number;
  } = {}): Promise<ErrorLogResponse> {
    const response = await axios.get(`${this.baseUrl}/data-quality/errors`, { params: filters });
    return response.data;
  }

  async reprocessData(process: string, date: string): Promise<{ message: string; task_id: string }> {
    const response = await axios.post(`${this.baseUrl}/data-quality/reprocess`, {
      process,
      txn_date: date
    });
    return response.data;
  }

  // Health check
  async healthCheck(): Promise<{ status: string }> {
    const response = await axios.get(`${this.baseUrl}/monitoring/health`);
    return response.data;
  }
}

export const apiService = new ApiService();
