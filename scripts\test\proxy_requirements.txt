# Additional requirements for proxy crawler alternatives
# Install with: pip install -r proxy_requirements.txt

# Browser automation
selenium>=4.15.0
webdriver-manager>=4.0.0

# Enhanced HTTP handling
requests[socks]>=2.31.0
urllib3>=2.0.0

# Proxy and VPN support
PySocks>=1.7.1
requests-tor>=1.0.0

# Optional: For advanced proxy rotation
rotating-proxies>=0.6.2
proxy-randomizer>=1.0.0

# Cloud scraping services (API clients)
# Note: You'll need to sign up for these services and get API keys
# scrapingbee>=1.0.0  # Uncomment if using ScrapingBee
# scraperapi>=1.0.0   # Uncomment if using ScraperAPI

# Additional utilities
fake-useragent>=1.4.0
undetected-chromedriver>=3.5.0  # More stealth browser automation
