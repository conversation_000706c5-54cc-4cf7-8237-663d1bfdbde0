/**
 * Centralized duration calculation utility
 * Ensures consistent duration handling across all components
 */

export interface DurationCalculationOptions {
  startTime?: string;
  endTime?: string; 
  status?: string;
  durationSeconds?: number;
}

/**
 * Calculate duration between start and end times with status-aware logic
 * 
 * @param options - Configuration object
 * @returns Formatted duration string
 */
export const calculateDuration = (options: DurationCalculationOptions): string => {
  const { startTime, endTime, status } = options;
  
  if (!startTime) return 'N/A';
  
  try {
    const start = new Date(startTime);
    if (isNaN(start.getTime())) {
      console.warn(`Invalid start time: ${startTime}`);
      return 'Invalid start';
    }
    
    // For completed/failed/error processes, MUST use endTime to prevent ongoing calculation
    // For running processes, use current time to show live duration
    let end: Date;
    const isCompleted = status === 'completed' || status === 'failed' || status === 'error';
    
    if (isCompleted) {
      if (endTime) {
        end = new Date(endTime);
        if (isNaN(end.getTime())) {
          console.warn(`Invalid end time ${endTime} for completed process, using start time + 1s`);
          end = new Date(start.getTime() + 1000); // Use start + 1 second as fallback
        }
      } else if (options.durationSeconds !== undefined) {
        // Use duration_seconds from database if available
        console.warn(`Using duration_seconds (${options.durationSeconds}s) as fallback for missing end_time.`);
        return formatDuration(options.durationSeconds * 1000);
      } else {
        // Completed process without end_time - this should not happen, but handle it
        console.warn(`Completed process ${status} without end_time. Using fallback: start time + 1s. Consider ensuring end_time is provided for completed processes.`);
        end = new Date(start.getTime() + 1000);
      }
    } else {
      // Running or starting process - use current time for live updates
      end = new Date();
    }

    let durationMs = end.getTime() - start.getTime();
    
    // Debug logging for problematic durations
    if (durationMs > 10 * 60 * 60 * 1000) { // More than 10 hours
      console.warn(`Unusually long duration detected:`, {
        startTime,
        endTime,
        status,
        startParsed: start.toISOString(),
        endParsed: end.toISOString(),
        durationMs,
        durationHours: Math.round(durationMs / (60 * 60 * 1000) * 10) / 10
      });
    }
    
    // Handle negative durations (shouldn't happen but safeguard)
    if (durationMs < 0) {
      console.warn(`Negative duration detected: ${durationMs}ms, setting to 0`);
      durationMs = 0;
    }
    
    return formatDuration(durationMs);
  } catch (error) {
    console.error('Error calculating duration:', error);
    return 'Error';
  }
};

/**
 * Format duration milliseconds into human-readable string
 * 
 * @param durationMs - Duration in milliseconds
 * @returns Formatted string (e.g., "1h 23m 45s", "23m 45s", "45s")
 */
export const formatDuration = (durationMs: number): string => {
  const totalSeconds = Math.floor(durationMs / 1000);
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;
  
  if (hours > 0) {
    return `${hours}h ${minutes}m ${seconds}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds}s`;
  } else {
    return `${seconds}s`;
  }
};

/**
 * Check if a task_id is valid (not undefined, null, or placeholder)
 * 
 * @param taskId - Task ID to validate
 * @returns true if valid, false otherwise
 */
export const isValidTaskId = (taskId?: string): boolean => {
  return !!(taskId && taskId !== 'undefined' && taskId !== '(...)' && taskId.trim() !== '');
};

/**
 * Filter out processes with invalid task IDs
 * 
 * @param processes - Array of processes
 * @returns Filtered array with only valid processes
 */
export const filterValidProcesses = <T extends { task_id?: string }>(processes: T[]): T[] => {
  return processes.filter(process => process && isValidTaskId(process.task_id));
};
