import requests
import json
import time

def test_orchestrator_api():
    """Test starting the HKEX script through the dashboard API"""
    
    base_url = "http://localhost:8000"
    
    print("=== Testing HKEX Dashboard Orchestrator ===")
    
    # Test 1: Check if we can start the HKEX script
    print("\n1. Testing script execution through API...")
    
    script_data = {
        "script_path": "o:\\Github\\MaxPain\\MaxPain2024\\UpdateIndexOptionPostgres.py",
        "args": ["--help"],
        "working_directory": "o:\\Github\\MaxPain\\MaxPain2024"
    }
    
    try:
        response = requests.post(f"{base_url}/api/start-process", json=script_data, timeout=10)
        print(f"   Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            process_id = result.get("process_id")
            print(f"   Process ID: {process_id}")
            print("   [PASS] Script started successfully via API")
            
            # Wait a moment for execution
            time.sleep(3)
            
            # Check process status
            print("\n2. Checking process status...")
            status_response = requests.get(f"{base_url}/api/processes", timeout=5)
            if status_response.status_code == 200:
                processes = status_response.json()
                print(f"   Active processes: {len(processes)}")
                print("   [PASS] Process status retrieved")
                
                # Show process details
                for proc in processes:
                    if proc.get("id") == process_id:
                        print(f"   Process {process_id}: {proc.get('status', 'unknown')}")
            
            # Get logs
            print("\n3. Checking process logs...")
            logs_response = requests.get(f"{base_url}/api/process-logs/{process_id}", timeout=5)
            if logs_response.status_code == 200:
                logs = logs_response.json()
                print(f"   Log entries: {len(logs)}")
                print("   [PASS] Process logs retrieved")
                
                # Show recent logs
                for log in logs[-3:]:  # Show last 3 log entries
                    print(f"   Log: {log.get('message', '')[:100]}...")
            
        else:
            print(f"   [FAIL] Script start failed: {response.text}")
            
    except Exception as e:
        print(f"   [FAIL] API test failed: {str(e)}")
    
    # Test 2: Test the wrapper script
    print("\n4. Testing wrapper script...")
    
    wrapper_data = {
        "script_path": "o:\\Github\\MaxPain\\MaxPain2024\\UpdateIndexOptionPostgres_wrapper.py",
        "args": [],
        "working_directory": "o:\\Github\\MaxPain\\MaxPain2024"
    }
    
    try:
        response = requests.post(f"{base_url}/api/start-process", json=wrapper_data, timeout=10)
        print(f"   Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            process_id = result.get("process_id")
            print(f"   Wrapper Process ID: {process_id}")
            print("   [PASS] Wrapper script started successfully")
        else:
            print(f"   [FAIL] Wrapper script failed: {response.text}")
            
    except Exception as e:
        print(f"   [FAIL] Wrapper test failed: {str(e)}")

if __name__ == "__main__":
    test_orchestrator_api()