#!/usr/bin/env python3
"""
Test the orchestrator with better error handling
"""

import asyncio
import os
import sys

# Set Windows event loop policy
if os.name == 'nt':
    asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

async def test_orchestrator():
    # Add the backend app to the path
    backend_path = r"O:\Github\MaxPain\MaxPain2024\dashboard\backend\app"
    sys.path.insert(0, backend_path)
    
    from app.services.simple_orchestrator import SimpleOrchestrator
    
    orchestrator = SimpleOrchestrator()
    
    print("=== Testing UpdateIndexOptionPostgres with better error handling ===")
    
    # Test with dry run to minimize impact
    parameters = {
        'txn_date': '2024-12-20',
        'dry_run': True
    }
    
    task_id = await orchestrator.start_process('update_index_options', parameters)
    print(f"Started task: {task_id}")
    
    # Monitor for 60 seconds
    for i in range(60):
        await asyncio.sleep(1)
        
        # Get process info
        processes = orchestrator.list_active_processes()
        if task_id in processes:
            process_info = processes[task_id]
            status = process_info['status']
            message = process_info['message']
            progress = process_info['progress']
            
            print(f"[{i:2d}s] Status: {status}, Progress: {progress}%, Message: {message}")
            
            if status in ['completed', 'failed']:
                print(f"\nFinal status: {status}")
                if 'error' in process_info['info']:
                    print(f"Error details:\n{process_info['info']['error']}")
                if 'output' in process_info['info']:
                    print(f"Last output lines:")
                    for line in process_info['info']['output'][-10:]:
                        print(f"  {line}")
                break
        else:
            print(f"[{i:2d}s] Task not found in active processes")
            break
    
    print("Test completed")

if __name__ == "__main__":
    asyncio.run(test_orchestrator())
