#!/usr/bin/env python3
"""
Comprehensive test to verify the HKEX Dashboard fixes.
This test verifies that the orchestrator can handle the update_index_options process
without requiring the txn_date parameter.
"""

import sys
import os
from pathlib import Path
import asyncio
import json

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

def test_configuration():
    """Test that the orchestrator configuration is correct."""
    print("=== Testing Orchestrator Configuration ===")
    
    try:
        from app.services.simple_orchestrator import orchestrator
        
        # Test that update_index_options has the correct configuration
        process_types = orchestrator.get_process_types()
        update_config = process_types.get('update_index_options')
        
        if not update_config:
            print("✗ update_index_options configuration not found")
            return False
        
        print(f"Configuration: {json.dumps(update_config, indent=2)}")
        
        # Check that txn_date is not in requires_params
        requires_params = update_config.get('requires_params', [])
        if 'txn_date' in requires_params:
            print("✗ txn_date is still in requires_params - this should be fixed!")
            return False
        else:
            print("✓ txn_date is not in requires_params")
        
        # Check that txn_date is in optional_params
        optional_params = update_config.get('optional_params', [])
        if 'txn_date' not in optional_params:
            print("✗ txn_date is not in optional_params")
            return False
        else:
            print("✓ txn_date is in optional_params")
        
        print("✓ Configuration test passed!")
        return True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_start_process():
    """Test that we can start the process without txn_date parameter."""
    print("\n=== Testing Process Start (Simulation) ===")
    
    try:
        from app.services.simple_orchestrator import orchestrator
        
        # Test parameters validation
        process_type = 'update_index_options'
        parameters = {}  # Empty parameters - this was causing the 400 error
        
        print(f"Testing process start with process_type: {process_type}")
        print(f"Testing with parameters: {parameters}")
        
        # Check if process validation passes
        config = orchestrator.process_configs.get(process_type)
        if not config:
            print(f"✗ Process type {process_type} not found")
            return False
        
        # Validate required parameters
        required_params = config.get('requires_params', [])
        missing_params = [param for param in required_params if param not in parameters]
        
        if missing_params:
            print(f"✗ Missing required parameters: {missing_params}")
            return False
        else:
            print("✓ All required parameters satisfied (none required)")
        
        print("✓ Process validation test passed!")
        return True
        
    except Exception as e:
        print(f"✗ Process start test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_windows_event_loop():
    """Test Windows event loop setup."""
    print("\n=== Testing Windows Event Loop Setup ===")
    
    try:
        import asyncio
        
        # Check current event loop
        try:
            loop = asyncio.get_event_loop()
            print(f"Current event loop type: {type(loop)}")
            
            if sys.platform == 'win32':
                if isinstance(loop, asyncio.ProactorEventLoop):
                    print("✓ Windows is using ProactorEventLoop")
                else:
                    print(f"⚠ Windows is using {type(loop)} instead of ProactorEventLoop")
            else:
                print("✓ Not Windows - event loop type is system appropriate")
                
        except RuntimeError:
            print("✓ No event loop running yet - this is normal")
        
        print("✓ Event loop test passed!")
        return True
        
    except Exception as e:
        print(f"✗ Event loop test failed: {e}")
        return False

def test_log_methods():
    """Test that all required log methods exist."""
    print("\n=== Testing Log Methods ===")
    
    try:
        from app.services.simple_orchestrator import orchestrator
        
        # Check that all log methods exist
        required_methods = [
            'get_log_tail',
            'get_full_log_content', 
            'get_process_logs',
            'get_process_history',
            'get_active_processes'
        ]
        
        for method_name in required_methods:
            if hasattr(orchestrator, method_name):
                print(f"✓ {method_name} method exists")
            else:
                print(f"✗ {method_name} method missing")
                return False
        
        print("✓ All log methods test passed!")
        return True
        
    except Exception as e:
        print(f"✗ Log methods test failed: {e}")
        return False

async def run_all_tests():
    """Run all tests."""
    print("HKEX Dashboard Fix Verification Test")
    print("="*50)
    
    tests = [
        ("Configuration", test_configuration),
        ("Process Start", test_start_process),
        ("Windows Event Loop", test_windows_event_loop),
        ("Log Methods", test_log_methods)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
                
        except Exception as e:
            print(f"✗ {test_name} test crashed: {e}")
    
    print(f"\n=== SUMMARY ===")
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! The fix appears to be working correctly.")
        print("\nThe 400 Bad Request error should now be resolved.")
        print("You can start the server with: python run_server.py")
    else:
        print("❌ Some tests failed. Please check the output above.")
    
    return passed == total

if __name__ == "__main__":
    # Run the tests
    try:
        result = asyncio.run(run_all_tests())
        sys.exit(0 if result else 1)
    except Exception as e:
        print(f"Test suite crashed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
