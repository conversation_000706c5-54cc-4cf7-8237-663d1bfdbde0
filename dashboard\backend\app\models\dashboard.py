"""
Dashboard-specific database models
"""
from sqlalchemy import Column, Integer, String, DateTime, JSON, Boolean, Text, Float
from sqlalchemy.sql import func
from datetime import datetime
from core.database import Base

class ProcessExecution(Base):
    """Track process execution history"""
    __tablename__ = "process_executions"
    
    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(String(255), unique=True, index=True)
    process_name = Column(String(100), nullable=False)
    script_path = Column(String(500), nullable=False)
    status = Column(String(50), default="pending")  # pending, running, completed, failed
    start_time = Column(DateTime(timezone=True), default=func.now())
    end_time = Column(DateTime(timezone=True), nullable=True)
    duration_seconds = Column(Float, nullable=True)
    return_code = Column(Integer, nullable=True)
    stdout = Column(Text, nullable=True)
    stderr = Column(Text, nullable=True)
    parameters = Column(JSON, nullable=True)
    created_at = Column(DateTime(timezone=True), default=func.now())
    updated_at = Column(DateTime(timezone=True), default=func.now(), onupdate=func.now())

class SystemHealth(Base):
    """Track system health metrics"""
    __tablename__ = "system_health"
    
    id = Column(Integer, primary_key=True, index=True)
    timestamp = Column(DateTime(timezone=True), default=func.now())
    cpu_usage = Column(Float, nullable=True)
    memory_usage = Column(Float, nullable=True)
    disk_usage = Column(Float, nullable=True)
    database_status = Column(String(50), default="unknown")
    redis_status = Column(String(50), default="unknown")
    active_connections = Column(Integer, default=0)
    queue_size = Column(Integer, default=0)
    metrics_data = Column(JSON, nullable=True)

class TableMetrics(Base):
    """Track table row counts and metadata"""
    __tablename__ = "table_metrics"
    
    id = Column(Integer, primary_key=True, index=True)
    table_name = Column(String(100), nullable=False)
    row_count = Column(Integer, default=0)
    last_updated = Column(DateTime(timezone=True), nullable=True)
    table_size_bytes = Column(Integer, nullable=True)
    index_size_bytes = Column(Integer, nullable=True)
    vacuum_stats = Column(JSON, nullable=True)
    timestamp = Column(DateTime(timezone=True), default=func.now())

class DataQualityCheck(Base):
    """Track data quality check results"""
    __tablename__ = "data_quality_checks"
    
    id = Column(Integer, primary_key=True, index=True)
    check_id = Column(String(255), unique=True, index=True)
    check_name = Column(String(200), nullable=False)
    table_name = Column(String(100), nullable=False)
    check_type = Column(String(50), nullable=False)  # count, duplicate, null, range, etc.
    status = Column(String(50), default="pending")  # passed, failed, warning, error
    expected_value = Column(String(500), nullable=True)
    actual_value = Column(String(500), nullable=True)
    error_message = Column(Text, nullable=True)
    check_config = Column(JSON, nullable=True)
    execution_time_ms = Column(Float, nullable=True)
    created_at = Column(DateTime(timezone=True), default=func.now())
    updated_at = Column(DateTime(timezone=True), default=func.now(), onupdate=func.now())

class SystemAlert(Base):
    """Track system alerts and notifications"""
    __tablename__ = "system_alerts"
    
    id = Column(Integer, primary_key=True, index=True)
    alert_id = Column(String(255), unique=True, index=True)
    alert_type = Column(String(50), nullable=False)  # error, warning, info
    title = Column(String(200), nullable=False)
    message = Column(Text, nullable=False)
    source = Column(String(100), nullable=True)  # process, system, data_quality
    severity = Column(String(20), default="medium")  # low, medium, high, critical
    acknowledged = Column(Boolean, default=False)
    acknowledged_by = Column(String(100), nullable=True)
    acknowledged_at = Column(DateTime(timezone=True), nullable=True)
    resolved = Column(Boolean, default=False)
    resolved_at = Column(DateTime(timezone=True), nullable=True)
    alert_metadata = Column(JSON, nullable=True)
    created_at = Column(DateTime(timezone=True), default=func.now())
    updated_at = Column(DateTime(timezone=True), default=func.now(), onupdate=func.now())

class ProcessSchedule(Base):
    """Track scheduled process configurations"""
    __tablename__ = "process_schedules"
    
    id = Column(Integer, primary_key=True, index=True)
    schedule_id = Column(String(255), unique=True, index=True)
    process_name = Column(String(100), nullable=False)
    script_path = Column(String(500), nullable=False)
    cron_expression = Column(String(100), nullable=True)
    enabled = Column(Boolean, default=True)
    last_run = Column(DateTime(timezone=True), nullable=True)
    next_run = Column(DateTime(timezone=True), nullable=True)
    run_count = Column(Integer, default=0)
    success_count = Column(Integer, default=0)
    failure_count = Column(Integer, default=0)
    parameters = Column(JSON, nullable=True)
    timeout_seconds = Column(Integer, default=3600)
    created_at = Column(DateTime(timezone=True), default=func.now())
    updated_at = Column(DateTime(timezone=True), default=func.now(), onupdate=func.now())
