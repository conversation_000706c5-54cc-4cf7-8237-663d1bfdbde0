#!/usr/bin/env python3
"""
Test script to verify Windows asyncio fix is working for HKEX Dashboard
This script tests process execution to ensure the NotImplementedError is resolved
"""

import requests
import json
import time
import sys
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

def test_health_check():
    """Test basic health check"""
    print("1. Testing health check...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            print("✅ Health check passed")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def test_process_types():
    """Test process types endpoint"""
    print("\n2. Testing process types...")
    try:
        response = requests.get(f"{API_BASE}/processes/types")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Process types retrieved: {len(data)} types")
            print(f"   Available types: {list(data.keys())}")
            return True, data
        else:
            print(f"❌ Process types failed: {response.status_code}")
            return False, None
    except Exception as e:
        print(f"❌ Process types error: {e}")
        return False, None

def test_process_execution():
    """Test actual process execution to verify Windows asyncio fix"""
    print("\n3. Testing process execution (Windows asyncio fix verification)...")
    
    # Test with dry_run to avoid actual database changes
    process_data = {
        "process": "update_index_options",
        "parameters": {
            "dry_run": True,
            "txn_date": "2025-05-24"
        }
    }
    
    try:
        # Start the process
        print(f"   Starting process: {process_data['process']}")
        response = requests.post(
            f"{API_BASE}/processes/start",
            json=process_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code != 200:
            print(f"❌ Process start failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
        
        result = response.json()
        task_id = result.get("task_id")
        print(f"✅ Process started successfully, task_id: {task_id}")
        
        # Monitor the process status
        print("   Monitoring process status...")
        max_wait = 60  # 60 seconds max wait
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            try:
                status_response = requests.get(f"{API_BASE}/processes/{task_id}/status")
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    status = status_data.get("status", "unknown")
                    print(f"   Status: {status}")
                    
                    if status == "completed":
                        print("✅ Process completed successfully!")
                        print(f"   Final result: {status_data}")
                        return True
                    elif status == "failed":
                        print("❌ Process failed!")
                        print(f"   Error details: {status_data}")
                        return False
                    elif status in ["running", "pending"]:
                        print(f"   Process {status}, waiting...")
                        time.sleep(5)
                    else:
                        print(f"   Unknown status: {status}")
                        time.sleep(2)
                else:
                    print(f"   Status check failed: {status_response.status_code}")
                    time.sleep(2)
            except Exception as e:
                print(f"   Status check error: {e}")
                time.sleep(2)
        
        print("⚠️  Process monitoring timed out")
        return False
        
    except Exception as e:
        print(f"❌ Process execution error: {e}")
        return False

def test_api_endpoints():
    """Test various API endpoints"""
    print("\n4. Testing additional API endpoints...")
    
    endpoints = [
        ("/processes/types", "GET"),
        ("/health", "GET")
    ]
    
    success_count = 0
    for endpoint, method in endpoints:
        try:
            if method == "GET":
                response = requests.get(f"{API_BASE}{endpoint}")
            else:
                continue
                
            if response.status_code == 200:
                print(f"✅ {method} {endpoint} - OK")
                success_count += 1
            else:
                print(f"❌ {method} {endpoint} - {response.status_code}")
        except Exception as e:
            print(f"❌ {method} {endpoint} - Error: {e}")
    
    return success_count == len(endpoints)

def main():
    """Run comprehensive tests"""
    print("🚀 HKEX Dashboard - Windows AsyncIO Fix Verification")
    print("=" * 60)
    
    # Track test results
    results = []
    
    # Run tests
    results.append(("Health Check", test_health_check()))
    
    process_types_success, process_types_data = test_process_types()
    results.append(("Process Types", process_types_success))
    
    results.append(("API Endpoints", test_api_endpoints()))
    
    # The critical test - process execution
    results.append(("Process Execution (Windows Fix)", test_process_execution()))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{test_name:<30} {status}")
        if success:
            passed += 1
    
    print("-" * 60)
    print(f"TOTAL: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Windows asyncio fix is working correctly!")
        print("✅ System is ready for production use!")
        return True
    else:
        print("⚠️  Some tests failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
