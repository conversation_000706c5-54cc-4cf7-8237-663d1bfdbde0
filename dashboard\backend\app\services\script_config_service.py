"""
Script Configuration Service - JSON-based mockup for dynamic script management.
This service reads script configurations from a JSON file and provides a database-like interface.
Later this will be migrated to a real database implementation.
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime
import asyncio
import aiofiles

logger = logging.getLogger(__name__)

class ScriptConfigService:
    """Service for managing script configurations from JSON file."""
    
    def __init__(self, config_file_path: Optional[str] = None):
        """Initialize the service with configuration file path."""
        if config_file_path is None:
            # Default to config/process_configs.json relative to this file
            self.config_file = Path(__file__).parent.parent / "config" / "process_configs.json"
        else:
            self.config_file = Path(config_file_path)
        
        self._config_cache = None
        self._last_modified = None
        logger.info(f"ScriptConfigService initialized with config file: {self.config_file}")
    
    async def _load_config(self, force_reload: bool = False) -> Dict[str, Any]:
        """Load configuration from JSON file with caching."""
        try:
            # Check if file exists
            if not self.config_file.exists():
                logger.error(f"Configuration file not found: {self.config_file}")
                return {"scripts": {}, "metadata": {}}
            
            # Check if we need to reload
            current_modified = self.config_file.stat().st_mtime
            if not force_reload and self._config_cache and self._last_modified == current_modified:
                return self._config_cache
            
            # Load configuration
            async with aiofiles.open(self.config_file, 'r', encoding='utf-8') as f:
                content = await f.read()
                config = json.loads(content)
            
            # Cache the configuration
            self._config_cache = config
            self._last_modified = current_modified
            
            logger.info(f"Loaded configuration with {len(config.get('scripts', {}))} scripts")
            return config
            
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in configuration file: {e}")
            return {"scripts": {}, "metadata": {}}
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            return {"scripts": {}, "metadata": {}}
    
    async def get_all_scripts(self) -> Dict[str, Dict[str, Any]]:
        """Get all script configurations."""
        config = await self._load_config()
        return config.get("scripts", {})
    
    async def get_script(self, script_key: str) -> Optional[Dict[str, Any]]:
        """Get a specific script configuration by key."""
        scripts = await self.get_all_scripts()
        return scripts.get(script_key)
    
    async def script_exists(self, script_key: str) -> bool:
        """Check if a script configuration exists."""
        script = await self.get_script(script_key)
        return script is not None
    
    async def get_script_keys(self) -> List[str]:
        """Get list of all script keys."""
        scripts = await self.get_all_scripts()
        return list(scripts.keys())
    
    async def get_process_types(self) -> Dict[str, Dict[str, Any]]:
        """Get process types in the format expected by the orchestrator."""
        scripts = await self.get_all_scripts()
        return {
            name: {
                'description': config['description'],
                'timeout': config['timeout'],
                'requires_params': config['requires_params'],
                'optional_params': config.get('optional_params', [])
            }
            for name, config in scripts.items()
        }
    
    async def validate_script_config(self, script_config: Dict[str, Any]) -> List[str]:
        """Validate a script configuration and return list of errors."""
        errors = []
        
        # Required fields
        required_fields = ['script', 'description', 'timeout']
        for field in required_fields:
            if field not in script_config:
                errors.append(f"Missing required field: {field}")
        
        # Validate timeout
        if 'timeout' in script_config:
            try:
                timeout = int(script_config['timeout'])
                if timeout <= 0:
                    errors.append("Timeout must be a positive integer")
            except (ValueError, TypeError):
                errors.append("Timeout must be a valid integer")
        
        # Validate parameter lists
        for param_field in ['requires_params', 'optional_params']:
            if param_field in script_config:
                if not isinstance(script_config[param_field], list):
                    errors.append(f"{param_field} must be a list")
                else:
                    for param in script_config[param_field]:
                        if not isinstance(param, str):
                            errors.append(f"All parameters in {param_field} must be strings")
        
        return errors
    
    async def reload_config(self) -> bool:
        """Force reload configuration from file."""
        try:
            await self._load_config(force_reload=True)
            logger.info("Configuration reloaded successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to reload configuration: {e}")
            return False
    
    async def get_metadata(self) -> Dict[str, Any]:
        """Get configuration metadata."""
        config = await self._load_config()
        return config.get("metadata", {})

    async def validate_process_type(self, process_type: str) -> bool:
        """Validate if a process type exists in the configuration."""
        return await self.script_exists(process_type)

    async def get_valid_process_types(self) -> List[str]:
        """Get list of all valid process type names."""
        return await self.get_script_keys()

# Global instance
script_config_service = ScriptConfigService()
