# HKEX Dashboard

A comprehensive web-based dashboard for monitoring and orchestrating the HKEX Option Report Processing System.

## Features

- **Process Orchestration**: Start/stop/monitor data processing scripts
- **Real-time Monitoring**: Live updates on processing status and progress
- **Data Quality Monitoring**: Automated validation and quality scoring
- **Troubleshooting Tools**: Error logs, diagnostics, and recovery utilities
- **System Health**: Database connectivity and performance monitoring

## Architecture

- **Backend**: FastAPI with SQLAlchemy, Celery for background tasks
- **Frontend**: React with TypeScript, Material-UI components
- **Database**: PostgreSQL (existing HKEX database)
- **Cache**: Redis for session management and caching
- **Real-time**: WebSocket connections for live updates

### Recent UI and Backend Fixes (May 2025)

Significant debugging and enhancements were completed to address runtime issues and improve UI stability:

*   **Python Script Execution**: Resolved errors in `UpdateIndexOptionPostgres.py` related to SQLAlchemy database interactions and `yfinance` data fetching.
*   **Orchestrator Environment**: Ensured the backend orchestrator (`simple_orchestrator.py`) correctly uses the project's Python virtual environment.
*   **Log Viewer UI**: Fixed a "black on black" text issue in `ProcessMonitor.tsx` and `RealTimeLogViewer.tsx` by applying direct `sx` styling and resolving CSS conflicts. Addressed a `logs.map is not a function` error in `RealTimeLogViewer.tsx`.

These fixes have improved the reliability of script execution and the usability of the real-time log monitoring features.

## Quick Start

### Environment Configuration

The application uses environment variables for port configuration. Copy the example environment file and adjust as needed:

```bash
# Copy environment template
cp .env.example .env

# Edit configuration
# FRONTEND_PORT=3080
# BACKEND_PORT=8004
```

### Development Setup

1. Install dependencies:
```bash
cd backend && pip install -r requirements.txt
cd ../frontend && npm install
```

2. Start services:
```bash
# Start backend (uses BACKEND_PORT from .env)
cd backend && uvicorn app.main:app --reload --port ${BACKEND_PORT:-8004}

# Start frontend (uses FRONTEND_PORT from .env)
cd frontend && npm start

# Start Celery worker
cd backend && celery -A app.tasks worker --loglevel=info
```

3. Access the dashboard at http://localhost:${FRONTEND_PORT} (default: http://localhost:3080)

### Production Deployment

```bash
# Using docker-compose with environment variables
docker-compose up -d
```

Access the application at http://localhost:${FRONTEND_PORT} (configured in .env file)

## Documentation

- [User Guide](docs/user-guide.md)
- [API Documentation](docs/api.md)
- [Deployment Guide](docs/deployment.md)
