#!/usr/bin/env python3
"""
API Endpoint Test - simulates the HTTP request that was failing.
This reproduces the exact scenario that was causing the 400 Bad Request.
"""

import sys
import os
from pathlib import Path
import json

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

def simulate_api_request():
    """Simulate the API request that was failing."""
    print("=== Simulating API Request: POST /api/v1/processes/start ===")
    
    try:
        from app.services.simple_orchestrator import orchestrator
        
        # This is the exact request that was causing the 400 error
        request_data = {
            "process_type": "update_index_options",
            "parameters": {}  # Empty parameters - this was the problem
        }
        
        print(f"Request data: {json.dumps(request_data, indent=2)}")
        
        # Simulate the validation logic from the API endpoint
        process_type = request_data["process_type"]
        parameters = request_data["parameters"]
        
        # Get process configuration
        config = orchestrator.process_configs.get(process_type)
        if not config:
            print(f"✗ Process type '{process_type}' not found")
            return False
        
        print(f"Process config: {json.dumps(config, indent=2)}")
        
        # Check required parameters (this is where it was failing)
        required_params = config.get('requires_params', [])
        missing_params = [param for param in required_params if param not in parameters]
        
        if missing_params:
            # This was the original error condition
            error_msg = f"Required parameter(s) missing for {process_type}: {', '.join(missing_params)}"
            print(f"✗ VALIDATION FAILED: {error_msg}")
            print("✗ This would return 400 Bad Request")
            return False
        else:
            print("✓ VALIDATION PASSED: All required parameters satisfied")
            print("✓ This would return 200 OK and start the process")
            return True
        
    except Exception as e:
        print(f"✗ API simulation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_original_problem():
    """Test the exact scenario that was reported in the issue."""
    print("\n=== Testing Original Problem Scenario ===")
    
    print("Original error message: 'Required parameter 'txn_date' missing for update_index_options'")
    print("This occurred when calling POST /api/v1/processes/start with empty parameters")
    
    success = simulate_api_request()
    
    if success:
        print("\n🎉 ORIGINAL PROBLEM IS FIXED!")
        print("The API endpoint should now accept requests without txn_date parameter.")
    else:
        print("\n❌ ORIGINAL PROBLEM STILL EXISTS!")
        print("The API endpoint is still rejecting requests without txn_date parameter.")
    
    return success

if __name__ == "__main__":
    print("HKEX Dashboard - API Endpoint Fix Test")
    print("="*50)
    
    try:
        result = test_original_problem()
        
        if result:
            print("\n✅ SUCCESS: The 400 Bad Request issue has been resolved!")
            print("You can now start processes without providing txn_date parameter.")
        else:
            print("\n❌ FAILURE: The 400 Bad Request issue still exists!")
        
        sys.exit(0 if result else 1)
        
    except Exception as e:
        print(f"Test crashed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
