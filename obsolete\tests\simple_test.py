#!/usr/bin/env python3
"""
Minimal test to check UpdateIndexOptionPostgres.py import and basic execution
"""

import os
import sys
import subprocess

# Set up environment
script_dir = r"O:\Github\MaxPain\MaxPain2024"
os.chdir(script_dir)
sys.path.insert(0, script_dir)

print(f"Working directory: {os.getcwd()}")
print(f"Python path includes: {script_dir}")

# Test basic import of the script
print("\n=== Testing basic imports ===")
try:
    # Test dotenv loading
    from dotenv import load_dotenv
    load_dotenv()
    print("✓ dotenv loaded")
    
    # Check critical environment variables
    platform = os.environ.get('platform', 'NOT_SET')
    will9700_db = os.environ.get('WILL9700_DB', 'NOT_SET')
    log_level = os.environ.get('LOG_LEVEL', 'NOT_SET')
    
    print(f"  platform: {platform}")
    print(f"  WILL9700_DB: {will9700_db}")
    print(f"  LOG_LEVEL: {log_level}")
    
except Exception as e:
    print(f"✗ Error loading environment: {e}")

# Test Storacle import
try:
    import Storacle
    print("✓ Storacle imported successfully")
except Exception as e:
    print(f"✗ Error importing Storacle: {e}")

# Test executing the script with minimal arguments
print("\n=== Testing script execution ===")
try:
    result = subprocess.run(
        [sys.executable, "UpdateIndexOptionPostgres.py", "--help"],
        capture_output=True,
        text=True,
        timeout=10,
        cwd=script_dir
    )
    
    print(f"Help command return code: {result.returncode}")
    if result.stdout:
        print("Help output:")
        print(result.stdout[:500])  # First 500 chars
    if result.stderr:
        print("Help stderr:")
        print(result.stderr[:500])
        
except Exception as e:
    print(f"✗ Error running help command: {e}")

# Test dry run
print("\n=== Testing dry run ===")
try:
    result = subprocess.run(
        [sys.executable, "UpdateIndexOptionPostgres.py", "--dry-run", "--date", "2024-12-20"],
        capture_output=True,
        text=True,
        timeout=30,
        cwd=script_dir
    )
    
    print(f"Dry run return code: {result.returncode}")
    if result.stdout:
        print("Dry run output:")
        print(result.stdout)
    if result.stderr:
        print("Dry run stderr:")
        print(result.stderr)
        
except Exception as e:
    print(f"✗ Error running dry run: {e}")
