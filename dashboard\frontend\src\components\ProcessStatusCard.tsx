import React from 'react';
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  Ty<PERSON>graphy,
  Box,
  Chip,
  LinearProgress,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  PlayArrow,
  Stop,
  Refresh,
  CheckCircle,
  Error,
  Warning,
  Schedule
} from '@mui/icons-material';
import { ProcessStatus } from '../types';

interface ProcessStatusCardProps {
  process: ProcessStatus;
  onStart?: () => void;
  onStop?: () => void;
  onRefresh?: () => void;
}

const ProcessStatusCard: React.FC<ProcessStatusCardProps> = ({
  process,
  onStart,
  onStop,
  onRefresh
}) => {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle color="success" />;
      case 'failed':
        return <Error color="error" />;
      case 'running':
        return <Schedule color="primary" />;
      case 'pending':
        return <Warning color="warning" />;
      default:
        return <Schedule />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'success';
      case 'failed':
        return 'error';
      case 'running':
        return 'primary';
      case 'pending':
        return 'warning';
      default:
        return 'default';
    }
  };

  const formatProcessName = (processType: string) => {
    return processType
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  return (
    <Card sx={{ minWidth: 275, mb: 2 }}>
      <CardHeader
        title={formatProcessName(process.process)}
        subheader={`Task ID: ${process.task_id}`}
        action={
          <Box>
            {process.status === 'running' && onStop && (
              <Tooltip title="Stop Process">
                <IconButton onClick={onStop} color="error">
                  <Stop />
                </IconButton>
              </Tooltip>
            )}
            {(process.status === 'success' || process.status === 'failed') && onStart && (
              <Tooltip title="Restart Process">
                <IconButton onClick={onStart} color="primary">
                  <PlayArrow />
                </IconButton>
              </Tooltip>
            )}
            <Tooltip title="Refresh Status">
              <IconButton onClick={onRefresh}>
                <Refresh />
              </IconButton>
            </Tooltip>
          </Box>
        }
      />
      <CardContent>
        <Box display="flex" alignItems="center" mb={2}>
          {getStatusIcon(process.status)}
          <Chip
            label={process.status.toUpperCase()}
            color={getStatusColor(process.status) as any}
            sx={{ ml: 1 }}
          />
        </Box>

        {process.progress !== undefined && (
          <Box mb={2}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Progress: {process.progress}%
            </Typography>
            <LinearProgress variant="determinate" value={process.progress} />
          </Box>
        )}

        {process.records_processed && (
          <Typography variant="body2" color="text.secondary">
            Records Processed: {process.records_processed.toLocaleString()}
          </Typography>
        )}

        {process.message && (
          <Typography variant="body2" sx={{ mt: 1 }}>
            {process.message}
          </Typography>
        )}

        <Box display="flex" justifyContent="space-between" mt={2}>
          {process.started_at && (
            <Typography variant="caption" color="text.secondary">
              Started: {new Date(process.started_at).toLocaleString()}
            </Typography>
          )}
          {process.completed_at && (
            <Typography variant="caption" color="text.secondary">
              Completed: {new Date(process.completed_at).toLocaleString()}
            </Typography>
          )}
        </Box>
      </CardContent>
    </Card>
  );
};

export default ProcessStatusCard;
