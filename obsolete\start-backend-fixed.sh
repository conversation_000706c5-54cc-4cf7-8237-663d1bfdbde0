#!/bin/bash

echo "🚀 HKEX Dashboard Backend Startup Script"
echo "========================================"

# Clean up any existing containers
echo "🧹 Cleaning up existing containers..."
cd /o/Github/MaxPain/MaxPain2024/dashboard
docker-compose -f docker-compose.dev.yml down -v

# Rebuild images to ensure all changes are applied
echo "🔨 Building fresh Docker images..."
docker-compose -f docker-compose.dev.yml build --no-cache

# Start the services
echo "🚀 Starting services..."
docker-compose -f docker-compose.dev.yml up -d postgres redis

# Wait for database to be ready
echo "⏳ Waiting for PostgreSQL to be ready..."
sleep 10

# Check if PostgreSQL is responding
echo "🔍 Checking PostgreSQL connection..."
docker-compose -f docker-compose.dev.yml exec postgres pg_isready -U postgres

# Start backend service
echo "🖥️ Starting backend service..."
docker-compose -f docker-compose.dev.yml up backend

echo "✅ Startup complete!"
echo "Backend should be available at: http://localhost:8000"
echo "API docs should be available at: http://localhost:8000/docs"
