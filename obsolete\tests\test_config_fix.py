#!/usr/bin/env python3
"""
Simple test to verify the orchestrator configuration fix.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

def test_configuration():
    """Test that the orchestrator configuration is fixed"""
    print("Testing orchestrator configuration fix...")
    
    try:
        from services.simple_orchestrator import SimpleOrchestrator
        from services.websocket_manager import WebSocketManager
        
        # Create orchestrator instance
        websocket_manager = WebSocketManager()
        orchestrator = SimpleOrchestrator(websocket_manager)
        
        # Get the update_index_options configuration
        config = orchestrator.process_configs.get('update_index_options')
        if not config:
            print("❌ ERROR: update_index_options not found in configuration")
            return False
        
        print(f"Configuration found:")
        print(f"  Required params: {config['requires_params']}")
        print(f"  Optional params: {config['optional_params']}")
        
        # Check that txn_date is now optional
        if 'txn_date' in config['requires_params']:
            print("❌ ERROR: txn_date is still in required_params")
            return False
        
        if 'txn_date' not in config['optional_params']:
            print("❌ ERROR: txn_date is not in optional_params")
            return False
        
        print("✅ SUCCESS: txn_date is now optional for update_index_options")
        
        # Test validation logic
        print("\nTesting validation logic...")
        for param in config['requires_params']:
            if param not in {}:  # Empty parameters dict
                print(f"❌ ERROR: Would fail validation for missing required param: {param}")
                return False
        
        print("✅ SUCCESS: Validation passes with empty parameters")
        return True
        
    except Exception as e:
        print(f"❌ ERROR: Exception occurred: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("HKEX Dashboard - Configuration Fix Test")
    print("="*50)
    
    if test_configuration():
        print("\n🎉 Configuration fix is working correctly!")
        print("The 400 Bad Request error should now be resolved.")
    else:
        print("\n❌ Configuration fix failed!")
        sys.exit(1)
