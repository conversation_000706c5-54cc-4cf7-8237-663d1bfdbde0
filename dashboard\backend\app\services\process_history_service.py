"""
Process History Service for managing persistent process execution data
"""
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timezone
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_, or_
from core.database import get_db
from models.dashboard import ProcessExecution

logger = logging.getLogger(__name__)

class ProcessHistoryService:
    """Service for managing process execution history in the database"""
    
    @staticmethod
    def create_process_execution(
        task_id: str,
        process_name: str,
        script_path: str,
        parameters: Dict[str, Any],
        db: Session
    ) -> ProcessExecution:
        """Create a new process execution record"""
        try:
            process_execution = ProcessExecution(
                task_id=task_id,
                process_name=process_name,
                script_path=script_path,
                status="pending",
                parameters=parameters,
                start_time=datetime.now(timezone.utc)
            )
            
            db.add(process_execution)
            db.commit()
            db.refresh(process_execution)
            
            logger.info(f"Created process execution record for task_id: {task_id}")
            return process_execution
            
        except Exception as e:
            logger.error(f"Error creating process execution record: {e}")
            db.rollback()
            raise
    
    @staticmethod
    def update_process_status(
        task_id: str,
        status: str,
        stdout: Optional[str] = None,
        stderr: Optional[str] = None,
        return_code: Optional[int] = None,
        end_time: Optional[datetime] = None,
        db: Session = None
    ) -> Optional[ProcessExecution]:
        """Update process execution status and details"""
        try:
            if db is None:
                # This is a fallback - in practice, the orchestrator should pass the db session
                return None
                
            process_execution = db.query(ProcessExecution).filter(
                ProcessExecution.task_id == task_id
            ).first()
            
            if not process_execution:
                logger.warning(f"Process execution not found for task_id: {task_id}")
                return None
              # Update fields
            logger.info(f"Updating process {task_id} from status '{process_execution.status}' to '{status}'")
            process_execution.status = status
            if stdout is not None:
                process_execution.stdout = stdout
            if stderr is not None:
                process_execution.stderr = stderr
            if return_code is not None:
                process_execution.return_code = return_code
            if end_time is not None:
                process_execution.end_time = end_time
                
                # Calculate duration
                if process_execution.start_time:
                    duration = end_time - process_execution.start_time
                    process_execution.duration_seconds = duration.total_seconds()
            
            process_execution.updated_at = datetime.now(timezone.utc)
            
            db.commit()
            db.refresh(process_execution)
            
            logger.info(f"Updated process execution status to {status} for task_id: {task_id}")
            return process_execution
            
        except Exception as e:
            logger.error(f"Error updating process execution: {e}")
            if db:
                db.rollback()
            raise
    
    @staticmethod
    def get_process_history(
        db: Session,
        skip: int = 0,
        limit: int = 100,
        status_filter: Optional[str] = None,
        process_name_filter: Optional[str] = None,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None
    ) -> List[ProcessExecution]:
        """Get process execution history with optional filters"""
        try:
            query = db.query(ProcessExecution)
            
            # Apply filters
            if status_filter:
                query = query.filter(ProcessExecution.status == status_filter)
            
            if process_name_filter:
                query = query.filter(ProcessExecution.process_name.ilike(f"%{process_name_filter}%"))
            
            if date_from:
                query = query.filter(ProcessExecution.start_time >= date_from)
                
            if date_to:
                query = query.filter(ProcessExecution.start_time <= date_to)
            
            # Order by start time (most recent first)
            query = query.order_by(desc(ProcessExecution.start_time))
            
            # Apply pagination
            processes = query.offset(skip).limit(limit).all()
            
            logger.info(f"Retrieved {len(processes)} process execution records")
            return processes
            
        except Exception as e:
            logger.error(f"Error retrieving process history: {e}")
            raise
    
    @staticmethod
    def get_process_by_task_id(task_id: str, db: Session) -> Optional[ProcessExecution]:
        """Get a specific process execution by task_id"""
        try:
            process_execution = db.query(ProcessExecution).filter(
                ProcessExecution.task_id == task_id
            ).first()
            
            return process_execution
            
        except Exception as e:
            logger.error(f"Error retrieving process by task_id {task_id}: {e}")
            raise
    
    @staticmethod
    def get_active_processes(db: Session) -> List[ProcessExecution]:
        """Get currently active (running or pending) processes"""
        try:
            active_processes = db.query(ProcessExecution).filter(
                or_(
                    ProcessExecution.status == "pending",
                    ProcessExecution.status == "running"
                )
            ).order_by(desc(ProcessExecution.start_time)).all()
            
            logger.info(f"Retrieved {len(active_processes)} active processes")
            return active_processes
            
        except Exception as e:
            logger.error(f"Error retrieving active processes: {e}")
            raise
    
    @staticmethod
    def get_process_statistics(db: Session) -> Dict[str, Any]:
        """Get process execution statistics"""
        try:
            total_processes = db.query(ProcessExecution).count()
            
            completed_processes = db.query(ProcessExecution).filter(
                ProcessExecution.status == "completed"
            ).count()
            
            failed_processes = db.query(ProcessExecution).filter(
                ProcessExecution.status == "failed"
            ).count()
            
            running_processes = db.query(ProcessExecution).filter(
                ProcessExecution.status == "running"
            ).count()
            
            pending_processes = db.query(ProcessExecution).filter(
                ProcessExecution.status == "pending"
            ).count()
            
            # Get recent processes (last 7 days)
            from datetime import timedelta
            week_ago = datetime.now(timezone.utc) - timedelta(days=7)
            recent_processes = db.query(ProcessExecution).filter(
                ProcessExecution.start_time >= week_ago
            ).count()
            
            statistics = {
                "total_processes": total_processes,
                "completed_processes": completed_processes,
                "failed_processes": failed_processes,
                "running_processes": running_processes,
                "pending_processes": pending_processes,
                "recent_processes": recent_processes,
                "success_rate": (completed_processes / total_processes * 100) if total_processes > 0 else 0
            }
            
            logger.info(f"Generated process statistics: {statistics}")
            return statistics
            
        except Exception as e:
            logger.error(f"Error generating process statistics: {e}")
            raise
    
    @staticmethod
    def cleanup_old_processes(db: Session, days_to_keep: int = 30) -> int:
        """Clean up old process execution records"""
        try:
            from datetime import timedelta
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_to_keep)
            
            old_processes = db.query(ProcessExecution).filter(
                and_(
                    ProcessExecution.start_time < cutoff_date,
                    or_(
                        ProcessExecution.status == "completed",
                        ProcessExecution.status == "failed",
                        ProcessExecution.status == "cancelled"
                    )
                )
            ).all()
            
            count = len(old_processes)
            
            for process in old_processes:
                db.delete(process)
            
            db.commit()
            
            logger.info(f"Cleaned up {count} old process execution records")
            return count
            
        except Exception as e:
            logger.error(f"Error cleaning up old processes: {e}")
            db.rollback()
            raise

# Create a singleton instance
process_history_service = ProcessHistoryService()
