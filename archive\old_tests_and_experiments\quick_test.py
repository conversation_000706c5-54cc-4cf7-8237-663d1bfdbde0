#!/usr/bin/env python3
"""Quick test of backend endpoints"""
import requests

def quick_test():
    base_url = "http://localhost:8000"
    
    print("🚀 Quick Backend Test")
    
    # Test health
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        print(f"Health: {response.status_code} - {response.json()}")
    except Exception as e:
        print(f"Health failed: {e}")
    
    # Test process types
    try:
        response = requests.get(f"{base_url}/api/v1/processes/types", timeout=5)
        print(f"Process types: {response.status_code} - Got {len(response.json().get('process_types', []))} types")
    except Exception as e:
        print(f"Process types failed: {e}")
    
    # Test active processes
    try:
        response = requests.get(f"{base_url}/api/v1/processes/active", timeout=5)
        print(f"Active processes: {response.status_code} - {len(response.json())} active")
    except Exception as e:
        print(f"Active processes failed: {e}")

if __name__ == "__main__":
    quick_test()
