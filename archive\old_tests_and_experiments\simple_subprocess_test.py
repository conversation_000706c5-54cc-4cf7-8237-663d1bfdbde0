import asyncio
import subprocess
import sys
import os

print(f"Python version: {sys.version}")
print(f"Platform: {sys.platform}")

# Apply the same Windows fix as in our orchestrator
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    print("Set ProactorEventLoop policy")

async def test_subprocess():
    loop = asyncio.get_running_loop()
    print(f"Event loop type: {type(loop)}")
    
    # Test simple command
    try:
        process = await asyncio.create_subprocess_exec(
            'python', '--version',
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        stdout, stderr = await process.communicate()
        print(f"SUCCESS: {stdout.decode().strip()}")
        return True
    except Exception as e:
        print(f"ERROR: {e}")
        return False

if __name__ == "__main__":
    result = asyncio.run(test_subprocess())
    print(f"Test result: {'PASS' if result else 'FAIL'}")
