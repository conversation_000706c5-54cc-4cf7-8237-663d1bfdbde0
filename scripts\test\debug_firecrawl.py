"""
Debug Firecrawl API Response Structure

This script helps understand the actual response structure from Firecrawl API.
"""

import asyncio
from firecrawl import AsyncF<PERSON><PERSON>rawlApp

async def debug_firecrawl_response():
    """Debug the actual Firecrawl response structure"""
    app = AsyncFirecrawlApp(api_key='fc-f514d4245edf41438797e1733226896c')
    
    test_url = 'https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/hsiwo250613.htm'
    print(f"Testing URL: {test_url}")
    
    try:
        response = await app.scrape_url(
            url=test_url,
            formats=['html'],
            only_main_content=False
        )
        
        print(f"Response type: {type(response)}")
        print(f"Response: {response}")
        
        # Try to access different attributes
        if hasattr(response, '__dict__'):
            print(f"Response attributes: {list(response.__dict__.keys())}")
        
        if hasattr(response, 'data'):
            print(f"Response.data: {response.data}")
        
        if hasattr(response, 'html'):
            print(f"Response.html length: {len(response.html) if response.html else 'None'}")
        
        # Try different ways to access the content
        for attr in ['html', 'content', 'data', 'text']:
            if hasattr(response, attr):
                value = getattr(response, attr)
                print(f"response.{attr}: {type(value)} - {str(value)[:100] if value else 'None'}...")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_firecrawl_response())
