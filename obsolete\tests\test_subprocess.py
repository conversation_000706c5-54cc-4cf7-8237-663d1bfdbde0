#!/usr/bin/env python3
"""
Test subprocess creation to verify Windows compatibility.
"""

import asyncio
import sys
import subprocess
from pathlib import Path

# Set Windows event loop policy
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

async def test_subprocess():
    """Test subprocess creation with the same configuration as the orchestrator."""
    print("Testing subprocess creation...")
    
    # Test the command that's failing
    script_path = Path("o:/Github/MaxPain/MaxPain2024/UpdateIndexOptionPostgres.py")
    cmd_args = ['python', str(script_path), '--help']  # Use --help to test without side effects
    
    print(f"Command: {' '.join(cmd_args)}")
    print(f"Script exists: {script_path.exists()}")
    print(f"Event loop type: {type(asyncio.get_running_loop())}")
    
    try:
        if sys.platform == 'win32':
            # Windows-specific subprocess creation (same as in orchestrator)
            process = await asyncio.create_subprocess_exec(
                *cmd_args,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=script_path.parent,
                creationflags=subprocess.CREATE_NO_WINDOW
            )
        else:
            # Unix/Linux subprocess creation
            process = await asyncio.create_subprocess_exec(
                *cmd_args,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=script_path.parent
            )
        
        print("✅ Subprocess created successfully!")
        
        # Get output
        stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=10)
        
        print(f"Return code: {process.returncode}")
        if stdout:
            print(f"Stdout: {stdout.decode()[:200]}...")
        if stderr:
            print(f"Stderr: {stderr.decode()[:200]}...")
            
        return process.returncode == 0
        
    except Exception as e:
        print(f"❌ Subprocess creation failed: {e}")
        
        # Try fallback approach
        if sys.platform == 'win32':
            print("Trying fallback shell method...")
            try:
                process = await asyncio.create_subprocess_shell(
                    ' '.join(cmd_args),
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    cwd=script_path.parent
                )
                
                stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=10)
                print("✅ Fallback subprocess creation successful!")
                print(f"Return code: {process.returncode}")
                return process.returncode == 0
                
            except Exception as fallback_error:
                print(f"❌ Fallback also failed: {fallback_error}")
        
        return False

if __name__ == "__main__":
    result = asyncio.run(test_subprocess())
    print(f"\nTest result: {'PASSED' if result else 'FAILED'}")
