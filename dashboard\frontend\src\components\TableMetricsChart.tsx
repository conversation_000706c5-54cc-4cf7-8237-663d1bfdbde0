import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, Typography, Box } from '@mui/material';
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { TableMetric } from '../types';

interface TableMetricsChartProps {
  data: TableMetric[];
  height?: number;
}

const TableMetricsChart: React.FC<TableMetricsChartProps> = ({ 
  data, 
  height = 400 
}) => {
  // Format data for recharts
  const chartData = data.map(item => ({
    table: item.table_name.length > 15 
      ? item.table_name.substring(0, 12) + '...' 
      : item.table_name,
    fullTableName: item.table_name,
    rows: item.record_count || 0,
    sizeKB: item.table_size_bytes ? Math.round(item.table_size_bytes / 1024) : 0
  }));

  // Custom tooltip to show full table name
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <Box sx={{ 
          backgroundColor: 'rgba(255, 255, 255, 0.95)', 
          p: 1, 
          border: '1px solid #ccc',
          borderRadius: 1
        }}>
          <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
            {data.fullTableName}
          </Typography>
          <Typography variant="body2" color="primary">
            Rows: {data.rows.toLocaleString()}
          </Typography>
          <Typography variant="body2" color="secondary">
            Size: {data.sizeKB.toLocaleString()} KB
          </Typography>
        </Box>
      );
    }
    return null;
  };

  return (
    <Card>
      <CardHeader 
        title="Database Table Metrics"
        subheader="Row counts and table sizes"
      />
      <CardContent>
        <ResponsiveContainer width="100%" height={height}>
          <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 60 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              dataKey="table" 
              angle={-45}
              textAnchor="end"
              height={80}
              tick={{ fontSize: 11 }}
            />
            <YAxis 
              yAxisId="rows"
              orientation="left"
              tick={{ fontSize: 12 }}
              label={{ value: 'Row Count', angle: -90, position: 'insideLeft' }}
            />
            <YAxis 
              yAxisId="size"
              orientation="right"
              tick={{ fontSize: 12 }}
              label={{ value: 'Size (KB)', angle: 90, position: 'insideRight' }}
            />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Bar
              yAxisId="rows"
              dataKey="rows"
              fill="#1976d2"
              name="Row Count"
              radius={[2, 2, 0, 0]}
            />
            <Bar
              yAxisId="size"
              dataKey="sizeKB"
              fill="#ed6c02"
              name="Size (KB)"
              radius={[2, 2, 0, 0]}
            />
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};

export default TableMetricsChart;
