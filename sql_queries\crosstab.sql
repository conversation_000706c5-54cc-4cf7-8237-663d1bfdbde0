https://github.com/hnsl/colpivot/blob/master/colpivot.sql
-- Copyright © 2015, <PERSON><PERSON> Landeholm <<EMAIL>>
-- This Source Code Form is subject to the terms of the Mozilla Public
-- License, v. 2.0. If a copy of the MPL was not distributed with this
-- file, You can obtain one at http://mozilla.org/MPL/2.0/.

-- See the README.md file distributed with this project for documentation.
create or replace function colpivot(
    out_table varchar, in_query varchar,
    key_cols varchar[], class_cols varchar[],
    value_e varchar, col_order varchar
) returns void as $$
    declare
        in_table varchar;
        col varchar;
        ali varchar;
        on_e varchar;
        i integer;
        rec record;
        query varchar;
        -- This is actually an array of arrays but postgres does not support an array of arrays type so we flatten it.
        -- We could theoretically use the matrix feature but it's extremly cancerogenous and we would have to involve
        -- custom aggrigates. For most intents and purposes postgres does not have a multi-dimensional array type.
        clsc_cols text[] := array[]::text[];
        n_clsc_cols integer;
        n_class_cols integer;
    begin
        in_table := quote_ident('__' || out_table || '_in');
        execute ('create temp table ' || in_table || ' on commit drop as ' || in_query);
        -- get ordered unique columns (column combinations)
        query := 'select array[';
        i := 0;
        foreach col in array class_cols loop
            if i > 0 then
                query := query || ', ';
            end if;
            query := query || 'quote_literal(' || quote_ident(col) || ')';
            i := i + 1;
        end loop;
        query := query || '] x from ' || in_table;
        for j in 1..2 loop
            if j = 1 then
                query := query || ' group by ';
            else
                query := query || ' order by ';
                if col_order is not null then
                    query := query || col_order || ' ';
                    exit;
                end if;
            end if;
            i := 0;
            foreach col in array class_cols loop
                if i > 0 then
                    query := query || ', ';
                end if;
                query := query || quote_ident(col);
                i := i + 1;
            end loop;
        end loop;
        -- raise notice '%', query;
        for rec in
            execute query
        loop
            clsc_cols := array_cat(clsc_cols, rec.x);
        end loop;
        n_class_cols := array_length(class_cols, 1);
        n_clsc_cols := array_length(clsc_cols, 1) / n_class_cols;
        -- build target query
        query := 'select ';
        i := 0;
        foreach col in array key_cols loop
            if i > 0 then
                query := query || ', ';
            end if;
            query := query || '_key.' || quote_ident(col) || ' ';
            i := i + 1;
        end loop;
        for j in 1..n_clsc_cols loop
            query := query || ', ';
            col := '';
            for k in 1..n_class_cols loop
                if k > 1 then
                    col := col || ', ';
                end if;
                col := col || clsc_cols[(j - 1) * n_class_cols + k];
            end loop;
            ali := '_clsc_' || j::text;
            query := query || '(' || replace(value_e, '#', ali) || ')' || ' as ' || quote_ident(col) || ' ';
        end loop;
        query := query || ' from (select distinct ';
        i := 0;
        foreach col in array key_cols loop
            if i > 0 then
                query := query || ', ';
            end if;
            query := query || quote_ident(col) || ' ';
            i := i + 1;
        end loop;
        query := query || ' from ' || in_table || ') _key ';
        for j in 1..n_clsc_cols loop
            ali := '_clsc_' || j::text;
            on_e := '';
            i := 0;
            foreach col in array key_cols loop
                if i > 0 then
                    on_e := on_e || ' and ';
                end if;
                on_e := on_e || ali || '.' || quote_ident(col) || ' = _key.' || quote_ident(col) || ' ';
                i := i + 1;
            end loop;
            for k in 1..n_class_cols loop
                on_e := on_e || ' and ';
                on_e := on_e || ali || '.' || quote_ident(class_cols[k]) || ' = ' || clsc_cols[(j - 1) * n_class_cols + k];
            end loop;
            query := query || 'left join ' || in_table || ' as ' || ali || ' on ' || on_e || ' ';
        end loop;
        -- raise notice '%', query;
        execute ('create temp table ' || quote_ident(out_table) || ' on commit drop as ' || query);
        -- cleanup temporary in_table before we return
        execute ('drop table ' || in_table)
        return;
    end;
$$ language plpgsql volatile;

begin;

create temp table _test (
    year int,
    month int,
    country varchar,
    state varchar,
    income int
) on commit drop;

insert into _test values
    (1985, 01, 'sweden', '', 10),
    (1985, 01, 'denmark', '', 11),
    (1985, 01, 'usa', 'washington', 13),
    (1985, 02, 'sweden', '', 20),
    (1985, 02, 'usa', 'washington', 21),
    (1985, 03, 'sweden', '', 34),
    (1985, 03, 'denmark', '', 31),
    (1985, 03, 'usa', 'washington', 39),
    (1990, 12, 'sweden', '', 42),
    (1990, 12, 'denmark', '', 43),
    (1990, 12, 'usa', 'washington', 49),
    (1990, 12, 'germany', '', 45);

select colpivot('_test_pivoted', 'select * from _test',
    array['year', 'month'], array['country', 'state'], '#.income', null);

select * from _test_pivoted order by year, month;


create Table ProductSales
  (    Productname varchar(50),
      Year int,
      Sales int
  );
Insert into ProductSales values
      ('A',2017,100),
      ('A',2018,150),
      ('A',2019,300),
      ('A',2020,500),
      ('A',2021,450),
      ('A',2022,675),
      ('B',2017,0),
      ('B',2018,900),
      ('B',2019,1120),
      ('B',2020,750),
      ('B',2021,1500),
      ('B',2022,1980);
      
Select * from ProductSales;




create extension tablefunc;


 Select * from
  crosstab('select Productname,year,sales from ProductSales order by 1,2')
  as ProductSales(Productname varchar(50),year1 int,y2 int, y3 int,y4 int,y5 int,y6 int);    

create Table ProductSales_t
  (   Productname text,
      Year text,
      Sales text
  );
Insert into ProductSales_t values
      ('A',2017,100),
      ('A',2018,150),
      ('A',2019,300),
      ('A',2020,500),
      ('A',2021,450),
      ('A',2022,675),
      ('B',2017,0),
      ('B',2018,900),
      ('B',2019,1120),
      ('B',2020,750),
      ('B',2021,1500),
      ('B',2022,1980);
Select * from crosstab3('select Productname,year,sales from ProductSales_t order by 1,2')


##
CREATE TYPE my_crosstab_custom AS (
    productname varchar(50),
    Year int,
    sales int
);

CREATE OR REPLACE FUNCTION crosstab_custom(text)
  RETURNS setof my_crosstab_custom
  AS '$libdir/tablefunc','crosstab' LANGUAGE C STABLE STRICT;

Select * from crosstab_custom('select Productname,year,sales from ProductSales order by 1,2')  


create table test (
    year int,
    month int,
    country varchar,
    state varchar,
    income int
) on commit drop;

insert into test values
    (1985, 01, 'sweden', '', 10),
    (1985, 01, 'denmark', '', 11),
    (1985, 01, 'usa', 'washington', 13),
    (1985, 02, 'sweden', '', 20),
    (1985, 02, 'usa', 'washington', 21),
    (1985, 03, 'sweden', '', 34),
    (1985, 03, 'denmark', '', 31),
    (1985, 03, 'usa', 'washington', 39),
    (1990, 12, 'sweden', '', 42),
    (1990, 12, 'denmark', '', 43),
    (1990, 12, 'usa', 'washington', 49),
    (1990, 12, 'germany', '', 45);

select colpivot('test_pivoted', 'select * from _test',
    array['year', 'month'], array['country', 'state'], '#.income', null);

select * from test_pivoted order by year, month;

begin;

create temp table _test on commit drop
as select * from ProductSales;

select * from _test;

select colpivot('_test_pivoted', 'select * from _test',
    array['product'], array['year'], '#.sales', null);

select * from _test_pivoted;


rollback;
select colpivot('test_pivoted', 'select * from test',
    array['year', 'month'], array['country', 'state'], '#.income', null);

select * from test_pivoted order by year, month;

begin;

create temp table _test on commit drop
as SELECT * from (
select a.inst_name,a.txn_date,
        round(avg((case when substr(a.inst_name,21,1) = 'C' then a.iv else 0 end) ),1) AS call_iv, 
        round(avg((case when substr(a.inst_name,21,1) = 'P' then a.iv else 0 end) ),1) AS put_iv 
        from weekly_option_daily_report a
        where a.txn_date >= current_date - 5
        group by a.inst_name, a.txn_date
) as option_iv
WHERE call_iv > 0 or put_iv > 0

select colpivot('_test_pivoted', 'select * from _test',
    array['inst_name'], array['txn_date'], '#.call_iv', null);

select * from _test_pivoted;


create temp table _test on commit drop
as SELECT * from (
select a.inst_name, to_text(a.txn_date),
        round(avg((case when substr(a.inst_name,21,1) = 'C' then a.iv else 0 end) ),1) ||','|| 
        round(avg((case when substr(a.inst_name,21,1) = 'P' then a.iv else 0 end) ),1) AS iv 
        from weekly_option_daily_report a
        where a.txn_date >= current_date - 5
        group by a.inst_name, a.txn_date
) as option_iv
WHERE call_iv > 0 or put_iv > 0;
Select * from crosstab3('select inst_name, txn_date, iv from _test')

select colpivot('_test_pivoted', 'select * from _test',
    array['inst_name'], array['txn_date'], '#.call_iv', null);

select * from _test_pivoted;
create temp table _test on commit drop
as SELECT * from (
select a.inst_name::text, to_char(a.txn_date, 'YYMMDD') as txn_date,
        round(avg((case when substr(a.inst_name,21,1) = 'C' then a.iv else 0 end) ),1) ||','|| 
        round(avg((case when substr(a.inst_name,21,1) = 'P' then a.iv else 0 end) ),1) AS iv 
        from weekly_option_daily_report a
        where a.txn_date >= current_date - 5
        group by a.inst_name, a.txn_date
) as option_iv;

Select * from crosstab3('select inst_name, txn_date, iv from _test')

create VIEW option_iv as
select a.inst_name,a.txn_date,
        round(avg((case when substr(a.inst_name,21,1) = 'C' then a.iv else 0 end) ),1) AS call_iv, 
        round(avg((case when substr(a.inst_name,21,1) = 'P' then a.iv else 0 end) ),1) AS put_iv 
        from weekly_option_daily_report a
        group by a.inst_name, a.txn_date
