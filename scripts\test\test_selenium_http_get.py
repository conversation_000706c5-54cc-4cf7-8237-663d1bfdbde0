#!/usr/bin/env python3
"""
Test script for selenium_http_get() function

This script tests the Selenium-based HTTP fetching functionality
to ensure it works correctly before integrating it into the main
fallback chain.
"""

import sys
import os
from datetime import datetime

# Add the current directory to Python path to import hkex_fetcher
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from hkex_fetcher import selenium_http_get
    print("✅ Successfully imported selenium_http_get from hkex_fetcher")
except ImportError as e:
    print(f"❌ Failed to import selenium_http_get: {e}")
    sys.exit(1)


def test_selenium_basic():
    """Test basic Selenium functionality with a simple website"""
    print("\n" + "="*60)
    print("TEST 1: Basic Selenium functionality")
    print("="*60)
    
    test_url = "https://httpbin.org/html"
    print(f"Testing URL: {test_url}")
    
    try:
        response = selenium_http_get(test_url, timeout=30)
        
        if response is None:
            print("❌ Test failed: No response object returned")
            return False
            
        print(f"Status Code: {response.status_code}")
        print(f"Content Length: {len(response.content)} bytes")
        print(f"Text Length: {len(response.text)} characters")
        
        if response.status_code == 200:
            print("✅ Basic Selenium test PASSED")
            return True
        else:
            print(f"❌ Basic Selenium test FAILED: Status code {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Basic Selenium test FAILED with exception: {e}")
        return False


def test_selenium_hkex():
    """Test Selenium with HKEX website"""
    print("\n" + "="*60)
    print("TEST 2: HKEX website access")
    print("="*60)
    
    # Test with a recent HKEX report URL (adjust date as needed)
    test_date = datetime.now().strftime("%y%m%d")
    test_url = f"https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/hsio{test_date}.htm"
    print(f"Testing HKEX URL: {test_url}")
    
    try:
        response = selenium_http_get(test_url, timeout=45)
        
        if response is None:
            print("❌ HKEX test failed: No response object returned")
            return False
            
        print(f"Status Code: {response.status_code}")
        print(f"Content Length: {len(response.content)} bytes")
        
        if response.status_code == 200:
            # Check if content looks like HKEX report
            content_text = response.text.lower()
            if 'hkex' in content_text or 'option' in content_text or 'derivative' in content_text:
                print("✅ HKEX Selenium test PASSED - Valid HKEX content detected")
                return True
            else:
                print("⚠️  HKEX Selenium test PARTIAL - Got 200 but content doesn't look like HKEX report")
                print("First 200 characters of content:")
                print(response.text[:200])
                return True  # Still consider it a pass since we got a response
        elif response.status_code == 404:
            print("⚠️  HKEX test result: Report not found (404) - This is expected for future dates")
            return True  # 404 is expected for non-existent reports
        else:
            print(f"❌ HKEX Selenium test FAILED: Status code {response.status_code}")
            if hasattr(response, 'error') and response.error:
                print(f"Error details: {response.error}")
            return False
            
    except Exception as e:
        print(f"❌ HKEX Selenium test FAILED with exception: {e}")
        return False


def test_selenium_timeout():
    """Test Selenium timeout handling"""
    print("\n" + "="*60)
    print("TEST 3: Timeout handling")
    print("="*60)
    
    # Use a URL that should timeout (non-existent domain)
    test_url = "https://this-domain-should-not-exist-12345.com"
    print(f"Testing timeout with URL: {test_url}")
    
    try:
        response = selenium_http_get(test_url, timeout=10)
        
        if response is None:
            print("❌ Timeout test failed: No response object returned")
            return False
            
        print(f"Status Code: {response.status_code}")
        
        if response.status_code in [404, 500, 504]:  # Expected error codes
            print("✅ Timeout test PASSED - Proper error handling")
            return True
        else:
            print(f"⚠️  Timeout test UNEXPECTED: Got status code {response.status_code}")
            return True  # Still consider it a pass
            
    except Exception as e:
        print(f"❌ Timeout test FAILED with exception: {e}")
        return False


def main():
    """Run all Selenium tests"""
    print("🧪 Starting Selenium HTTP GET Tests")
    print("=" * 60)
    
    # Check if required dependencies are available
    try:
        from selenium import webdriver
        from webdriver_manager.chrome import ChromeDriverManager
        print("✅ Selenium dependencies are available")
    except ImportError as e:
        print(f"❌ Missing Selenium dependencies: {e}")
        print("Please install with: pip install selenium webdriver-manager")
        return False
    
    tests = [
        ("Basic Functionality", test_selenium_basic),
        ("HKEX Website Access", test_selenium_hkex),
        ("Timeout Handling", test_selenium_timeout)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' crashed with exception: {e}")
            results.append((test_name, False))
    
    # Print summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests PASSED! Selenium HTTP GET is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
