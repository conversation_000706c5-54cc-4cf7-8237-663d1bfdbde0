#!/usr/bin/env python3
"""
Test script to debug the 400 Bad Request error
"""

import sys
import os
import requests
import json

# Add the backend directory to the Python path
backend_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, backend_dir)

def test_process_start_endpoint():
    """Test the process start endpoint with different request formats"""
    
    base_url = "http://localhost:8001"
    endpoint = f"{base_url}/api/v1/processes/start"
    
    print(f"Testing endpoint: {endpoint}")
    
    # Test 1: Simple request format (as shown in previous successful tests)
    test_data_1 = {
        "process": "update_index_options",
        "parameters": {}
    }
    
    print("\n=== Test 1: Simple format ===")
    print(f"Request data: {json.dumps(test_data_1, indent=2)}")
    
    try:
        response = requests.post(
            endpoint,
            json=test_data_1,
            headers={"Content-Type": "application/json"}
        )
        print(f"Response status: {response.status_code}")
        print(f"Response body: {response.text}")
    except Exception as e:
        print(f"Request failed: {e}")
    
    # Test 2: With parameters
    test_data_2 = {
        "process": "update_stock_options",
        "parameters": {
            "txn_date": "2024-01-15",
            "batch_size": 100
        }
    }
    
    print("\n=== Test 2: With parameters ===")
    print(f"Request data: {json.dumps(test_data_2, indent=2)}")
    
    try:
        response = requests.post(
            endpoint,
            json=test_data_2,
            headers={"Content-Type": "application/json"}
        )
        print(f"Response status: {response.status_code}")
        print(f"Response body: {response.text}")
    except Exception as e:
        print(f"Request failed: {e}")
    
    # Test 3: Check if server is responding to health check
    print("\n=== Test 3: Health check ===")
    try:
        health_response = requests.get(f"{base_url}/health")
        print(f"Health check status: {health_response.status_code}")
        print(f"Health check body: {health_response.text}")
    except Exception as e:
        print(f"Health check failed: {e}")
    
    # Test 4: Check process types endpoint
    print("\n=== Test 4: Process types ===")
    try:
        types_response = requests.get(f"{base_url}/api/v1/processes/types")
        print(f"Process types status: {types_response.status_code}")
        print(f"Process types body: {types_response.text}")
    except Exception as e:
        print(f"Process types failed: {e}")

if __name__ == "__main__":
    test_process_start_endpoint()
