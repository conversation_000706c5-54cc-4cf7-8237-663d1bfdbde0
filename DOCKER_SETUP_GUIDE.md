# Docker Setup Guide for MaxPain2024 Project

This guide explains the Docker build structure and process for the MaxPain2024 project.

## Project Structure Overview

The project uses Docker to containerize its main services: backend (FastAPI), frontend (React), Celery workers, and Redis. Docker Compose is used to orchestrate these services for both development and production environments.

Key files and directories:

*   **`o:\\Github\\MaxPain\\MaxPain2024\\docker-compose.yml`**: Defines and configures the services for production.
*   **`o:\\Github\\MaxPain\\MaxPain2024\\docker-compose.dev.yml`**: Defines and configures services for development, often with features like hot-reloading and debugging.
*   **`o:\\Github\\MaxPain\\MaxPain2024\\dashboard\\backend\\Dockerfile`**: Instructions to build the Docker image for the backend, Celery worker, and Celery beat services.
*   **`o:\\Github\\MaxPain\\MaxPain2024\\dashboard\\backend\\Dockerfile.dev`**: (If present, or similar to `Dockerfile` but tailored for development) Instructions for the development image of backend services.
*   **`o:\\Github\\MaxPain\\MaxPain2024\\dashboard\\frontend\\Dockerfile`**: Instructions to build the Docker image for the React frontend.
*   **`o:\\Github\\MaxPain\\MaxPain2024\\dashboard\\frontend\\Dockerfile.dev`**: (If present) Instructions for the development image of the frontend.
*   **`o:\\Github\\MaxPain\\MaxPain2024\\dashboard\\nginx\\Dockerfile`**: Custom nginx container with envsubst support for dynamic configuration.
*   **`o:\\Github\\MaxPain\\MaxPain2024\\dashboard\\nginx\\nginx.conf.template`**: Template file with environment variable placeholders for dynamic nginx configuration.
*   **`o:\\Github\\MaxPain\\MaxPain2024\\dashboard\\nginx\\start-nginx.sh`**: Script that processes templates and starts nginx with dynamic configuration.
*   **`o:\\Github\\MaxPain\\MaxPain2024\\requirements.txt`**: Python dependencies for the backend services.
*   **`o:\\Github\\MaxPain\\MaxPain2024\\scripts\\`**: Contains Python scripts that can be run within the backend containers or as separate tasks.
*   **`o:\\Github\\MaxPain\\MaxPain2024\\.env`**: Environment configuration including BACKEND_PORT for flexible port configuration.

## Docker Build Process

The Docker images are built based on the instructions in the `Dockerfile`s. The `docker-compose.yml` files then use these images to run the services.

### 1. Build Context

*   For the `backend`, `celery_worker`, and `celery_beat` services defined in `docker-compose.yml` and `docker-compose.dev.yml`, the **build context** is set to `.` (the project root: `o:\\Github\\MaxPain\\MaxPain2024\\`).
*   This means that when Docker builds these images, it has access to all files and folders within the project root.
*   The `dockerfile` directive in the compose files then specifies the path to the actual Dockerfile from this root context (e.g., `dockerfile: ./dashboard/backend/Dockerfile`).

### 2. Backend Dockerfile (`dashboard/backend/Dockerfile`)

This file is crucial for the Python-based services. Here\'s a breakdown of its typical structure and key steps:

1.  **`FROM python:3.11-slim`**: Specifies the base image (a slim version of Python 3.11).
2.  **`WORKDIR /app`**: Sets the working directory inside the container to `/app`. Subsequent commands will run from this directory.
3.  **System Dependencies**:
    ```dockerfile
    RUN apt-get update && apt-get install -y \
        gcc \
        g++ \
        postgresql-client \
        && rm -rf /var/lib/apt/lists/*
    ```
    Installs necessary system packages like compilers and the PostgreSQL client (useful for connecting to your external database).
4.  **Python Dependencies**:
    ```dockerfile
    COPY requirements.txt /app/requirements.txt
    RUN pip install --no-cache-dir -r /app/requirements.txt
    ```
    *   Copies the `requirements.txt` file from the build context (project root) into the image at `/app/requirements.txt`.
    *   Installs the Python packages listed in `requirements.txt`.
5.  **Application Code**:
    ```dockerfile
    COPY ./dashboard/backend/app/ /app/
    ```
    *   Copies the backend application code from `o:\\Github\\MaxPain\\MaxPain2024\\dashboard\\backend\\app\\` (relative to the build context) into the `/app/` directory in the image.
6.  **Scripts**:
    ```dockerfile
    COPY ./scripts/ /app/scripts/
    ```
    *   Copies the Python scripts from `o:\\Github\\MaxPain\\MaxPain2024\\scripts\\` (relative to the build context) into `/app/scripts/` inside the image. This makes them available to be executed from within the container.
7.  **Logs Directory**:
    ```dockerfile
    RUN mkdir -p /app/logs
    ```
    *   Creates a logs directory inside the image. Note that `docker-compose.yml` also defines a volume mount for logs (`./logs:/app/logs`), which means logs written to `/app/logs` in the container will persist on the host machine in the `o:\\Github\\MaxPain\\MaxPain2024\\logs` directory.
8.  **`EXPOSE 8000`**: Informs Docker that the application inside the container will listen on port 8000. This doesn\'t publish the port; publishing is done in `docker-compose.yml`.
9.  **`CMD [...]`**: Specifies the default command to run when the container starts (e.g., `uvicorn main:app ...`). This can be overridden in `docker-compose.yml`.

### 3. Frontend Dockerfile (`dashboard/frontend/Dockerfile`)

This file builds the React frontend image. It typically involves:

1.  **`FROM node:<version>-alpine`**: Using a Node.js base image.
2.  **Setting `WORKDIR`**.
3.  **Copying `package.json` and `package-lock.json`**.
4.  **Running `npm install` or `yarn install`** to install frontend dependencies.
5.  **Copying the frontend source code** (e.g., from `dashboard/frontend/src` and `dashboard/frontend/public`).
6.  **Running a build command** (e.g., `npm run build` or `yarn build`) to create a production build of the React app.
7.  Often, a multi-stage build is used:
    *   A "builder" stage to compile the static assets.
    *   A final, lightweight server stage (e.g., using Nginx or Serve) to serve these static assets.

### 4. Docker Compose (`docker-compose.yml` and `docker-compose.dev.yml`)

These files define how the services run and interact:

*   **`services`**: Each service (e.g., `redis`, `backend`, `celery_worker`, `frontend`) is defined here.
*   **`build`**:
    *   `context`: Specifies the directory sent to the Docker daemon for the build (project root in our case for backend services).
    *   `dockerfile`: Path to the Dockerfile from the context.
*   **`image`**: Can be used to specify a pre-built image name or will use the image built from the `build` instructions.
*   **`container_name`**: Assigns a specific name to the container.
*   **`environment`**: Sets environment variables inside the container (e.g., database URLs, API keys).
    *   Crucially, `DATABASE_URL` is configured here to point to your external PostgreSQL instance.
*   **`ports`**: Maps ports from the host to the container (e.g., `"8000:8000"` maps host port 8000 to container port 8000 for the backend).
*   **`volumes`**:
    *   Mounts host directories or named volumes into the container.
    *   Example: `./dashboard/backend/app:/app` (in `docker-compose.dev.yml`) mounts the local backend app code into the container for hot-reloading during development.
    *   Example: `./logs:/app/logs` mounts the local `logs` folder to `/app/logs` in the container, so log files persist on the host.
    *   Example: `./:/workspace` mounts the entire project root into `/workspace` inside the container, providing access to all project files if needed (though for scripts, they are now also `COPY`ied into the image).
*   **`depends_on`**: Specifies service dependencies, controlling startup order.
*   **`command`**: Overrides the default `CMD` in the Dockerfile.
*   **`networks`**: Defines custom networks for services to communicate.

### 5. Nginx Template System (`dashboard/nginx/`)

The project now uses a dynamic nginx configuration system that supports environment variable substitution for flexible deployment configurations.

#### Key Components:

1. **Custom Nginx Dockerfile (`dashboard/nginx/Dockerfile`)**:
   ```dockerfile
   FROM nginx:alpine
   
   # Install gettext for envsubst command
   RUN apk add --no-cache gettext
   
   # Copy template and startup script
   COPY nginx.conf.template /etc/nginx/nginx.conf.template
   COPY start-nginx.sh /start-nginx.sh
   RUN chmod +x /start-nginx.sh
   
   # Use custom startup script
   CMD ["/start-nginx.sh"]
   ```

2. **Nginx Configuration Template (`dashboard/nginx/nginx.conf.template`)**:
   - Contains `${BACKEND_PORT}` placeholder for dynamic port configuration
   - Supports WebSocket proxy configuration
   - Includes proper headers for WebSocket upgrade
   
   Example template section:
   ```nginx
   upstream backend {
       server backend:${BACKEND_PORT};
   }
   
   location /ws {
       proxy_pass http://backend;
       proxy_http_version 1.1;
       proxy_set_header Upgrade $http_upgrade;
       proxy_set_header Connection "upgrade";
       # Additional WebSocket headers...
   }
   ```

3. **Startup Script (`dashboard/nginx/start-nginx.sh`)**:
   ```bash
   #!/bin/sh
   # Process template with environment variables
   envsubst '${BACKEND_PORT}' < /etc/nginx/nginx.conf.template > /etc/nginx/nginx.conf
   
   # Start nginx
   nginx -g 'daemon off;'
   ```

#### How It Works:

1. **Environment Variable Configuration**: 
   - `BACKEND_PORT` is set in `.env` file (default: 8004)
   - Docker Compose passes this to the nginx container

2. **Template Processing**:
   - `start-nginx.sh` runs `envsubst` to replace `${BACKEND_PORT}` with actual value
   - Generates final `nginx.conf` from template

3. **WebSocket Support**:
   - Template includes proper WebSocket proxy configuration
   - Supports both `/ws` and `/ws/` endpoints
   - Handles WebSocket upgrade headers correctly

#### Docker Compose Configuration:

```yaml
nginx:
  build: ./dashboard/nginx
  container_name: maxpain2024-nginx
  env_file: .env
  environment:
    - BACKEND_PORT=${BACKEND_PORT}
  # ... other configuration
```

### 6. Environment Configuration System

#### Backend Port Configuration:

The backend now supports flexible port configuration via environment variables:

1. **Backend Configuration (`dashboard/backend/app/core/config.py`)**:
   ```python
   class Settings(BaseSettings):
       # ... other settings
       backend_port: int = int(os.getenv("BACKEND_PORT", 8000))
   ```

2. **Dockerfile Environment Support (`dashboard/backend/Dockerfile`)**:
   ```dockerfile
   # Use environment variable for port exposure and command
   EXPOSE ${BACKEND_PORT}
   CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "${BACKEND_PORT}", "--workers", "4"]
   ```

3. **Docker Compose Integration**:
   - Uses `env_file: .env` to load environment variables
   - Passes `BACKEND_PORT` to all relevant services
   - Enables consistent port configuration across all containers

#### Benefits:

- **Flexibility**: Easy port changes without code modifications
- **Environment Isolation**: Different ports for dev/staging/production
- **WebSocket Compatibility**: Ensures nginx proxy points to correct backend port
- **Scalability**: Supports multiple backend instances with different ports

## Running the Setup

### Environment Setup
1. **Configure Environment Variables**:
   ```bash
   # Copy and edit .env file
   cp .env.example .env
   
   # Set backend port (example)
   echo "BACKEND_PORT=8004" >> .env
   ```

### Building and Starting Services

1.  **Build Images**:
    *   `docker-compose build` (builds all services defined in `docker-compose.yml`)
    *   `docker-compose -f docker-compose.dev.yml build` (builds for development)
    *   Or build specific services: `docker-compose build backend nginx`
    
    **Note**: The nginx service now builds a custom container with template support, so it requires building rather than pulling a pre-built image.

2.  **Start Services**:
    *   `docker-compose up` (starts all services from `docker-compose.yml` in the foreground)
    *   `docker-compose up -d` (starts in detached mode)
    *   `docker-compose -f docker-compose.dev.yml up` (for development)

3.  **Stop Services**:
    *   `docker-compose down` (stops and removes containers, networks, etc.)
    *   `docker-compose -f docker-compose.dev.yml down`

### Verification Steps

After starting the services, verify the setup:

1. **Check Container Status**:
   ```bash
   docker-compose ps
   ```

2. **Verify Nginx Template Processing**:
   ```bash
   # Check if template was processed correctly
   docker-compose exec nginx cat /etc/nginx/nginx.conf | grep backend
   
   # Should show: server backend:8004; (or your configured port)
   ```

3. **Test WebSocket Connectivity**:
   ```bash
   # Test API endpoint
   curl http://localhost/api/health
   
   # Test frontend
   curl http://localhost/
   ```

4. **Check Logs for Issues**:
   ```bash
   docker-compose logs nginx
   docker-compose logs backend
   ```

## Accessing Scripts

With the `COPY ./scripts/ /app/scripts/` instruction in `dashboard/backend/Dockerfile`:

*   The scripts from your `o:\\Github\\MaxPain\\MaxPain2024\\scripts\\` directory are now part of the backend-related images.
*   You can execute these scripts from within a running backend, celery_worker, or celery_beat container using:
    ```bash
    docker-compose exec backend python /app/scripts/your_script_name.py
    ```
    (Replace `backend` with `celery_worker` or `celery_beat` if appropriate, and `your_script_name.py` with the actual script name).
*   If a service\'s command in `docker-compose.yml` needs to run one of these scripts, it can directly call `python /app/scripts/your_script_name.py`.

This setup provides a robust way to manage your application\'s services, dependencies, and configurations for both development and deployment.

## Troubleshooting

### Common Issues and Solutions

#### 1. Nginx Template Not Processing Environment Variables

**Symptoms**: 
- nginx.conf contains literal `${BACKEND_PORT}` text
- 502 Bad Gateway errors
- Backend connection failures

**Solution**:
```bash
# Rebuild nginx container
docker-compose build --no-cache nginx
docker-compose up -d nginx

# Verify envsubst is working
docker-compose exec nginx envsubst --version
```

#### 2. WebSocket Connection Failures

**Symptoms**:
- Frontend shows "WebSocket connection failed"
- Browser console: "WebSocket connection to 'ws://localhost/ws' failed"

**Debugging Steps**:
```bash
# Check nginx configuration
docker-compose exec nginx cat /etc/nginx/nginx.conf | grep -A 10 "location /ws"

# Test WebSocket endpoint directly
curl -i -N -H "Connection: Upgrade" -H "Upgrade: websocket" \
     -H "Sec-WebSocket-Version: 13" -H "Sec-WebSocket-Key: test" \
     http://localhost:8004/ws

# Check backend logs for WebSocket errors
docker-compose logs backend | grep -i websocket
```

#### 3. Backend Port Configuration Issues

**Symptoms**:
- Backend not accessible on expected port
- nginx upstream connection failures

**Solution**:
```bash
# Verify .env file configuration
cat .env | grep BACKEND_PORT

# Check if backend is using correct port
docker-compose exec backend netstat -tlnp | grep :8004

# Restart services with new configuration
docker-compose down
docker-compose up -d
```

#### 4. Build Context Issues

**Symptoms**:
- "COPY failed: file not found" errors during build
- Missing scripts or application files in containers

**Solution**:
```bash
# Ensure you're running from project root
cd o:\\Github\\MaxPain\\MaxPain2024\\

# Clean build (removes cache)
docker-compose build --no-cache

# Verify build context
docker-compose config
```

#### 5. Volume Mount Issues (Windows)

**Symptoms**:
- Files not persisting between container restarts
- Permission denied errors

**Solution**:
```bash
# Ensure Docker Desktop has access to the drive
# Go to Docker Desktop Settings > Resources > File Sharing

# Check volume mounts
docker-compose exec backend ls -la /app/logs
docker volume ls
```

### Development vs Production Issues

#### Environment File Conflicts
Make sure you're using the correct compose file and environment:

```bash
# Development
docker-compose -f docker-compose.dev.yml up

# Production  
docker-compose up
```

#### Port Conflicts
If ports are already in use:

```bash
# Check what's using the port
netstat -ano | findstr :80
netstat -ano | findstr :8004

# Change ports in .env file
echo "BACKEND_PORT=8005" >> .env
```

### Logs and Debugging

#### Access Container Logs
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f nginx
docker-compose logs -f backend

# Last 100 lines
docker-compose logs --tail=100 backend
```

#### Enter Containers for Debugging
```bash
# Backend container
docker-compose exec backend bash

# Nginx container
docker-compose exec nginx sh

# Check running processes
docker-compose exec backend ps aux
```

#### Network Debugging
```bash
# List networks
docker network ls

# Inspect network
docker network inspect maxpain2024_backend-network

# Test connectivity between containers
docker-compose exec nginx ping backend
docker-compose exec backend ping redis
```
