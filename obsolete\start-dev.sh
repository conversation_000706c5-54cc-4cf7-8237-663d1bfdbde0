#!/bin/bash

# HKEX Dashboard Development Startup Script

echo "Starting HKEX Dashboard in Development Mode..."

# Check if .env file exists
if [ ! -f .env ]; then
    echo "Creating .env file from .env.example..."
    cp .env.example .env
    echo "Please update .env file with your configuration before running again."
    exit 1
fi

# Create logs directory
mkdir -p logs

# Start development environment
echo "Starting development containers..."
docker-compose -f docker-compose.dev.yml up --build

echo "Development environment started!"
echo "Frontend: http://localhost:3000"
echo "Backend API: http://localhost:8000"
echo "API Docs: http://localhost:8000/docs"
