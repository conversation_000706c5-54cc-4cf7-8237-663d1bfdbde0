"""
Proxy Crawler Alternatives for HKEX Data Fetching

This module provides alternative methods to fetch HKEX data when direct access fails,
including proxy services, browser automation, and cloud-based solutions.
"""

import requests
import time
import random
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import os
from dotenv import load_dotenv

load_dotenv()

# ================================================================================
# Method 1: Rotating Proxy Services
# ================================================================================

def get_proxy_session():
    """Create a session with rotating proxy support"""
    session = requests.Session()
    
    # Example proxy services (you'll need to sign up and get credentials)
    proxy_services = [
        # ProxyMesh (paid service)
        # "http://username:<EMAIL>:31280",
        
        # Bright Data (formerly Luminati) - premium service
        # "http://username:<EMAIL>:22225",
        
        # ScrapingBee API (cloud-based)
        # Uses API instead of traditional proxy
        
        # Free proxy lists (unreliable, use with caution)
        # You can integrate with free proxy APIs like:
        # - proxy-list.download
        # - free-proxy-list.net
    ]
    
    # For demonstration, using a simple proxy rotation
    # In production, you'd want to use a paid service
    return session

def fetch_with_scrapingbee(url):
    """
    Use ScrapingBee API to fetch content (cloud-based solution)
    Sign up at: https://www.scrapingbee.com/
    """
    api_key = os.getenv('SCRAPINGBEE_API_KEY')  # Set this in your .env file
    if not api_key:
        print("ScrapingBee API key not found. Please set SCRAPINGBEE_API_KEY in .env file")
        return None
    
    api_url = "https://app.scrapingbee.com/api/v1/"
    params = {
        'api_key': api_key,
        'url': url,
        'render_js': 'false',  # Set to 'true' if JavaScript rendering is needed
        'premium_proxy': 'true',  # Use premium proxies
        'country_code': 'hk',  # Use Hong Kong proxies for better access
    }
    
    try:
        response = requests.get(api_url, params=params, timeout=60)
        if response.status_code == 200:
            print(f"✓ Successfully fetched via ScrapingBee: {url}")
            return response
        else:
            print(f"✗ ScrapingBee failed: HTTP {response.status_code}")
            return None
    except Exception as e:
        print(f"✗ ScrapingBee error: {e}")
        return None

def fetch_with_scraperapi(url):
    """
    Use ScraperAPI to fetch content (another cloud-based solution)
    Sign up at: https://www.scraperapi.com/
    """
    api_key = os.getenv('SCRAPERAPI_KEY')  # Set this in your .env file
    if not api_key:
        print("ScraperAPI key not found. Please set SCRAPERAPI_KEY in .env file")
        return None
    
    api_url = "http://api.scraperapi.com"
    params = {
        'api_key': api_key,
        'url': url,
        'country_code': 'hk',  # Use Hong Kong proxies
        'render': 'false',  # Set to 'true' if JavaScript rendering is needed
        'premium': 'true',  # Use premium proxies
    }
    
    try:
        response = requests.get(api_url, params=params, timeout=60)
        if response.status_code == 200:
            print(f"✓ Successfully fetched via ScraperAPI: {url}")
            return response
        else:
            print(f"✗ ScraperAPI failed: HTTP {response.status_code}")
            return None
    except Exception as e:
        print(f"✗ ScraperAPI error: {e}")
        return None

# ================================================================================
# Method 2: Browser Automation (Selenium)
# ================================================================================

def create_stealth_browser():
    """Create a stealth browser instance that's harder to detect"""
    chrome_options = Options()
    
    # Stealth options
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    # User agent rotation
    user_agents = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ]
    chrome_options.add_argument(f"--user-agent={random.choice(user_agents)}")
    
    # Optional: Run in headless mode
    # chrome_options.add_argument("--headless")
    
    try:
        driver = webdriver.Chrome(options=chrome_options)
        
        # Execute script to remove webdriver property
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        return driver
    except Exception as e:
        print(f"Failed to create browser: {e}")
        return None

def fetch_with_selenium(url, max_wait=30):
    """Fetch content using Selenium browser automation"""
    driver = create_stealth_browser()
    if not driver:
        return None
    
    try:
        print(f"Fetching with Selenium: {url}")
        
        # Add random delay before request
        time.sleep(random.uniform(2, 5))
        
        driver.get(url)
        
        # Wait for page to load
        WebDriverWait(driver, max_wait).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        
        # Additional wait to ensure content is fully loaded
        time.sleep(random.uniform(3, 7))
        
        # Get page source
        content = driver.page_source
        
        print(f"✓ Successfully fetched via Selenium: {url}")
        print(f"Content length: {len(content)} characters")
        
        # Create a mock response object
        class MockResponse:
            def __init__(self, content):
                self.content = content.encode('utf-8')
                self.status_code = 200
                self.text = content
        
        return MockResponse(content)
        
    except Exception as e:
        print(f"✗ Selenium fetch failed: {e}")
        return None
    finally:
        if driver:
            driver.quit()

# ================================================================================
# Method 3: VPN/Proxy Integration
# ================================================================================

def fetch_with_tor_proxy(url):
    """
    Fetch content through Tor network (requires Tor to be running)
    Install Tor Browser or Tor service first
    """
    session = requests.Session()
    
    # Tor proxy configuration
    session.proxies = {
        'http': 'socks5://127.0.0.1:9050',
        'https': 'socks5://127.0.0.1:9050'
    }
    
    try:
        print(f"Fetching via Tor: {url}")
        response = session.get(url, timeout=60)
        if response.status_code == 200:
            print(f"✓ Successfully fetched via Tor: {url}")
            return response
        else:
            print(f"✗ Tor fetch failed: HTTP {response.status_code}")
            return None
    except Exception as e:
        print(f"✗ Tor fetch error: {e}")
        return None

# ================================================================================
# Method 4: Cloud Function Approach
# ================================================================================

def deploy_cloud_scraper():
    """
    Instructions for deploying a cloud-based scraper
    This would run on AWS Lambda, Google Cloud Functions, or Azure Functions
    """
    cloud_function_code = '''
    # Example AWS Lambda function
    import json
    import requests
    from bs4 import BeautifulSoup

    def lambda_handler(event, context):
        url = event.get('url')
        if not url:
            return {'statusCode': 400, 'body': 'URL required'}
        
        try:
            response = requests.get(url, timeout=30)
            if response.status_code == 200:
                return {
                    'statusCode': 200,
                    'body': response.text,
                    'headers': {'Content-Type': 'text/html'}
                }
            else:
                return {'statusCode': response.status_code, 'body': 'Failed to fetch'}
        except Exception as e:
            return {'statusCode': 500, 'body': str(e)}
    '''
    
    print("To deploy a cloud scraper:")
    print("1. Create an AWS Lambda function with the above code")
    print("2. Set up API Gateway to trigger the function")
    print("3. Call the API endpoint from your script")
    print("4. This approach uses different IP addresses and is harder to block")

# ================================================================================
# Unified Fetcher with Fallback Methods
# ================================================================================

def fetch_with_fallback(url, methods=['direct', 'selenium', 'scrapingbee']):
    """
    Try multiple methods to fetch content with fallback
    
    Args:
        url: URL to fetch
        methods: List of methods to try in order
    
    Returns:
        Response object or None
    """
    for method in methods:
        print(f"\nTrying method: {method}")
        
        try:
            if method == 'direct':
                # Use the enhanced direct method from main script
                from UpdateStockOptionReportPostgres import safe_http_get
                response = safe_http_get(url, timeout=90, max_retries=2)
                if response and response.status_code == 200:
                    return response
                    
            elif method == 'selenium':
                response = fetch_with_selenium(url)
                if response and response.status_code == 200:
                    return response
                    
            elif method == 'scrapingbee':
                response = fetch_with_scrapingbee(url)
                if response and response.status_code == 200:
                    return response
                    
            elif method == 'scraperapi':
                response = fetch_with_scraperapi(url)
                if response and response.status_code == 200:
                    return response
                    
            elif method == 'tor':
                response = fetch_with_tor_proxy(url)
                if response and response.status_code == 200:
                    return response
                    
        except Exception as e:
            print(f"Method {method} failed: {e}")
            continue
    
    print("All methods failed")
    return None

if __name__ == "__main__":
    # Example usage
    test_url = "https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/dqe250616.htm"
    
    print("Testing proxy crawler alternatives...")
    response = fetch_with_fallback(test_url)
    
    if response:
        print(f"✓ Successfully fetched content ({len(response.content)} bytes)")
    else:
        print("✗ All methods failed")
