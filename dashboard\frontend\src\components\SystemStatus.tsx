import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  CardContent,
  Typo<PERSON>,
  Box,
  Grid,
  Chip,
  LinearProgress,
  <PERSON>ert,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Memory,
  Storage,
  Speed,
  CloudQueue,
  Dataset,
  Refresh,
  CheckCircle,
  Warning,
  Error
} from '@mui/icons-material';

interface SystemMetrics {
  cpu_usage: number;
  memory_usage: number;
  disk_usage: number;
  active_connections: number;
  database_status: 'healthy' | 'warning' | 'error';
  queue_size: number;
  last_updated: string;
}

interface SystemStatusProps {
  onRefresh?: () => void;
}

const SystemStatus: React.FC<SystemStatusProps> = ({ onRefresh }) => {
  const [metrics, setMetrics] = useState<SystemMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchMetrics = async () => {
    try {
      setLoading(true);
      // Simulate API call - replace with actual endpoint when available
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock data for now
      const mockMetrics: SystemMetrics = {
        cpu_usage: Math.random() * 100,
        memory_usage: 60 + Math.random() * 20,
        disk_usage: 45 + Math.random() * 15,
        active_connections: Math.floor(Math.random() * 50) + 10,
        database_status: Math.random() > 0.8 ? 'warning' : 'healthy',
        queue_size: Math.floor(Math.random() * 10),
        last_updated: new Date().toISOString()
      };
      
      setMetrics(mockMetrics);
      setError(null);
    } catch (err) {
      setError('Failed to fetch system metrics');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMetrics();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchMetrics, 30000);
    return () => clearInterval(interval);
  }, []);

  const handleRefresh = () => {
    fetchMetrics();
    onRefresh?.();
  };

  const getUsageColor = (usage: number) => {
    if (usage < 50) return 'success';
    if (usage < 80) return 'warning';
    return 'error';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle color="success" />;
      case 'warning':
        return <Warning color="warning" />;
      case 'error':
        return <Error color="error" />;
      default:
        return <CheckCircle color="disabled" />;
    }
  };

  if (loading && !metrics) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            System Status
          </Typography>
          <LinearProgress />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">System Status</Typography>
            <IconButton onClick={handleRefresh} size="small">
              <Refresh />
            </IconButton>
          </Box>
          <Alert severity="error">{error}</Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h6">System Status</Typography>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Typography variant="caption" color="text.secondary" sx={{ mr: 1 }}>
              Last updated: {metrics ? new Date(metrics.last_updated).toLocaleTimeString() : 'Never'}
            </Typography>
            <IconButton onClick={handleRefresh} size="small">
              <Refresh />
            </IconButton>
          </Box>
        </Box>

        {metrics && (
          <Grid container spacing={3}>
            {/* CPU Usage */}
            <Grid item xs={12} sm={6} md={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Speed sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
                <Typography variant="h6" gutterBottom>
                  CPU Usage
                </Typography>
                <Typography variant="h4" sx={{ mb: 1 }}>
                  {Math.round(metrics.cpu_usage)}%
                </Typography>
                <LinearProgress 
                  variant="determinate" 
                  value={metrics.cpu_usage} 
                  color={getUsageColor(metrics.cpu_usage)}
                  sx={{ height: 8, borderRadius: 4 }}
                />
              </Box>
            </Grid>

            {/* Memory Usage */}
            <Grid item xs={12} sm={6} md={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Memory sx={{ fontSize: 40, color: 'secondary.main', mb: 1 }} />
                <Typography variant="h6" gutterBottom>
                  Memory Usage
                </Typography>
                <Typography variant="h4" sx={{ mb: 1 }}>
                  {Math.round(metrics.memory_usage)}%
                </Typography>
                <LinearProgress 
                  variant="determinate" 
                  value={metrics.memory_usage} 
                  color={getUsageColor(metrics.memory_usage)}
                  sx={{ height: 8, borderRadius: 4 }}
                />
              </Box>
            </Grid>

            {/* Disk Usage */}
            <Grid item xs={12} sm={6} md={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Storage sx={{ fontSize: 40, color: 'info.main', mb: 1 }} />
                <Typography variant="h6" gutterBottom>
                  Disk Usage
                </Typography>
                <Typography variant="h4" sx={{ mb: 1 }}>
                  {Math.round(metrics.disk_usage)}%
                </Typography>
                <LinearProgress 
                  variant="determinate" 
                  value={metrics.disk_usage} 
                  color={getUsageColor(metrics.disk_usage)}
                  sx={{ height: 8, borderRadius: 4 }}
                />
              </Box>
            </Grid>

            {/* Database Status */}
            <Grid item xs={12} sm={6} md={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Dataset sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
                <Typography variant="h6" gutterBottom>
                  Database
                </Typography>
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', mb: 2 }}>
                  {getStatusIcon(metrics.database_status)}
                  <Typography variant="h6" sx={{ ml: 1 }}>
                    {metrics.database_status}
                  </Typography>
                </Box>
                <Chip 
                  label={`${metrics.active_connections} connections`}
                  size="small"
                  color="primary"
                  variant="outlined"
                />
              </Box>
            </Grid>

            {/* Additional Metrics */}
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, flexWrap: 'wrap' }}>
                <Tooltip title="Number of items in processing queue">
                  <Chip
                    icon={<CloudQueue />}
                    label={`Queue: ${metrics.queue_size}`}
                    color={metrics.queue_size > 5 ? 'warning' : 'default'}
                    variant="outlined"
                  />
                </Tooltip>
                
                <Tooltip title="System load indicator">
                  <Chip
                    label={`Load: ${(metrics.cpu_usage / 100 * 4).toFixed(2)}`}
                    color={metrics.cpu_usage > 80 ? 'error' : 'default'}
                    variant="outlined"
                  />
                </Tooltip>
                
                <Tooltip title="Active database connections">
                  <Chip
                    label={`DB Connections: ${metrics.active_connections}`}
                    color={metrics.active_connections > 40 ? 'warning' : 'default'}
                    variant="outlined"
                  />
                </Tooltip>
              </Box>
            </Grid>
          </Grid>
        )}
      </CardContent>
    </Card>
  );
};

export default SystemStatus;
