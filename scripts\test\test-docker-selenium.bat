@echo off
echo ========================================
echo   Testing Selenium in Docker Container
echo ========================================
echo.

echo Testing WebSocket imports...
docker exec -it hkex_backend_dev python -c "
try:
    from websocket import WebSocketApp
    print('✅ WebSocketApp import: SUCCESS')
except ImportError as e:
    print('❌ WebSocketApp import: FAILED -', e)

try:
    from selenium import webdriver
    print('✅ Selenium import: SUCCESS')
except ImportError as e:
    print('❌ Selenium import: FAILED -', e)
"

echo.
echo Testing HKEX fetcher...
docker exec -it hkex_backend_dev python -c "
from scripts.hkex_fetcher import selenium_http_get
print('Testing Selenium HTTP GET...')
response = selenium_http_get('https://httpbin.org/html', timeout=30)
if response and response.status_code == 200:
    print('✅ Selenium HTTP GET: SUCCESS - Status', response.status_code)
else:
    print('❌ Selenium HTTP GET: FAILED - Status', response.status_code if response else 'No response')
"

echo.
echo Testing HKEX fallback chain...
docker exec -it hkex_backend_dev python -c "
from scripts.hkex_fetcher import safe_http_get_with_firecrawl_fallback
print('Testing HKEX fallback chain...')
response = safe_http_get_with_firecrawl_fallback('https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/hsio250630.htm', timeout=60)
if response and response.status_code == 200:
    print('✅ HKEX Fallback Chain: SUCCESS - Fetched', len(response.content), 'bytes')
else:
    print('❌ HKEX Fallback Chain: FAILED - Status', response.status_code if response else 'No response')
"

echo.
echo ========================================
echo   Test Complete!
echo ========================================
