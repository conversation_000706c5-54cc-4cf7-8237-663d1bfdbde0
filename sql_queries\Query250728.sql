WITH RankedStrikes AS (
    -- First, we create a temporary result set (CTE) called RankedStrikes.
    -- In this CTE, we calculate a rank for each strike based on our two criteria.
    SELECT
        symb,
        cmonth,
        txn_date,
        stock_price,
        strike,
        -- The ROW_NUMBER() window function assigns a unique, sequential integer to rows within a partition.
        -- We partition by symbol, contract month, and date, so the ranking restarts for each group.
        
        -- Rank 1: Based on the difference between call and put open interest (coi - poi), in descending order.
        -- The strike with the highest difference gets rank 1.
        ROW_NUMBER() OVER(PARTITION BY symb, cmonth, txn_date ORDER BY (coi - poi) DESC) as rn_coi_poi_diff,
        
        -- Rank 2: Based on the total net gamma. The sample data already has a 'net_gamma' column
        -- which is the sum of 'cnetgamma' and 'pnetgamma'. We order by this in descending order.
        -- The strike with the highest net_gamma gets rank 1.
        ROW_NUMBER() OVER(PARTITION BY symb, cmonth, txn_date ORDER BY net_gamma DESC) as rn_net_gamma
    FROM
        v_stock_option_value
)
-- Now, we select from our CTE to produce the final report.
SELECT
    symb,
    cmonth,
    txn_date,
    max(stock_price),
    -- We use conditional aggregation to pivot the results.
    -- For each group, this finds the 'strike' value from the row where the coi-poi rank is 1.
    MAX(CASE WHEN rn_coi_poi_diff = 1 THEN strike END) AS strike_max_coi_poi_diff,
    
    -- Similarly, this finds the 'strike' value from the row where the net gamma rank is 1.
    MAX(CASE WHEN rn_net_gamma = 1 THEN strike END) AS strike_max_net_gamma
FROM
    RankedStrikes
WHERE
    -- We only need to process rows that are ranked #1 in at least one of the categories.
    -- This pre-filters the data before the final aggregation, improving efficiency.
    rn_coi_poi_diff = 1 OR rn_net_gamma = 1
GROUP BY
    -- The final result is grouped to get one row per symbol, contract month, and date.
    symb,
    cmonth,
    txn_date
ORDER BY
    -- Ordering the final result for readability.
    symb,
    cmonth,
    txn_date DESC;


WITH support_resistance AS (
  SELECT strike, SUM(coi - poi) as total_oi, 
         SUM(coi_option_value + poi_option_value) as total_value
  FROM v_stock_option_value_latest
  WHERE txn_date = '2025-07-25'
	and symb = 'TCH'
	and cmonth='30JUL25'
  GROUP BY 1
)
SELECT * FROM support_resistance 
ORDER BY total_oi DESC LIMIT 10;





SELECT *, (coi - poi) as total_oi, 
         (coi_option_value + poi_option_value) as total_value
  FROM v_stock_option_value_latest
  WHERE txn_date = '2025-07-25'
	and symb = 'TCH'
	and cmonth='30JUL25'

"30JUL25"	"TCH"	"2025-07-25"	550.500000	350.000000	370	1.000000	0.000000	200.58	0.000000	370.000000	-539	0.000000	0.000000	0.01	0.010000	0.140000	0.010000	370.140000	0	0	37000	20368500	7421460	0	0	-539	909	7420921
"30JUL25"	"TCH"	"2025-07-25"	550.500000	360.000000	210	1.000000	0.000000	190.58	0.000000	210.000000	-332	0.000000	0.000000	0.01	0.000000	0.090000	0.000000	210.090000	0	0	21000	11560500	4002180	0	0	-332	542	4001848
"30JUL25"	"TCH"	"2025-07-25"	550.500000	370.000000	260	1.000000	0.000000	180.59	0.000000	259.970000	-870	0.000000	0.000000	0.01	0.010000	0.240000	0.020000	260.200000	0	0	26000	14313000	4695340	0	0	-870	1130	4694470
"30JUL25"	"TCH"	"2025-07-25"	550.500000	380.000000	32	1.000000	0.000000	170.59	0.000000	32.000000	-2831	0.000000	0.000000	0.01	0.050000	0.790000	0.050000	32.790000	0	0	3200	1761600	545888	0	0	-2831	2863	543057
"30JUL25"	"TCH"	"2025-07-25"	550.500000	390.000000	30	1.000000	0.000000	160.59	0.000000	30.000000	-988	0.000000	0.000000	0.01	0.020000	0.320000	0.020000	30.320000	0	0	3000	1651500	481770	0	0	-988	1018	480782
"30JUL25"	"TCH"	"2025-07-25"	550.500000	400.000000	137	1.000000	0.000000	150.59	0.000000	137.000000	-2564	0.000000	0.000000	0.01	0.060000	0.810000	0.060000	137.810000	0	0	13700	7541850	2063083	0	0	-2564	2701	2060519
"30JUL25"	"TCH"	"2025-07-25"	550.500000	410.000000	90	1.000000	0.000000	140.59	0.000000	90.000000	-2828	0.000000	0.000000	0.01	0.080000	1.000000	0.080000	91.000000	0	0	9000	4954500	1265310	0	0	-2828	2918	1262482
"30JUL25"	"TCH"	"2025-07-25"	550.500000	420.000000	30	1.000000	0.000000	130.6	0.000000	30.000000	-7121	0.000000	0.000000	0.01	0.220000	2.790000	0.230000	32.790000	0	0	3000	1651500	391800	0	0	-7121	7151	384679
"30JUL25"	"TCH"	"2025-07-25"	550.500000	430.000000	591	1.000000	0.000000	120.6	0.000000	590.980000	-6843	0.000000	0.000000	0.01	0.260000	2.930000	0.260000	593.910000	0	0	59100	32534550	7127460	0	0	-6843	7434	7120617
"30JUL25"	"TCH"	"2025-07-25"	550.500000	440.000000	1283	1.000000	0.000000	110.6	0.000000	1283.000000	-5582	0.000000	0.000000	0.01	0.240000	2.570000	0.240000	1285.570000	0	0	128300	70629150	14189980	0	0	-5582	6865	14184398
"30JUL25"	"TCH"	"2025-07-25"	550.500000	450.000000	1500	1.000000	0.000000	100.6	0.000000	1500.000000	-11693	0.000000	0.000000	0.01	0.600000	5.710000	0.600000	1505.710000	-200	0	150000	82575000	15090000	0	0	-11693	13193	15078307
"30JUL25"	"TCH"	"2025-07-25"	550.500000	460.000000	2594	1.000000	0.000000	90.61	0.080000	2593.380000	-13953	0.000000	0.000000	0.01	1.000000	8.830000	1.080000	2602.220000	0	10	259400	142799700	23504234	0	0	-13953	16547	23490281
"30JUL25"	"TCH"	"2025-07-25"	550.500000	470.000000	1493	1.000000	0.000000	80.61	0.030000	1492.840000	-6435	0.000000	0.000000	0.01	0.540000	4.230000	0.570000	1497.070000	-1	67	149300	82189650	12035073	0	0	-6435	7928	12028638
"30JUL25"	"TCH"	"2025-07-25"	550.500000	480.000000	6955	1.000000	0.000000	70.61	0.000000	6955.000000	-22226	0.000000	0.000000	0.01	2.170000	14.760000	2.170000	6969.760000	-2	-3	695500	382872750	49109255	0	0	-22226	29181	49087029
"30JUL25"	"TCH"	"2025-07-25"	550.500000	490.000000	2630	1.000000	0.000000	60.62	0.290000	2628.310000	-26844	0.000000	0.000000	0.02	6.240000	42.070000	6.540000	2670.380000	-5	45	263000	144781500	15943060	0	0	-53688	29474	15889372
"30JUL25"	"TCH"	"2025-07-25"	550.500000	500.000000	10415	1.000000	0.000000	50.65	4.930000	10383.580000	-22511	0.000000	0.000000	0.04	12.910000	86.950000	17.840000	10470.530000	-65	-3	1041500	573345750	52751975	0	0	-90044	32926	52661931
"30JUL25"	"TCH"	"2025-07-25"	550.500000	510.000000	12831	0.990000	0.000000	40.7	16.060000	12727.800000	-16098	0.010000	0.000000	0.13	26.940000	194.850000	43.000000	12922.650000	-442	5	1270269	699283085	52222170	16098	8861949	-209274	28929	52012896
"30JUL25"	"TCH"	"2025-07-25"	550.500000	520.000000	12125	1.000000	0.000000	30.5	0.000000	12125.000000	-12399	0.040000	0.000000	0.36	55.700000	446.100000	55.700000	12571.100000	-307	-749	1212500	667481250	36981250	49596	27302598	-446364	24524	36534886
"30JUL25"	"TCH"	"2025-07-25"	550.500000	530.000000	18127	0.950000	0.010000	21.03	145.180000	17232.690000	-7938	0.100000	0.010000	1.02	84.100000	781.760000	229.280000	18014.450000	-445	-1517	1722065	947996783	38121081	79380	43698690	-809676	26065	37311405
"30JUL25"	"TCH"	"2025-07-25"	550.500000	540.000000	29980	0.770000	0.020000	12.9	624.060000	23107.030000	-10250	0.240000	0.020000	2.6	209.130000	2439.530000	833.190000	25546.560000	-2167	-3006	2308460	1270807230	38674200	246000	135423000	-2665000	40230	36009200
"30JUL25"	"TCH"	"2025-07-25"	550.500000	550.000000	14017	0.520000	0.030000	6.45	400.270000	7279.730000	-2922	0.480000	0.030000	5.85	83.440000	1404.460000	483.710000	8684.190000	-788	-885	728884	401250642	9040965	140256	77210928	-1709370	16939	7331595
"30JUL25"	"TCH"	"2025-07-25"	550.500000	560.000000	15120	0.290000	0.020000	3.48	314.780000	4349.980000	-3023	0.710000	0.020000	12.8	62.930000	2153.290000	377.710000	6503.270000	233	-120	438480	241383240	5261760	214633	118155467	-3869440	18143	1392320
"30JUL25"	"TCH"	"2025-07-25"	550.500000	570.000000	10114	0.160000	0.010000	1.96	129.890000	1600.250000	-1029	0.810000	0.010000	22.06	13.090000	834.950000	142.980000	2435.200000	-638	-109	161824	89084112	1982344	83349	45883625	-2269974	11143	-287630
"30JUL25"	"TCH"	"2025-07-25"	550.500000	580.000000	4729	0.090000	0.010000	1.17	36.490000	430.800000	-130	0.880000	0.010000	31.32	1.090000	114.060000	37.580000	544.860000	-34	0	42561	23429831	553293	11440	6297720	-407160	4859	146133
"30JUL25"	"TCH"	"2025-07-25"	550.500000	600.000000	7747	0.030000	0.000000	0.51	22.440000	256.440000	-211	0.900000	0.000000	51.43	1.020000	190.270000	23.460000	446.720000	25	0	23241	12794171	395097	18990	10453995	-1085173	7958	-690076
"30JUL25"	"TCH"	"2025-07-25"	550.500000	610.000000	1429	0.020000	0.000000	0.37	2.770000	32.580000	-120	0.910000	0.000000	61.59	0.480000	109.030000	3.250000	141.610000	0	0	2858	1573329	52873	10920	6011460	-739080	1549	-686207
"30JUL25"	"TCH"	"2025-07-25"	550.500000	620.000000	1896	0.020000	0.000000	0.28	2.580000	31.440000	-60	0.910000	0.000000	71.79	0.200000	54.770000	2.780000	86.210000	0	0	3792	2087496	53088	5460	3005730	-430740	1956	-377652



WITH RankedStrikes AS (
    -- First, we create a temporary result set (CTE) called RankedStrikes.
    -- In this CTE, we calculate a rank for each strike based on our two criteria.
    SELECT
        symb,
        cmonth,
        txn_date,
        stock_price,
        strike,
        -- The ROW_NUMBER() window function assigns a unique, sequential integer to rows within a partition.
        -- We partition by symbol, contract month, and date, so the ranking restarts for each group.
        
        -- Rank 1: Based on the difference between call and put open interest (coi - poi), in descending order.
        -- The strike with the highest difference gets rank 1.
        ROW_NUMBER() OVER(PARTITION BY symb, cmonth, txn_date ORDER BY (coi - poi) DESC) as rn_coi_poi_diff,
		ROW_NUMBER() OVER(PARTITION BY symb, cmonth, txn_date ORDER BY coi  DESC) as rn_coi,
		ROW_NUMBER() OVER(PARTITION BY symb, cmonth, txn_date ORDER BY -poi DESC) as rn_poi,        
        -- Rank 2: Based on the total net gamma. The sample data already has a 'net_gamma' column
        -- which is the sum of 'cnetgamma' and 'pnetgamma'. We order by this in descending order.
        -- The strike with the highest net_gamma gets rank 1.
        ROW_NUMBER() OVER(PARTITION BY symb, cmonth, txn_date ORDER BY net_gamma DESC) as rn_net_gamma
    FROM
        v_stock_option_value
	where  txn_date >= '2025-01-01'
	and symb = 'TCH'
	and cmonth='30JUL25'
)
-- Now, we select from our CTE to produce the final report.
SELECT
    symb,
    cmonth,
    txn_date,
    max(stock_price),
    -- We use conditional aggregation to pivot the results.
    -- For each group, this finds the 'strike' value from the row where the coi-poi rank is 1.
    MAX(CASE WHEN rn_coi_poi_diff = 1 THEN strike END) AS strike_max_coi_poi_diff,
	MAX(CASE WHEN rn_coi = 1 THEN strike END) AS strike_max_coi,
	MAX(CASE WHEN rn_poi = 1 THEN strike END) AS strike_max_poi,    
    -- Similarly, this finds the 'strike' value from the row where the net gamma rank is 1.
    MAX(CASE WHEN rn_net_gamma = 1 THEN strike END) AS strike_max_net_gamma
FROM
    RankedStrikes
WHERE
    -- We only need to process rows that are ranked #1 in at least one of the categories.
    -- This pre-filters the data before the final aggregation, improving efficiency.
    rn_coi_poi_diff = 1 OR rn_net_gamma = 1 OR rn_coi = 1 OR rn_poi = 1
GROUP BY
    -- The final result is grouped to get one row per symbol, contract month, and date.
    symb,
    cmonth,
    txn_date
ORDER BY
    -- Ordering the final result for readability.
    symb,
    cmonth,
    txn_date DESC;

"symb"	"cmonth"	"txn_date"	"max"	"strike_max_coi_poi_diff"	"strike_max_coi"	"strike_max_poi"	"strike_max_net_gamma"
"TCH"	"30JUL25"	"2025-07-28"	555.50	"540.00"	"540.00"	"490.00"	"540.00"
"TCH"	"30JUL25"	"2025-07-25"	550.50	"540.00"	"540.00"	"490.00"	"540.00"
"TCH"	"30JUL25"	"2025-07-24"	557.00	"540.00"	"540.00"	"490.00"	"540.00"
"TCH"	"30JUL25"	"2025-07-23"	552.00	"540.00"	"540.00"	"490.00"	"540.00"
"TCH"	"30JUL25"	"2025-07-22"	526.00	"540.00"	"540.00"	"490.00"	"540.00"
"TCH"	"30JUL25"	"2025-07-21"	521.50	"540.00"	"540.00"	"490.00"	"510.00"
"TCH"	"30JUL25"	"2025-07-18"	519.00	"540.00"	"540.00"	"490.00"	"510.00"
"TCH"	"30JUL25"	"2025-07-17"	517.00	"540.00"	"540.00"	"490.00"	"510.00"
"TCH"	"30JUL25"	"2025-07-16"	516.50	"540.00"	"540.00"	"490.00"	"510.00"
"TCH"	"30JUL25"	"2025-07-15"	517.50	"490.00"	"540.00"	"490.00"	"510.00"
"TCH"	"30JUL25"	"2025-07-14"	500.00	"490.00"	"540.00"	"490.00"	"500.00"
"TCH"	"30JUL25"	"2025-07-11"	496.60	"490.00"	"540.00"	"490.00"	"490.00"
"TCH"	"30JUL25"	"2025-07-10"	496.60	"490.00"	"540.00"	"490.00"	"490.00"
"TCH"	"30JUL25"	"2025-07-09"	497.60	"490.00"	"540.00"	"490.00"	"490.00"
"TCH"	"30JUL25"	"2025-07-08"	504.50	"540.00"	"540.00"	"490.00"	"500.00"
"TCH"	"30JUL25"	"2025-07-07"	502.00	"490.00"	"540.00"	"490.00"	"500.00"
"TCH"	"30JUL25"	"2025-07-04"	496.80	"490.00"	"540.00"	"490.00"	"490.00"
"TCH"	"30JUL25"	"2025-07-03"	501.00	"540.00"	"540.00"	"490.00"	"490.00"
"TCH"	"30JUL25"	"2025-07-02"	501.50	"540.00"	"540.00"	"490.00"	"490.00"
"TCH"	"30JUL25"	"2025-06-30"	503.00	"540.00"	"540.00"	"490.00"	"490.00"
"TCH"	"30JUL25"	"2025-06-27"	513.00	"540.00"	"540.00"	"490.00"	"540.00"
"TCH"	"30JUL25"	"2025-06-26"	513.00	"540.00"	"540.00"	"480.00"	"540.00"
"TCH"	"30JUL25"	"2025-06-25"	512.50	"540.00"	"540.00"	"480.00"	"540.00"
"TCH"	"30JUL25"	"2025-06-24"	509.50	"480.00"	"540.00"	"480.00"	"540.00"
"TCH"	"30JUL25"	"2025-06-23"	504.00	"480.00"	"540.00"	"480.00"	"480.00"
"TCH"	"30JUL25"	"2025-06-20"	505.50	"540.00"	"540.00"	"480.00"	"480.00"
"TCH"	"30JUL25"	"2025-06-19"	498.00	"540.00"	"540.00"	"480.00"	"480.00"
"TCH"	"30JUL25"	"2025-06-18"	508.00	"540.00"	"540.00"	"480.00"	"540.00"
"TCH"	"30JUL25"	"2025-06-17"	513.50	"540.00"	"540.00"	"480.00"	"540.00"
"TCH"	"30JUL25"	"2025-06-16"	509.50	"540.00"	"540.00"	"480.00"	"540.00"
"TCH"	"30JUL25"	"2025-06-13"	510.00	"540.00"	"540.00"	"480.00"	"540.00"
"TCH"	"30JUL25"	"2025-06-12"	510.00	"540.00"	"540.00"	"480.00"	"540.00"
"TCH"	"30JUL25"	"2025-06-11"	518.00	"540.00"	"540.00"	"480.00"	"540.00"
"TCH"	"30JUL25"	"2025-06-10"	513.50	"540.00"	"540.00"	"480.00"	"540.00"
"TCH"	"30JUL25"	"2025-06-09"	518.00	"540.00"	"540.00"	"480.00"	"540.00"
"TCH"	"30JUL25"	"2025-06-06"	515.00	"540.00"	"540.00"	"480.00"	"540.00"
"TCH"	"30JUL25"	"2025-06-05"	515.00	"540.00"	"540.00"	"480.00"	"540.00"
"TCH"	"30JUL25"	"2025-06-04"	512.00	"540.00"	"540.00"	"480.00"	"540.00"
"TCH"	"30JUL25"	"2025-06-03"	505.00	"540.00"	"540.00"	"480.00"	"540.00"
"TCH"	"30JUL25"	"2025-06-02"	498.40	"540.00"	"540.00"	"480.00"	"540.00"
"TCH"	"30JUL25"	"2025-05-30"	498.20	"540.00"	"540.00"	"480.00"	"540.00"
"TCH"	"30JUL25"	"2025-05-29"	510.50	"540.00"	"540.00"	"450.00"	"540.00"
"TCH"	"30JUL25"	"2025-05-28"	506.00	"540.00"	"540.00"	"450.00"	"540.00"
"TCH"	"30JUL25"	"2025-05-27"	512.00	"540.00"	"540.00"	"450.00"	"540.00"
"TCH"	"30JUL25"	"2025-05-26"	510.00	"540.00"	"540.00"	"450.00"	"540.00"
"TCH"	"30JUL25"	"2025-05-23"	518.00	"540.00"	"540.00"	"450.00"	"540.00"
"TCH"	"30JUL25"	"2025-05-22"	516.50	"540.00"	"540.00"	"450.00"	"540.00"
"TCH"	"30JUL25"	"2025-05-21"	520.50	"540.00"	"540.00"	"450.00"	"540.00"
"TCH"	"30JUL25"	"2025-05-20"	517.00	"540.00"	"540.00"	"450.00"	"540.00"
"TCH"	"30JUL25"	"2025-05-19"	514.00	"540.00"	"540.00"	"450.00"	"540.00"
"TCH"	"30JUL25"	"2025-05-16"	508.00	"540.00"	"540.00"	"450.00"	"540.00"
"TCH"	"30JUL25"	"2025-05-15"	520.00	"540.00"	"540.00"	"450.00"	"540.00"
"TCH"	"30JUL25"	"2025-05-14"	521.00	"540.00"	"540.00"	"450.00"	"540.00"
"TCH"	"30JUL25"	"2025-05-13"	506.00	"540.00"	"540.00"	"450.00"	"540.00"
"TCH"	"30JUL25"	"2025-05-12"	517.50	"540.00"	"540.00"	"450.00"	"540.00"
"TCH"	"30JUL25"	"2025-05-09"	494.60	"480.00"	"550.00"	"450.00"	"480.00"
"TCH"	"30JUL25"	"2025-05-08"	498.00	"480.00"	"600.00"	"480.00"	"480.00"
"TCH"	"30JUL25"	"2025-05-07"	489.80	"480.00"	"600.00"	"480.00"	"480.00"
"TCH"	"30JUL25"	"2025-05-06"	495.40	"480.00"	"600.00"	"480.00"	"480.00"
"TCH"	"30JUL25"	"2025-05-02"	487.80	"480.00"	"600.00"	"420.00"	"480.00"

    SELECT
        symb,
        cmonth,
        txn_date,
        stock_price,
        strike,
        -- The ROW_NUMBER() window function assigns a unique, sequential integer to rows within a partition.
        -- We partition by symbol, contract month, and date, so the ranking restarts for each group.
        
        -- Rank 1: Based on the difference between call and put open interest (coi - poi), in descending order.
        -- The strike with the highest difference gets rank 1.
        ROW_NUMBER() OVER(PARTITION BY symb, cmonth, txn_date ORDER BY (coi - poi) DESC) as rn_coi_poi_diff,
		ROW_NUMBER() OVER(PARTITION BY symb, cmonth, txn_date ORDER BY coi  DESC) as rn_coi,
		ROW_NUMBER() OVER(PARTITION BY symb, cmonth, txn_date ORDER BY -poi DESC) as rn_poi,        
        -- Rank 2: Based on the total net gamma. The sample data already has a 'net_gamma' column
        -- which is the sum of 'cnetgamma' and 'pnetgamma'. We order by this in descending order.
        -- The strike with the highest net_gamma gets rank 1.
        ROW_NUMBER() OVER(PARTITION BY symb, cmonth, txn_date ORDER BY net_gamma DESC) as rn_net_gamma
    FROM
        v_stock_option_value
	where  txn_date >= '2025-01-01'
	and symb = 'TCH'
	and cmonth='30JUL25'

"symb"	"cmonth"	"txn_date"	"stock_price"	"strike"	"rn_coi_poi_diff"	"rn_coi"	"rn_poi"	"rn_net_gamma"
"TCH"	"30JUL25"	"2025-07-28"	555.50	"540.00"	1	1	8	1
"TCH"	"30JUL25"	"2025-07-28"	555.50	"500.00"	2	8	2	12
"TCH"	"30JUL25"	"2025-07-28"	555.50	"490.00"	3	13	1	13
"TCH"	"30JUL25"	"2025-07-28"	555.50	"480.00"	4	10	3	11
"TCH"	"30JUL25"	"2025-07-28"	555.50	"510.00"	5	5	4	7
"TCH"	"30JUL25"	"2025-07-28"	555.50	"530.00"	6	2	9	5
"TCH"	"30JUL25"	"2025-07-28"	555.50	"520.00"	7	6	6	6
"TCH"	"30JUL25"	"2025-07-28"	555.50	"560.00"	8	3	15	2
"TCH"	"30JUL25"	"2025-07-28"	555.50	"550.00"	9	4	14	3
"TCH"	"30JUL25"	"2025-07-28"	555.50	"460.00"	10	14	5	14
"TCH"	"30JUL25"	"2025-07-28"	555.50	"450.00"	11	18	7	15
"TCH"	"30JUL25"	"2025-07-28"	555.50	"570.00"	12	7	19	4
"TCH"	"30JUL25"	"2025-07-28"	555.50	"600.00"	13	9	24	10
"TCH"	"30JUL25"	"2025-07-28"	555.50	"470.00"	14	17	12	18
"TCH"	"30JUL25"	"2025-07-28"	555.50	"430.00"	15	21	11	19
"TCH"	"30JUL25"	"2025-07-28"	555.50	"420.00"	16	31	10	21
"TCH"	"30JUL25"	"2025-07-28"	555.50	"440.00"	17	20	13	20
"TCH"	"30JUL25"	"2025-07-28"	555.50	"590.00"	18	11	28	9
"TCH"	"30JUL25"	"2025-07-28"	555.50	"580.00"	19	12	25	8
"TCH"	"30JUL25"	"2025-07-28"	555.50	"410.00"	20	28	17	22
"TCH"	"30JUL25"	"2025-07-28"	555.50	"380.00"	21	29	16	24
"TCH"	"30JUL25"	"2025-07-28"	555.50	"400.00"	22	25	18	23
"TCH"	"30JUL25"	"2025-07-28"	555.50	"620.00"	23	15	27	17
"TCH"	"30JUL25"	"2025-07-28"	555.50	"610.00"	24	19	26	16
"TCH"	"30JUL25"	"2025-07-28"	555.50	"750.00"	25	16	29	25
"TCH"	"30JUL25"	"2025-07-28"	555.50	"370.00"	26	23	21	27
"TCH"	"30JUL25"	"2025-07-28"	555.50	"390.00"	27	30	20	26
"TCH"	"30JUL25"	"2025-07-28"	555.50	"350.00"	28	22	22	29
"TCH"	"30JUL25"	"2025-07-28"	555.50	"360.00"	29	24	23	30
"TCH"	"30JUL25"	"2025-07-28"	555.50	"680.00"	30	26	30	28
"TCH"	"30JUL25"	"2025-07-28"	555.50	"690.00"	31	27	31	31


create or replace  materialized view max_oi_strikes_stock as (
WITH RankedStrikes AS (
    -- First, we create a temporary result set (CTE) called RankedStrikes.
    -- In this CTE, we calculate a rank for each strike based on our two criteria.
    SELECT
        symb,
        cmonth,
        txn_date,
        stock_price,
        strike,
        -- The ROW_NUMBER() window function assigns a unique, sequential integer to rows within a partition.
        -- We partition by symbol, contract month, and date, so the ranking restarts for each group.
        
        -- Rank 1: Based on the difference between call and put open interest (coi - poi), in descending order.
        -- The strike with the highest difference gets rank 1.
        ROW_NUMBER() OVER(PARTITION BY symb, cmonth, txn_date ORDER BY (coi - poi) DESC) as rn_coi_poi_diff,
		ROW_NUMBER() OVER(PARTITION BY symb, cmonth, txn_date ORDER BY coi  DESC) as rn_coi,
		ROW_NUMBER() OVER(PARTITION BY symb, cmonth, txn_date ORDER BY -poi DESC) as rn_poi,        
        -- Rank 2: Based on the total net gamma. The sample data already has a 'net_gamma' column
        -- which is the sum of 'cnetgamma' and 'pnetgamma'. We order by this in descending order.
        -- The strike with the highest net_gamma gets rank 1.
        ROW_NUMBER() OVER(PARTITION BY symb, cmonth, txn_date ORDER BY net_gamma DESC) as rn_net_gamma
    FROM
        v_stock_option_value
	where  txn_date >= '2025-07-25'
	and symb = 'TCH'
	and cmonth='30JUL25'
)
-- Now, we select from our CTE to produce the final report.
SELECT
    symb,
    cmonth,
    txn_date,
    max(stock_price),
    -- We use conditional aggregation to pivot the results.
    -- For each group, this finds the 'strike' value from the row where the coi-poi rank is 1.
    MAX(CASE WHEN rn_coi_poi_diff = 1 THEN strike END) AS strike_max_coi_poi_diff,
	MAX(CASE WHEN rn_coi = 1 THEN strike END) AS strike_max_coi,
	MAX(CASE WHEN rn_poi = 1 THEN strike END) AS strike_max_poi,    
    -- Similarly, this finds the 'strike' value from the row where the net gamma rank is 1.
    MAX(CASE WHEN rn_net_gamma = 1 THEN strike END) AS strike_max_net_gamma
FROM
    RankedStrikes
WHERE
    -- We only need to process rows that are ranked #1 in at least one of the categories.
    -- This pre-filters the data before the final aggregation, improving efficiency.
    rn_coi_poi_diff = 1 OR rn_net_gamma = 1 OR rn_coi = 1 OR rn_poi = 1
GROUP BY
    -- The final result is grouped to get one row per symbol, contract month, and date.
    symb,
    cmonth,
    txn_date
ORDER BY
    -- Ordering the final result for readability.
    symb,
    cmonth,
    txn_date DESC)


drop  materialized view max_oi_strikes_stock;
create materialized view max_oi_strikes_stock as (
WITH RankedStrikes AS (
    -- First, we create a temporary result set (CTE) called RankedStrikes.
    -- In this CTE, we calculate a rank for each strike based on our two criteria.
    SELECT
        symb,
        cmonth,
        txn_date,
        stock_price,
        strike,
        -- The ROW_NUMBER() window function assigns a unique, sequential integer to rows within a partition.
        -- We partition by symbol, contract month, and date, so the ranking restarts for each group.
        
        -- Rank 1: Based on the difference between call and put open interest (coi - poi), in descending order.
        -- The strike with the highest difference gets rank 1.
        ROW_NUMBER() OVER(PARTITION BY symb, cmonth, txn_date ORDER BY (coi - poi) DESC) as rn_coi_poi_diff,
		ROW_NUMBER() OVER(PARTITION BY symb, cmonth, txn_date ORDER BY coi  DESC) as rn_coi,
		ROW_NUMBER() OVER(PARTITION BY symb, cmonth, txn_date ORDER BY -poi DESC) as rn_poi,        
        -- Rank 2: Based on the total net gamma. The sample data already has a 'net_gamma' column
        -- which is the sum of 'cnetgamma' and 'pnetgamma'. We order by this in descending order.
        -- The strike with the highest net_gamma gets rank 1.
        ROW_NUMBER() OVER(PARTITION BY symb, cmonth, txn_date ORDER BY net_gamma DESC) as rn_net_gamma
    FROM
        v_stock_option_value
)
-- Now, we select from our CTE to produce the final report.
SELECT
    symb,
    cmonth,
    txn_date,
    max(stock_price),
    -- We use conditional aggregation to pivot the results.
    -- For each group, this finds the 'strike' value from the row where the coi-poi rank is 1.
    MAX(CASE WHEN rn_coi_poi_diff = 1 THEN strike END) AS strike_max_coi_poi_diff,
	MAX(CASE WHEN rn_coi = 1 THEN strike END) AS strike_max_coi,
	MAX(CASE WHEN rn_poi = 1 THEN strike END) AS strike_max_poi,    
    -- Similarly, this finds the 'strike' value from the row where the net gamma rank is 1.
    MAX(CASE WHEN rn_net_gamma = 1 THEN strike END) AS strike_max_net_gamma
FROM
    RankedStrikes
WHERE
    -- We only need to process rows that are ranked #1 in at least one of the categories.
    -- This pre-filters the data before the final aggregation, improving efficiency.
    rn_coi_poi_diff = 1 OR rn_net_gamma = 1 OR rn_coi = 1 OR rn_poi = 1
GROUP BY
    -- The final result is grouped to get one row per symbol, contract month, and date.
    symb,
    cmonth,
    txn_date
ORDER BY
    -- Ordering the final result for readability.
    symb,
    cmonth,
    txn_date DESC)    