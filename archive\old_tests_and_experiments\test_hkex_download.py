#!/usr/bin/env python3
"""
Test script to verify HKEX securities list download functionality
"""

import os
import sys
import requests
import pandas as pd
from dotenv import load_dotenv
load_dotenv()

def download_hkex_securities_list(url, output_path):
    """
    Download the latest HKEX securities list from the official website.

    Args:
        url (str): URL to download the XLS file from
        output_path (str): Directory path where to save the file

    Returns:
        bool: True if download successful, False otherwise
    """
    try:
        filename = 'ListOfSecurities_c.xlsx'
        file_path = os.path.join(output_path, filename)

        print(f"Downloading HKEX securities list from: {url}")
        print(f"Saving to: {file_path}")

        # Download the file
        response = requests.get(url, timeout=30)
        response.raise_for_status()  # Raise an exception for bad status codes

        # Save the file
        with open(file_path, 'wb') as f:
            f.write(response.content)

        print(f"✓ Successfully downloaded HKEX securities list: {file_path}")
        return True

    except requests.exceptions.RequestException as e:
        print(f"✗ Failed to download HKEX securities list: {e}")
        return False
    except Exception as e:
        print(f"✗ Error saving HKEX securities list: {e}")
        return False

def test_download():
    """Test the HKEX securities list download"""

    # Get output path from environment
    output_path = os.getenv('out_path')
    if not output_path:
        print("✗ out_path environment variable not set")
        return False

    # Fix Windows path issue - convert /output/ to a proper Windows path
    if output_path.startswith('/') and os.name == 'nt':
        # Convert Unix-style path to Windows path relative to current directory
        output_path = output_path.strip('/')
        output_path = os.path.join(os.getcwd(), output_path)

    print(f"Output path: {output_path}")

    # Ensure output directory exists
    os.makedirs(output_path, exist_ok=True)
    
    # HKEX URL
    hkex_url = 'https://www.hkex.com.hk/chi/services/trading/securities/securitieslists/ListOfSecurities_c.xlsx'
    
    # Test the download
    print("Testing HKEX securities list download...")
    success = download_hkex_securities_list(hkex_url, output_path)
    
    if success:
        # Check if file exists and has reasonable size
        file_path = os.path.join(output_path, 'ListOfSecurities_c.xlsx')
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            print(f"✓ File downloaded successfully: {file_path}")
            print(f"✓ File size: {file_size:,} bytes")
            
            # Try to read the file with pandas to verify it's valid
            try:
                import pandas as pd
                df = pd.read_excel(file_path, skiprows=0, header=2)
                print(f"✓ File is valid Excel format with {len(df)} rows")
                print(f"✓ Columns: {list(df.columns)}")
                return True
            except Exception as e:
                print(f"✗ File exists but cannot be read as Excel: {e}")
                return False
        else:
            print(f"✗ File was not created: {file_path}")
            return False
    else:
        print("✗ Download failed")
        return False

if __name__ == "__main__":
    print("HKEX Securities List Download Test")
    print("=" * 40)
    
    success = test_download()
    
    if success:
        print("\n✅ Test PASSED - Download functionality works correctly")
    else:
        print("\n❌ Test FAILED - Download functionality has issues")
        sys.exit(1)
