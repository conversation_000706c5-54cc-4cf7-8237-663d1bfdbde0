services:
  # Development version with hot reload and debug capabilities
  redis:
    image: redis:7-alpine
    container_name: hkex_redis_dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data_dev:/data
    command: redis-server --appendonly yes

  backend:
    build:
      context: . # Changed to use project root like main compose
      dockerfile: ./dashboard/backend/Dockerfile.dev # Use development Dockerfile
    container_name: hkex_backend_dev
    env_file:
      - .env
    environment:
      - DATABASE_URL=*************************************************/storacle
      - REDIS_URL=redis://redis:6379/0
      - ENVIRONMENT=development
      - DEBUG=true
      - PYTHONPATH=/app
      - TZ=Pacific/Auckland
    ports:
      - "${BACKEND_PORT}:${BACKEND_PORT}"
      - "5678:5678"  # Debug port
    volumes:
      - ./dashboard/backend/app:/app # Mount source code to /app for proper imports
      - ./scripts:/app/scripts # Mount scripts for live reload
      - C:\\output\\MaxPain\\logs:/app/logs # Standardized log output
      - ./:/workspace # Mount current directory (project root) to /workspace
      - C:\\output\\MaxPain:/output # Mount for HTML output
    depends_on:
      - redis
    restart: unless-stopped
    command: sh -c "cd /app && uvicorn main:app --host 0.0.0.0 --port ${BACKEND_PORT} --reload"
  celery_worker:
    build:
      context: . # Changed to use project root like main compose
      dockerfile: ./dashboard/backend/Dockerfile # Use main Dockerfile for consistency
    container_name: hkex_celery_worker_dev
    environment:
      - DATABASE_URL=*************************************************/storacle
      - REDIS_URL=redis://redis:6379/0
      - ENVIRONMENT=development
      # - DEBUG=true
      - PYTHONPATH=/app
      - TZ=Pacific/Auckland
    volumes:
      - ./dashboard/backend/app:/app
      - C:\\output\\MaxPain\\logs:/app/logs # Assuming logs is at the root
      - ./:/workspace # Mount current directory (project root) to /workspace
      - C:\\output\\MaxPain:/output # Mount for HTML output
    depends_on:
      - redis
    command: sh -c "cd /app && celery -A tasks.celery_app:celery_app worker --loglevel=debug"
  frontend:
    build:
      context: ./dashboard/frontend
      dockerfile: Dockerfile.dev
    container_name: hkex_frontend_dev
    env_file:
      - .env
    ports:
      - "${FRONTEND_PORT}:3000"
    volumes:
      - ./dashboard/frontend/src:/app/src
      - ./dashboard/frontend/public:/app/public
      - /app/node_modules
    environment:
      - REACT_APP_API_URL=http://backend:${BACKEND_PORT}
      - REACT_APP_WS_URL=ws://localhost:${BACKEND_PORT}/ws
      - REACT_APP_FRONTEND_PORT=${FRONTEND_PORT}
      - REACT_APP_BACKEND_PORT=${BACKEND_PORT}
      - CHOKIDAR_USEPOLLING=true
    depends_on:
      - backend

volumes:
  redis_data_dev:

networks:
  default:
    name: hkex_network_dev
