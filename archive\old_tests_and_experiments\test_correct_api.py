import requests
import json
import time

def test_orchestrator_api():
    """Test starting the HKEX script through the dashboard API"""
    
    base_url = "http://localhost:8000"
    api_base = f"{base_url}/api/v1"
    
    print("=== Testing HKEX Dashboard Orchestrator ===")
    
    # Test 1: Check available process types
    print("\n1. Checking available process types...")
    try:
        response = requests.get(f"{api_base}/processes/types", timeout=5)
        print(f"   Response Status: {response.status_code}")
        if response.status_code == 200:
            types = response.json()
            print(f"   Available process types: {types}")
            print("   [PASS] Process types retrieved")
        else:
            print(f"   [FAIL] Could not get process types: {response.text}")
    except Exception as e:
        print(f"   [FAIL] Process types test failed: {str(e)}")
    
    # Test 2: Start HKEX script
    print("\n2. Testing HKEX script execution...")
    
    script_data = {
        "type": "hkex_update",
        "script_path": "o:\\Github\\MaxPain\\MaxPain2024\\UpdateIndexOptionPostgres.py",
        "args": ["--help"],
        "working_directory": "o:\\Github\\MaxPain\\MaxPain2024"
    }
    
    try:
        response = requests.post(f"{api_base}/processes/start", json=script_data, timeout=10)
        print(f"   Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            process_id = result.get("process_id")
            print(f"   Process ID: {process_id}")
            print("   [PASS] Script started successfully via API")
            
            # Wait a moment for execution
            time.sleep(3)
            
            # Check process status
            print("\n3. Checking process status...")
            status_response = requests.get(f"{api_base}/processes", timeout=5)
            if status_response.status_code == 200:
                processes = status_response.json()
                print(f"   Active processes: {len(processes)}")
                print("   [PASS] Process status retrieved")
            
        else:
            print(f"   [FAIL] Script start failed: {response.text}")
            
    except Exception as e:
        print(f"   [FAIL] API test failed: {str(e)}")

if __name__ == "__main__":
    test_orchestrator_api()