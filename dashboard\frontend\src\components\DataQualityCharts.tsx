import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, Typo<PERSON>, <PERSON>, Chip } from '@mui/material';
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  ResponsiveContainer,
  Tooltip,
  Legend,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid
} from 'recharts';
import { DataQualityCheck } from '../types';

interface DataQualityChartsProps {
  checks: DataQualityCheck[];
  height?: number;
}

const DataQualityCharts: React.FC<DataQualityChartsProps> = ({ 
  checks, 
  height = 300 
}) => {
  // Calculate status distribution
  const statusCounts = checks.reduce((acc, check) => {
    acc[check.status] = (acc[check.status] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const statusData = Object.entries(statusCounts).map(([status, count]) => ({
    name: status.charAt(0).toUpperCase() + status.slice(1),
    value: count,
    status
  }));

  // Calculate checks by table
  const tableData = checks.reduce((acc, check) => {
    const existing = acc.find(item => item.table === check.table_name);
    if (existing) {
      existing.total += 1;
      existing[check.status] = (existing[check.status] || 0) + 1;
    } else {
      acc.push({
        table: check.table_name.length > 15 
          ? check.table_name.substring(0, 12) + '...' 
          : check.table_name,
        fullTableName: check.table_name,
        total: 1,
        [check.status]: 1
      });
    }
    return acc;
  }, [] as any[]);

  // Colors for different statuses
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'passed': return '#4caf50';
      case 'failed': return '#f44336';
      case 'warning': return '#ff9800';
      case 'error': return '#e91e63';
      default: return '#9e9e9e';
    }
  };

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <Box sx={{ 
          backgroundColor: 'rgba(255, 255, 255, 0.95)', 
          p: 1, 
          border: '1px solid #ccc',
          borderRadius: 1
        }}>
          <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
            {data.name || data.fullTableName}
          </Typography>
          <Typography variant="body2">
            Count: {data.value || data.total}
          </Typography>
        </Box>
      );
    }
    return null;
  };

  return (
    <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
      {/* Status Distribution Pie Chart */}
      <Card sx={{ flex: '1 1 300px', minWidth: 300 }}>
        <CardHeader 
          title="Check Status Distribution"
          subheader="Overall data quality check results"
        />
        <CardContent>
          <ResponsiveContainer width="100%" height={height}>
            <PieChart>
              <Pie
                data={statusData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, value, percent }) => 
                  `${name}: ${value} (${(percent * 100).toFixed(0)}%)`
                }
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {statusData.map((entry, index) => (
                  <Cell 
                    key={`cell-${index}`} 
                    fill={getStatusColor(entry.status)} 
                  />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
            </PieChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Checks by Table Bar Chart */}
      <Card sx={{ flex: '1 1 400px', minWidth: 400 }}>
        <CardHeader 
          title="Checks by Table"
          subheader="Data quality checks per database table"
        />
        <CardContent>
          <ResponsiveContainer width="100%" height={height}>
            <BarChart data={tableData} margin={{ top: 20, right: 30, left: 20, bottom: 60 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="table"
                angle={-45}
                textAnchor="end"
                height={80}
                tick={{ fontSize: 11 }}
              />
              <YAxis 
                tick={{ fontSize: 12 }}
                label={{ value: 'Check Count', angle: -90, position: 'insideLeft' }}
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              <Bar
                dataKey="total"
                fill="#1976d2"
                name="Total Checks"
                radius={[2, 2, 0, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </Box>
  );
};

export default DataQualityCharts;
