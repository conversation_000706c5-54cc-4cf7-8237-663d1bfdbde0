#!/usr/bin/env python
"""
Test script to verify the path format fixes for bash shell compatibility.
This script tests the key fixes we made to resolve the path format issues.
"""

import os
import sys
from pathlib import Path

def test_path_conversion():
    """Test path conversion functions."""
    print("=== Testing Path Conversion Functions ===")
    
    # Test Windows backslash to forward slash conversion
    windows_path = r"O:\Github\MaxPain\MaxPain2024\env\Scripts\python.exe"
    bash_path = windows_path.replace('\\', '/')
    
    print(f"Original Windows path: {windows_path}")
    print(f"Converted bash path: {bash_path}")
    
    # Test if path exists (checking both formats)
    path_exists = False
    if os.path.exists(bash_path):
        path_exists = True
        print(f"✓ Path exists with forward slashes: {bash_path}")
    elif os.name == 'nt':
        # On Windows, also try the original path with backslashes
        backslash_path = bash_path.replace('/', '\\')
        if os.path.exists(backslash_path):
            path_exists = True
            print(f"✓ Path exists with backslashes: {backslash_path}")
    
    if not path_exists:
        print(f"✗ Path does not exist: {bash_path}")
        print(f"✗ Alternative path does not exist: {bash_path.replace('/', chr(92))}")
    
    return path_exists

def test_script_path():
    """Test script path handling."""
    print("\n=== Testing Script Path Handling ===")
    
    script_dir = Path(__file__).parent
    script_path = script_dir / "UpdateIndexOptionPostgres.py"
    
    print(f"Script directory: {script_dir}")
    print(f"Original script path: {script_path}")
    print(f"Bash-compatible script path: {str(script_path).replace(chr(92), '/')}")
    
    if script_path.exists():
        print(f"✓ Script exists: {script_path}")
        return True
    else:
        print(f"✗ Script not found: {script_path}")
        return False

def test_working_directory():
    """Test working directory handling."""
    print("\n=== Testing Working Directory Handling ===")
    
    current_dir = Path.cwd()
    print(f"Current directory: {current_dir}")
    print(f"Bash-compatible current directory: {str(current_dir).replace(chr(92), '/')}")
    
    return True

def test_command_construction():
    """Test command construction for subprocess execution."""
    print("\n=== Testing Command Construction ===")
    
    # Simulate the command construction from the orchestrator
    python_interpreter = "O:/Github/MaxPain/MaxPain2024/env/Scripts/python.exe"
    script_path = "O:/Github/MaxPain/MaxPain2024/UpdateIndexOptionPostgres.py"
    
    cmd_args = [python_interpreter, script_path]
    
    print(f"Command arguments: {cmd_args}")
    print(f"Command as string: {' '.join(cmd_args)}")
    
    # Check if both files exist
    python_exists = os.path.exists(python_interpreter) or os.path.exists(python_interpreter.replace('/', '\\'))
    script_exists = os.path.exists(script_path) or os.path.exists(script_path.replace('/', '\\'))
    
    print(f"Python interpreter exists: {python_exists}")
    print(f"Script exists: {script_exists}")
    
    return python_exists and script_exists

def main():
    """Run all tests."""
    print("Testing Path Format Fixes for Bash Shell Compatibility")
    print("=" * 60)
    
    tests = [
        test_path_conversion,
        test_script_path,
        test_working_directory,
        test_command_construction
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
            results.append(False)
    
    print("\n=== Test Summary ===")
    passed = sum(results)
    total = len(results)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("✓ All tests passed! Path format fixes should work.")
    else:
        print("✗ Some tests failed. Path format issues may persist.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
