#!/usr/bin/env python3
"""
Test script to verify that main() function respects debug mode settings.
"""

import os
import sys
import subprocess
from unittest.mock import patch

def test_debug_mode_main():
    """Test that main() function respects HKEX_DEBUG environment variable"""
    
    print("=== Testing Debug Mode in main() Function ===")
    
    # Test 1: Debug mode enabled (should skip tests)
    print("\n1. Testing with HKEX_DEBUG=true")
    env = os.environ.copy()
    env['HKEX_DEBUG'] = 'true'
    env['WILL9700_DB'] = 'postgresql://test:test@localhost:5432/test'  # Mock DB
    env['out_path'] = './test_data/'
    
    try:
        # Run the main function with dry-run to avoid actual processing
        result = subprocess.run([
            sys.executable, 'UpdateIndexOptionPostgres.py', 
            '--dry-run', '--date', '2024-12-15'
        ], 
        env=env, 
        capture_output=True, 
        text=True, 
        timeout=30,
        cwd='.'
        )
        
        output = result.stdout + result.stderr
        print("Output:", output)
        
        if "⏭️  Skipping connection tests (debug mode enabled)" in output:
            print("✅ Debug mode correctly skips connection tests")
        else:
            print("❌ Debug mode not working - connection tests still running")
            
        if "test_hkex_connection" not in output.lower():
            print("✅ No connection test function calls in debug mode")
        else:
            print("❌ Connection test functions still being called")
            
    except subprocess.TimeoutExpired:
        print("⚠️  Test timed out - this might indicate connection tests are still running")
    except Exception as e:
        print(f"⚠️  Test failed with error: {e}")
    
    # Test 2: Debug mode disabled (should run tests)
    print("\n2. Testing with HKEX_DEBUG=false")
    env['HKEX_DEBUG'] = 'false'
    
    try:
        # Run with a very short timeout since connection tests might hang
        result = subprocess.run([
            sys.executable, 'UpdateIndexOptionPostgres.py', 
            '--dry-run', '--date', '2024-12-15'
        ], 
        env=env, 
        capture_output=True, 
        text=True, 
        timeout=10,  # Short timeout
        cwd='.'
        )
        
        output = result.stdout + result.stderr
        print("Output:", output)
        
        if "Testing connection to HKEX website" in output or "test_hkex_connection" in output.lower():
            print("✅ Production mode correctly runs connection tests")
        else:
            print("❌ Production mode not running connection tests")
            
    except subprocess.TimeoutExpired:
        print("✅ Production mode running connection tests (timed out as expected)")
    except Exception as e:
        print(f"⚠️  Test failed with error: {e}")

def test_debug_mode_detection():
    """Test debug mode detection function directly"""
    
    print("\n=== Testing Debug Mode Detection Function ===")
    
    # Test with different environment variable values
    test_cases = [
        ('true', True),
        ('false', False),
        ('1', True),
        ('0', False),
        ('yes', True),
        ('no', False),
        ('on', True),
        ('off', False),
        ('TRUE', True),
        ('FALSE', False),
        (None, False),  # Default when not set
    ]
    
    for env_value, expected in test_cases:
        # Set environment variable
        if env_value is None:
            if 'HKEX_DEBUG' in os.environ:
                del os.environ['HKEX_DEBUG']
        else:
            os.environ['HKEX_DEBUG'] = env_value
        
        # Import and test (need to reload module to pick up env changes)
        try:
            import importlib
            if 'hkex_fetcher' in sys.modules:
                importlib.reload(sys.modules['hkex_fetcher'])
            
            from hkex_fetcher import is_debug_mode
            result = is_debug_mode()
            
            if result == expected:
                print(f"✅ HKEX_DEBUG='{env_value}' -> {result} (expected {expected})")
            else:
                print(f"❌ HKEX_DEBUG='{env_value}' -> {result} (expected {expected})")
                
        except Exception as e:
            print(f"⚠️  Error testing HKEX_DEBUG='{env_value}': {e}")

def test_pipeline_debug_mode():
    """Test that pipeline functions respect debug mode"""
    
    print("\n=== Testing Pipeline Debug Mode Integration ===")
    
    try:
        # Test with debug mode enabled
        os.environ['HKEX_DEBUG'] = 'true'
        
        # Import pipeline functions
        import importlib
        if 'hkex_pipeline' in sys.modules:
            importlib.reload(sys.modules['hkex_pipeline'])
        if 'hkex_fetcher' in sys.modules:
            importlib.reload(sys.modules['hkex_fetcher'])
            
        from hkex_pipeline import HKEXPipeline
        from hkex_fetcher import is_debug_mode
        
        # Verify debug mode is detected
        debug_status = is_debug_mode()
        print(f"Debug mode detected: {debug_status}")
        
        if debug_status:
            print("✅ Pipeline should skip connection tests in debug mode")
        else:
            print("❌ Debug mode not properly detected")
            
        # Test pipeline creation (should not fail)
        def mock_save_data(data, report_type):
            return len(data)
            
        pipeline = HKEXPipeline("./test/", mock_save_data)
        print("✅ Pipeline created successfully with debug mode")
        
    except Exception as e:
        print(f"❌ Pipeline debug mode test failed: {e}")

if __name__ == "__main__":
    print("Testing Debug Mode Implementation")
    print("=" * 50)
    
    # Save original environment
    original_debug = os.environ.get('HKEX_DEBUG')
    
    try:
        # Run tests
        test_debug_mode_detection()
        test_pipeline_debug_mode()
        test_debug_mode_main()
        
    finally:
        # Restore original environment
        if original_debug is not None:
            os.environ['HKEX_DEBUG'] = original_debug
        elif 'HKEX_DEBUG' in os.environ:
            del os.environ['HKEX_DEBUG']
    
    print("\n" + "=" * 50)
    print("Debug mode testing completed!")
