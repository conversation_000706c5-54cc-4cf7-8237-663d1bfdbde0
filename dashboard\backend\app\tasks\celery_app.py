import os
import subprocess
import sys
from datetime import datetime
from celery import Celery

# Fix Windows multiprocessing issue for Celery
if os.name == 'nt':  # Windows
    import multiprocessing
    multiprocessing.set_start_method('spawn', force=True)

from core.config import settings

# Create Celery app
celery_app = Celery(
    "hkex_dashboard",
    broker=settings.celery_broker_url,
    backend=settings.celery_result_backend
)

celery_app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='Asia/Hong_Kong',
    enable_utc=True,
    task_track_started=True,
    task_time_limit=3600,  # 1 hour timeout
    task_soft_time_limit=3300,  # 55 minutes soft timeout
)

@celery_app.task(bind=True, name='run_processing_script')
def run_processing_script(self, script_path: str, parameters: dict, process_type: str = None):
    """Execute a data processing script"""
    try:
        # Update task state
        self.update_state(
            state='STARTED',
            meta={
                'process_type': process_type,
                'message': 'Starting script execution...',
                'progress': 0,
                'started_at': datetime.utcnow().isoformat()
            }
        )
        
        # Build command
        cmd = [sys.executable, script_path]
        
        # Add parameters as command line arguments
        if 'start_date' in parameters:
            cmd.extend(['--start-date', parameters['start_date']])
        if 'end_date' in parameters:
            cmd.extend(['--end-date', parameters['end_date']])
        if 'symbols' in parameters:
            cmd.extend(['--symbols'] + parameters['symbols'])
        
        # Execute script
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=os.path.dirname(script_path)
        )
        
        records_processed = 0
        
        # Monitor output for progress
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                line = output.strip()
                
                # Parse progress information from output
                if 'Processing' in line and 'records' in line:
                    try:
                        # Extract number of records processed
                        words = line.split()
                        for i, word in enumerate(words):
                            if word.isdigit():
                                records_processed = int(word)
                                break
                    except:
                        pass
                
                # Update progress
                self.update_state(
                    state='PROGRESS',
                    meta={
                        'process_type': process_type,
                        'message': line,
                        'progress': min(records_processed // 100, 100),  # Rough progress
                        'records_processed': records_processed,
                        'started_at': datetime.utcnow().isoformat()
                    }
                )
        
        # Wait for completion
        return_code = process.poll()
        stderr_output = process.stderr.read()
        
        if return_code == 0:
            return {
                'process_type': process_type,
                'message': 'Script completed successfully',
                'progress': 100,
                'records_processed': records_processed,
                'completed_at': datetime.utcnow().isoformat()
            }
        else:
            raise Exception(f"Script failed with return code {return_code}: {stderr_output}")
            
    except Exception as exc:
        # Update task state with error
        self.update_state(
            state='FAILURE',
            meta={
                'process_type': process_type,
                'message': f'Script execution failed: {str(exc)}',
                'error': str(exc),
                'completed_at': datetime.utcnow().isoformat()
            }
        )
        raise exc
