#!/usr/bin/env python3
"""
Test script to validate Docker setup for the MaxPain2024 application.
This script checks the imports and basic functionality before running in Docker.
"""

import os
import sys
import subprocess
from pathlib import Path

def test_basic_imports():
    """Test basic Python imports"""
    print("🔍 Testing basic imports...")
    try:
        import asyncio
        import multiprocessing
        import celery
        import fastapi
        import redis
        print("✅ All basic imports successful")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_celery_config():
    """Test Celery configuration can be imported"""
    print("\n🔍 Testing Celery configuration...")
    try:
        # Add the dashboard backend app to path
        sys.path.insert(0, str(Path(__file__).parent / "dashboard" / "backend" / "app"))
        
        # Test core config
        from app.core.config import settings
        print(f"✅ Config imported: {settings.project_name}")
        
        # Test celery app import
        from app.tasks.celery_app import celery_app
        print(f"✅ Celery app imported: {celery_app.main}")
        
        return True
    except Exception as e:
        print(f"❌ Celery config error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fastapi_import():
    """Test FastAPI app import"""
    print("\n🔍 Testing FastAPI app import...")
    try:
        # The path should already be set from previous test
        from app.main import app
        print(f"✅ FastAPI app imported: {app.title}")
        return True
    except Exception as e:
        print(f"❌ FastAPI import error: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_docker_files():
    """Check if all required Docker files exist"""
    print("\n🔍 Checking Docker files...")
    
    required_files = [
        "docker-compose.yml",
        "docker-compose.dev.yml",
        "dashboard/backend/Dockerfile",
        "start-app.sh"
    ]
    
    missing_files = []
    for file_path in required_files:
        full_path = Path(__file__).parent / file_path
        if not full_path.exists():
            missing_files.append(file_path)
        else:
            print(f"✅ Found: {file_path}")
    
    if missing_files:
        print(f"❌ Missing files: {missing_files}")
        return False
    
    return True

def main():
    """Run all tests"""
    print("🧪 Docker Setup Validation Test")
    print("=" * 50)
    
    tests = [
        ("Basic imports", test_basic_imports),
        ("Docker files", check_docker_files),
        ("Celery configuration", test_celery_config),
        ("FastAPI application", test_fastapi_import),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    
    passed = sum(results)
    total = len(results)
    
    for i, (test_name, _) in enumerate(tests):
        status = "✅ PASS" if results[i] else "❌ FAIL"
        print(f"  {test_name}: {status}")
    
    print(f"\n📈 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Docker setup should work correctly.")
        print("\n🚀 Next steps:")
        print("  1. Run: docker-compose build")
        print("  2. Run: docker-compose up -d")
        print("  3. Or use: ./start-app.sh dev")
    else:
        print("⚠️  Some tests failed. Fix the issues before proceeding with Docker.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
