# HKEX Option Report Processing System - Dashboard Project Brief

## Project Overview
The HKEX Option Report Processing System is a comprehensive Python application that extracts, processes, and analyzes Hong Kong Exchange (HKEX) option market data. The system downloads daily option reports, calculates option Greeks using the Black-Scholes model, and maintains a sophisticated PostgreSQL database for option position analysis and risk management.

## Current System Components
1. **UpdateIndexOptionPostgres.py** - Processes index option data (HSI, HHI, HTI, MHI)
2. **UpdateStockOptionReportPostgres.py** - Processes stock option data 
3. **copyViewMultiDB.py** - Synchronizes data between local and remote databases

## Problem Statement
The current system lacks visibility into the data processing pipeline. Users need:
- Real-time monitoring of data import progress
- Ability to orchestrate and control the three main scripts
- Visual inspection of record counts across various data tables
- Troubleshooting capabilities when processes fail
- Historical tracking of processing performance

## Project Goal
Create a comprehensive Dashboard application that provides:
1. **Orchestration** - Start/stop/schedule the three main data processing scripts
2. **Monitoring** - Real-time progress tracking and status updates
3. **Visualization** - Charts and tables showing record counts, processing times, and data quality metrics
4. **Troubleshooting** - Error logs, retry mechanisms, and diagnostic tools

## Success Criteria
- Dashboard can successfully launch and monitor all three processing scripts
- Real-time visibility into processing progress and record counts
- Historical tracking of processing performance and data quality
- User-friendly interface for non-technical users
- Robust error handling and recovery mechanisms

## Technical Constraints
- Must integrate with existing PostgreSQL database schema
- Should leverage existing Python codebase and dependencies
- Must handle SQLAlchemy 2.0 compatibility requirements
- Should minimize Yahoo Finance API calls to avoid rate limiting

## Key Stakeholders
- Data analysts who need to monitor daily processing
- System administrators who need to troubleshoot issues
- Risk managers who rely on timely option data updates
