"""
Test script to debug HKEX fetcher issues
"""
import os
import datetime as dt
from hkex_fetcher import fetch_daily_report, fetch_weekly_report, test_hkex_connection

def test_fetch_and_examine_content():
    """Test fetching a report and examine its content"""
    
    # Test date - yesterday or a recent trading day
    test_date = dt.date(2025, 7, 18)
    test_pathname = "o:/Github/MaxPain/MaxPain2024/"
    
    print(f"Testing HKEX fetcher for date: {test_date}")
    print("=" * 50)
    
    # Test connection first
    print("1. Testing HKEX connection...")
    connection_ok = test_hkex_connection()
    print(f"Connection result: {connection_ok}")
    print()
    
    # Test fetching a daily report
    print("2. Testing daily report fetch (MHI)...")
    file_path, success = fetch_daily_report("MHI", test_date, test_pathname)
    print(f"Fetch result: {success}")
    print(f"File path: {file_path}")
    
    if file_path and os.path.exists(file_path):
        print(f"3. Examining content of: {file_path}")
        with open(file_path, 'rb') as f:
            content = f.read()
        
        # Convert to text and show first 500 characters
        try:
            text_content = content.decode('utf-8')
            print(f"File size: {len(content)} bytes")
            print("First 500 characters:")
            print("-" * 30)
            print(text_content[:500])
            print("-" * 30)
            
            # Check for error indicators
            error_indicators = [
                "This site can't be reached",
                "ERR_HTTP2_PROTOCOL_ERROR", 
                "temporarily down",
                "moved permanently",
                "404",
                "403",
                "500"
            ]
            
            found_errors = []
            for indicator in error_indicators:
                if indicator.lower() in text_content.lower():
                    found_errors.append(indicator)
            
            if found_errors:
                print(f"❌ ERROR INDICATORS FOUND: {found_errors}")
            else:
                print("✅ No obvious error indicators found")
                
        except UnicodeDecodeError:
            print("❌ Could not decode file as UTF-8")
    else:
        print("❌ File was not created or does not exist")
    
    print()
    
    # Test weekly report too
    print("4. Testing weekly report fetch (HTI)...")
    file_path_weekly, success_weekly = fetch_weekly_report("HTI", test_date, test_pathname)
    print(f"Weekly fetch result: {success_weekly}")
    print(f"Weekly file path: {file_path_weekly}")
    
    if file_path_weekly and os.path.exists(file_path_weekly):
        with open(file_path_weekly, 'rb') as f:
            content_weekly = f.read()
        
        try:
            text_content_weekly = content_weekly.decode('utf-8')
            print(f"Weekly file size: {len(content_weekly)} bytes")
            
            # Quick check for errors
            if "This site can't be reached" in text_content_weekly:
                print("❌ Weekly report also contains error page")
            else:
                print("✅ Weekly report seems OK (no 'site can't be reached' error)")
                
        except UnicodeDecodeError:
            print("❌ Could not decode weekly file as UTF-8")

if __name__ == "__main__":
    test_fetch_and_examine_content()
