import React, { useState, useEffect } from 'react';
import {
  ThemeProvider,
  createTheme,
  CssBaseline,
  Container,
  Typography,
  Box,
  Card,
  CardContent,
  Button,
  Alert,
  Grid,
  Chip,
  List,
  ListItem,
  ListItemText,
  Tabs,
  Tab
} from '@mui/material';
import { Cable, CableOutlined, Dashboard, PlayArrow, BarChart } from '@mui/icons-material';
import { useWebSocket } from './hooks/useSimpleWebSocket';
import { getWebSocketUrl } from './config/environment';
import ProcessStarter from './components/ProcessStarter';
import ProcessMonitor from './components/ProcessMonitor';
import SystemStatus from './components/SystemStatus';

const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
});

const App: React.FC = () => {
  const [apiStatus, setApiStatus] = useState<'loading' | 'connected' | 'error'>('loading');
  const [apiData, setApiData] = useState<any>(null);
  const [processTypes, setProcessTypes] = useState<any>(null);
  const [activeProcesses, setActiveProcesses] = useState<any[]>([]);
  const [currentTab, setCurrentTab] = useState(0);  // WebSocket connection
  const { lastMessage, readyState, messages } = useWebSocket(getWebSocketUrl());

  const getWebSocketStatus = () => {
    switch (readyState) {
      case WebSocket.CONNECTING:
        return { status: 'connecting', text: 'Connecting...', color: 'info' as const };
      case WebSocket.OPEN:
        return { status: 'connected', text: 'Connected', color: 'success' as const };
      case WebSocket.CLOSING:
        return { status: 'closing', text: 'Closing...', color: 'warning' as const };
      case WebSocket.CLOSED:
        return { status: 'closed', text: 'Disconnected', color: 'error' as const };
      default:
        return { status: 'unknown', text: 'Unknown', color: 'default' as const };
    }
  };

  useEffect(() => {
    // Test API connection
    fetch('/api/v1/')
      .then(res => res.json())
      .then(data => {
        setApiData(data);
        setApiStatus('connected');
      })
      .catch(err => {
        console.error('API connection failed:', err);
        setApiStatus('error');
      });

    // Fetch process types
    fetch('/api/v1/processes/types')
      .then(res => res.json())
      .then(data => {
        setProcessTypes(data);
      })
      .catch(err => {
        console.error('Failed to fetch process types:', err);
      });
  }, []);

  // Handle WebSocket messages
  useEffect(() => {
    if (lastMessage) {
      console.log('Received WebSocket message:', lastMessage);
      
      // Handle different message types
      if (lastMessage.type === 'process_update') {
        // Update active processes
        setActiveProcesses(prev => {
          const existing = prev.find(p => p.task_id === lastMessage.data.task_id);
          if (existing) {
            return prev.map(p => 
              p.task_id === lastMessage.data.task_id 
                ? { ...p, ...lastMessage.data }
                : p
            );
          } else {
            return [...prev, lastMessage.data];
          }
        });
      }
    }
  }, [lastMessage]);
  const startProcess = async (processType: string, parameters: Record<string, any> = {}) => {
    try {
      const response = await fetch('/api/v1/processes/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          process_type: processType,
          parameters
        })
      });
      
      const data = await response.json();
      console.log('Process started:', data);
      
      // Add to active processes
      setActiveProcesses(prev => [...prev, {
        task_id: data.task_id,
        process_type: processType,
        status: 'starting',
        start_time: new Date().toISOString(),
        parameters
      }]);
    } catch (err) {
      console.error('Failed to start process:', err);
      throw err;
    }
  };

  const wsStatus = getWebSocketStatus();
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Container maxWidth="xl">
        <Box sx={{ my: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            HKEX Data Processing Dashboard
          </Typography>
          
          {/* Connection Status */}
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Backend API Status
                  </Typography>
                  {apiStatus === 'loading' && (
                    <Alert severity="info">Connecting to backend...</Alert>
                  )}
                  {apiStatus === 'connected' && (
                    <Alert severity="success">
                      Connected to backend API v{apiData?.version}
                    </Alert>
                  )}
                  {apiStatus === 'error' && (
                    <Alert severity="error">
                      Failed to connect to backend API
                    </Alert>
                  )}
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    WebSocket Status
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {wsStatus.status === 'connected' ? <Cable /> : <CableOutlined />}
                    <Chip 
                      label={wsStatus.text} 
                      color={wsStatus.color}
                      variant={wsStatus.status === 'connected' ? 'filled' : 'outlined'}
                    />
                  </Box>
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    Messages received: {messages.length}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Navigation Tabs */}
          <Card sx={{ mb: 3 }}>
            <Tabs 
              value={currentTab} 
              onChange={(_, newValue) => setCurrentTab(newValue)}
              variant="fullWidth"
            >
              <Tab icon={<Dashboard />} label="Overview" />
              <Tab icon={<PlayArrow />} label="Process Control" />
              <Tab icon={<BarChart />} label="Monitoring" />
            </Tabs>
          </Card>

          {/* Tab Content */}
          {currentTab === 0 && (
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <SystemStatus />
              </Grid>
              <Grid item xs={12}>
                <ProcessMonitor 
                  processes={activeProcesses}
                  onRefresh={() => {
                    // Refresh logic could go here
                    console.log('Refreshing process monitor');
                  }}
                />
              </Grid>
            </Grid>
          )}          {currentTab === 1 && processTypes && (
            <ProcessStarter
              processTypes={processTypes.process_types || []}
              onStartProcess={startProcess}
              onProcessStarted={() => {
                // Refresh processes after starting
                console.log('Process started, refreshing...');
              }}
              disabled={apiStatus !== 'connected'}
            />
          )}

          {currentTab === 2 && (
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <ProcessMonitor 
                  processes={activeProcesses}
                  onRefresh={() => {
                    console.log('Refreshing active processes');
                  }}                  onStop={(taskId: string) => {
                    console.log('Stopping process:', taskId);
                    // TODO: Implement process stopping
                  }}
                />
              </Grid>
            </Grid>
          )}
        </Box>
      </Container>
    </ThemeProvider>
  );
};

export default App;