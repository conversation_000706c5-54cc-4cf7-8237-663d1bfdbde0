# WebSocket Conflict Solution

## 🚨 Problem Description

The HKEX fetcher is experiencing a WebSocket import conflict in the Docker environment:

```
❌ Weekly pipeline error: cannot import name 'WebSocketApp' from 'websocket' (/app/websocket/__init__.py)
```

This prevents Selenium from working properly, causing the fallback chain to fail at step 2.

## 🔍 Root Cause Analysis

The issue occurs because:

1. **Package Conflict**: There are two different WebSocket packages:
   - `websocket` (incorrect for Selenium)
   - `websocket-client` (required by Selenium)

2. **Import Path Collision**: Selenium expects `WebSocketApp` from `websocket-client`, but finds a different `websocket` package first.

3. **Docker Environment**: The conflict is more pronounced in Docker where package resolution is stricter.

## ✅ Solutions Implemented

### 1. **Enhanced Fallback Chain** (Immediate Fix)

Modified `safe_http_get_with_firecrawl_fallback()` to include a 3-tier approach with prioritized Enhanced HTTP:

```python
1️⃣ Enhanced HTTP GET (better session management) ← FIRST PRIORITY
2️⃣ Selenium-based GET (with graceful import error handling)
3️⃣ Firecrawl-based GET (if available) ← LAST RESORT
```

### 2. **Graceful Selenium Error Handling**

```python
def selenium_http_get(url, timeout=30):
    try:
        from selenium import webdriver
        # ... selenium code
    except ImportError as e:
        print(f"❌ Selenium import failed: {e}")
        return MockResponse(b"", 500, url, error=f"Selenium import error: {e}")
```

### 3. **Enhanced HTTP GET Method**

Added `enhanced_http_get()` with:
- Better session management
- More sophisticated headers
- HKEX-specific optimizations
- Random delays to avoid bot detection

### 4. **Package Dependencies Fix**

Updated `requirements.txt` with correct WebSocket packages:

```txt
websockets==15.0.1          # For general WebSocket support
websocket-client==1.8.0     # Required by Selenium
trio==0.30.0                # Selenium dependency
trio-websocket==0.12.2      # Selenium dependency
selenium==4.33.0            # Specific version
webdriver-manager==4.0.2    # Chrome driver management
```

## 🛠️ Manual Fix Instructions

If the automatic solutions don't work, run these commands in the Docker container:

```bash
# 1. Remove conflicting packages
pip uninstall -y websocket websocket-client

# 2. Install correct websocket-client
pip install websocket-client==1.8.0

# 3. Reinstall Selenium dependencies
pip install selenium==4.33.0 webdriver-manager==4.0.2
pip install trio==0.30.0 trio-websocket==0.12.2
```

## 🧪 Testing Tools Created

### 1. **WebSocket Conflict Diagnostic Script**
```bash
python scripts/fix_websocket_conflict.py
```
- Diagnoses the WebSocket import issue
- Attempts automatic fix
- Provides manual fix instructions

### 2. **Docker Fallback Test Script**
```bash
python scripts/test_docker_fallback.py
```
- Tests all 4 fallback methods
- Simulates the failing weekly report scenario
- Validates enhanced HTTP GET method

## 📊 Expected Behavior After Fix

### Success Case (Enhanced HTTP Working):
```
🔄 Attempting fetch for: https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/htiwo250625.htm
1️⃣ Enhanced HTTP: SUCCESS (2736 bytes retrieved)
✅ Enhanced HTTP method succeeded
```

### Fallback Case (Enhanced HTTP Fails, Selenium Works):
```
🔄 Attempting fetch for: https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/htiwo250625.htm
1️⃣ Enhanced HTTP: FAILED (timeout/403)
2️⃣ Selenium: SUCCESS (2736 bytes retrieved)
✅ Selenium method succeeded
```

### Last Resort Case (Both Fail, Firecrawl Used):
```
🔄 Attempting fetch for: https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/htiwo250625.htm
1️⃣ Enhanced HTTP: FAILED (timeout/403)
2️⃣ Selenium: FAILED (import error)
3️⃣ Firecrawl: SUCCESS (2736 bytes retrieved)
✅ Firecrawl fallback succeeded
```

## 🎯 Benefits

1. **Immediate Relief**: Enhanced HTTP GET provides backup when Selenium fails
2. **Credit Conservation**: Firecrawl only used as last resort
3. **Graceful Degradation**: System continues working even with import issues
4. **Better Success Rate**: 4 methods instead of 3 increases reliability

## 🔮 Long-term Recommendations

1. **Docker Image Optimization**: Build custom Docker image with correct packages
2. **Package Pinning**: Use exact versions in requirements.txt
3. **Health Checks**: Add startup tests to verify Selenium functionality
4. **Monitoring**: Track which fallback method succeeds most often

## 📁 Files Modified

- `scripts/hkex_fetcher.py` - Enhanced fallback system
- `requirements.txt` - Fixed WebSocket dependencies
- `scripts/fix_websocket_conflict.py` - Diagnostic tool (NEW)
- `scripts/test_docker_fallback.py` - Testing tool (NEW)

## 🚀 Immediate Action Required

1. **Rebuild Docker containers** with updated requirements.txt
2. **Test the enhanced fallback** with weekly report fetching
3. **Monitor success rates** of each fallback method
4. **Run diagnostic script** if issues persist

The enhanced system should now handle the WebSocket conflict gracefully and continue fetching HKEX data successfully.
