"""
OptionPricing.py - Independent Option Pricing Mathematical Library

This module provides a comprehensive set of mathematical functions for option pricing
and risk calculations. It is designed to be highly independent with no external
dependencies on databases, file systems, or business logic.

Key Features:
- Pure mathematical functions only
- No database access or external dependencies
- Black-Scholes option pricing model
- Greeks calculations (Delta, Gamma, Charm)
- Implied volatility calculation
- Robust input validation and error handling

Dependencies:
- math (standard library)
- numpy (for statistical functions)
- scipy.stats (for normal distribution)
- scipy.optimize (for implied volatility calculation)

Author: Extracted from Storacle module for better separation of concerns
"""

import math
import numpy as np
from scipy.stats import norm
from scipy.optimize import bisect


def d1(strike, stock_price, volatility, time_to_expiry):
    """
    Calculate d1 parameter for Black-Scholes formula.
    
    Args:
        strike (float): Strike price of the option
        stock_price (float): Current stock price
        volatility (float): Implied volatility (as decimal, e.g., 0.2 for 20%)
        time_to_expiry (float): Time to expiry in years
        
    Returns:
        float: d1 value for Black-Scholes calculations
        
    Raises:
        ValueError: If any input is invalid
    """
    # Input validation and conversion
    try:
        strike_float = float(strike)
        stock_price_float = float(stock_price)
        volatility_float = float(volatility)
        time_float = float(time_to_expiry)
    except (ValueError, TypeError):
        raise ValueError("All inputs must be convertible to float")
    
    # Ensure positive values with minimum bounds
    if strike_float <= 0 or stock_price_float <= 0:
        raise ValueError("Strike and stock price must be positive")
    
    volatility_float = max(volatility_float, 0.0001)  # Minimum volatility
    time_float = max(time_float, 0.0001)  # Minimum time
    
    # Black-Scholes d1 formula with additional safeguards
    try:
        log_ratio = math.log(stock_price_float / strike_float)
        denominator = volatility_float * math.sqrt(time_float)

        # Additional check for denominator
        if denominator == 0:
            return 999999999  # Very large number as fallback

        return (log_ratio + (volatility_float ** 2 / 2) * time_float) / denominator

    except (ValueError, ZeroDivisionError, OverflowError) as e:
        # Return a very large number to indicate extreme conditions
        return 999999999


def d2(d1_value, volatility, time_to_expiry):
    """
    Calculate d2 parameter for Black-Scholes formula.
    
    Args:
        d1_value (float): d1 value from d1() function
        volatility (float): Implied volatility (as decimal)
        time_to_expiry (float): Time to expiry in years
        
    Returns:
        float: d2 value for Black-Scholes calculations
    """
    # Input validation and conversion
    try:
        d1_float = float(d1_value)
        volatility_float = float(volatility)
        time_float = float(time_to_expiry)
    except (ValueError, TypeError):
        raise ValueError("All inputs must be convertible to float")
    
    # Ensure minimum bounds
    volatility_float = max(volatility_float, 0.0001)
    time_float = max(time_float, 0.0001)
    
    return d1_float - volatility_float * math.sqrt(time_float)


def call_price(strike, stock_price, volatility, time_to_expiry, risk_free_rate=0):
    """
    Calculate European call option price using Black-Scholes formula.
    
    Args:
        strike (float): Strike price of the option
        stock_price (float): Current stock price
        volatility (float): Implied volatility (as decimal)
        time_to_expiry (float): Time to expiry in years
        risk_free_rate (float): Risk-free interest rate (default: 0)
        
    Returns:
        float: Call option price
    """
    # Input validation
    try:
        strike_float = float(strike)
        stock_price_float = float(stock_price)
        volatility_float = float(volatility)
        time_float = float(time_to_expiry)
        rate_float = float(risk_free_rate)
    except (ValueError, TypeError):
        raise ValueError("All inputs must be convertible to float")
    
    if strike_float <= 0 or stock_price_float <= 0:
        raise ValueError("Strike and stock price must be positive")
    
    # Calculate d1 and d2
    d1_val = d1(strike_float, stock_price_float, volatility_float, time_float)
    d2_val = d2(d1_val, volatility_float, time_float)
    
    # Black-Scholes call price formula
    call_delta = norm.cdf(d1_val)
    discounted_strike = strike_float * math.exp(-rate_float * time_float)
    
    return stock_price_float * call_delta - discounted_strike * norm.cdf(d2_val)


def put_price(strike, stock_price, volatility, time_to_expiry, risk_free_rate=0):
    """
    Calculate European put option price using Black-Scholes formula.
    
    Args:
        strike (float): Strike price of the option
        stock_price (float): Current stock price
        volatility (float): Implied volatility (as decimal)
        time_to_expiry (float): Time to expiry in years
        risk_free_rate (float): Risk-free interest rate (default: 0)
        
    Returns:
        float: Put option price
    """
    # Input validation
    try:
        strike_float = float(strike)
        stock_price_float = float(stock_price)
        volatility_float = float(volatility)
        time_float = float(time_to_expiry)
        rate_float = float(risk_free_rate)
    except (ValueError, TypeError):
        raise ValueError("All inputs must be convertible to float")
    
    if strike_float <= 0 or stock_price_float <= 0:
        raise ValueError("Strike and stock price must be positive")
    
    # Calculate d1 and d2
    d1_val = d1(strike_float, stock_price_float, volatility_float, time_float)
    d2_val = d2(d1_val, volatility_float, time_float)
    
    # Black-Scholes put price formula using N(-d1) and N(-d2)
    discounted_strike = strike_float * math.exp(-rate_float * time_float)
    
    return discounted_strike * norm.cdf(-d2_val) - stock_price_float * norm.cdf(-d1_val)


def gamma(d1_value, stock_price, volatility, time_to_expiry):
    """
    Calculate Gamma (rate of change of Delta) for an option.
    
    Args:
        d1_value (float): d1 value from d1() function
        stock_price (float): Current stock price
        volatility (float): Implied volatility (as decimal)
        time_to_expiry (float): Time to expiry in years
        
    Returns:
        float: Gamma value (rounded to 8 decimal places)
    """
    # Input validation and conversion
    try:
        d1_float = float(d1_value)
        stock_price_float = float(stock_price)
        volatility_float = float(volatility)
        time_float = float(time_to_expiry)
    except (ValueError, TypeError):
        raise ValueError("All inputs must be convertible to float")
    
    # Apply bounds and validation
    stock_price_bounded = max(stock_price_float, 0.001)
    volatility_bounded = min(max(volatility_float, 0.001), 20)  # Cap at 2000%
    time_bounded = max(time_float, 0.001)
    
    # Return 0 for boundary conditions
    if (stock_price_bounded == 0.001 or volatility_bounded == 0.001 or 
        time_bounded == 0.001 or volatility_bounded == 20):
        return 0
    
    # Gamma formula: φ(d1) / (S * σ * √T)
    # where φ(d1) is the standard normal probability density function
    try:
        phi_d1 = np.exp(-0.5 * d1_float**2) / np.sqrt(2 * np.pi)
        denominator = stock_price_bounded * volatility_bounded * np.sqrt(time_bounded)

        # Additional safeguard against division by zero
        if denominator == 0:
            return 0

        gamma_value = phi_d1 / denominator

        # Check for invalid results
        if not np.isfinite(gamma_value):
            return 0

        return round(gamma_value, 8)

    except (ValueError, ZeroDivisionError, OverflowError):
        return 0


def charm(d1_value, volatility, time_to_expiry):
    """
    Calculate Charm (rate of change of Delta with respect to time) for an option.
    
    Args:
        d1_value (float): d1 value from d1() function
        volatility (float): Implied volatility (as decimal)
        time_to_expiry (float): Time to expiry in years
        
    Returns:
        float: Charm value (rounded to 8 decimal places)
    """
    # Input validation and conversion
    try:
        d1_float = float(d1_value)
        volatility_float = float(volatility)
        time_float = float(time_to_expiry)
    except (ValueError, TypeError):
        raise ValueError("All inputs must be convertible to float")
    
    # Apply bounds
    volatility_bounded = min(max(volatility_float, 0.001), 20)
    time_bounded = max(time_float, 0.001)
    
    # Charm calculation with safeguards
    try:
        norm_vol = volatility_bounded * np.sqrt(time_bounded)
        d2_charm = d1_float - norm_vol
        phi_d1 = np.exp(-0.5 * d1_float**2) / np.sqrt(2 * np.pi)

        # Safeguard against division by zero
        if time_bounded == 0:
            return 0

        charm_value = phi_d1 * d2_charm / (2 * time_bounded)

        # Check for invalid results
        if not np.isfinite(charm_value):
            return 0

        return round(charm_value, 8)

    except (ValueError, ZeroDivisionError, OverflowError):
        return 0


def calculate_implied_volatility(option_price, stock_price, strike, time_to_expiry, 
                                option_type, risk_free_rate=0):
    """
    Calculate implied volatility from option price using bisection method.
    
    Args:
        option_price (float): Market price of the option
        stock_price (float): Current stock price
        strike (float): Strike price of the option
        time_to_expiry (float): Time to expiry in years
        option_type (str): 'call' or 'put' (case insensitive)
        risk_free_rate (float): Risk-free interest rate (default: 0)
        
    Returns:
        float: Implied volatility as decimal (e.g., 0.2 for 20%)
        
    Raises:
        ValueError: If inputs are invalid or no solution exists
    """
    # Input validation
    try:
        option_price_float = float(option_price)
        stock_price_float = float(stock_price)
        strike_float = float(strike)
        time_float = float(time_to_expiry)
        rate_float = float(risk_free_rate)
    except (ValueError, TypeError):
        raise ValueError("All numeric inputs must be convertible to float")
    
    if option_price_float < 0:
        raise ValueError("Option price cannot be negative")
    if stock_price_float <= 0 or strike_float <= 0:
        raise ValueError("Stock price and strike must be positive")
    if time_float <= 0:
        raise ValueError("Time to expiry must be positive")
    
    option_type_upper = option_type.upper()
    if option_type_upper not in ['C', 'CALL', 'P', 'PUT']:
        raise ValueError("Option type must be 'call' or 'put'")
    
    # Handle zero option price
    if option_price_float == 0:
        return 0.00001
    
    def price_difference(volatility):
        """Calculate difference between theoretical and market price"""
        try:
            if option_type_upper in ['C', 'CALL']:
                theoretical_price = call_price(strike_float, stock_price_float, 
                                             volatility, time_float, rate_float)
            else:  # PUT
                theoretical_price = put_price(strike_float, stock_price_float, 
                                            volatility, time_float, rate_float)
            return theoretical_price - option_price_float
        except:
            return float('inf')  # Return large value for invalid volatility
    
    try:
        # Use bisection method to find implied volatility
        # Search between 0.01% and 2000% volatility
        implied_vol = bisect(price_difference, 0.00001, 20.0, xtol=1e-6)
        return implied_vol
    except ValueError as e:
        # Handle cases where no solution exists (e.g., arbitrage opportunities)
        intrinsic_value = 0
        if option_type_upper in ['C', 'CALL']:
            intrinsic_value = max(stock_price_float - strike_float, 0)
        else:  # PUT
            intrinsic_value = max(strike_float - stock_price_float, 0)
        
        if option_price_float < intrinsic_value:
            print(f"Warning: Option price ({option_price_float}) below intrinsic value ({intrinsic_value})")
            return 0.00001
        
        print(f"Warning: Could not calculate IV for option_price={option_price_float}, "
              f"stock_price={stock_price_float}, strike={strike_float}, "
              f"time={time_float}, type={option_type}. Error: {e}")
        return 0.00001


# Convenience aliases for backward compatibility
CalcIV = calculate_implied_volatility


if __name__ == '__main__':
    """
    Test the option pricing functions with sample data.
    """
    print("=== Option Pricing Library Test ===")
    
    # Test parameters
    S = 24000  # Stock price
    K = 23600  # Strike price
    T = 0.0122  # Time to expiry (about 4.5 days)
    sigma = 0.2  # 20% volatility
    r = 0  # Risk-free rate
    
    # Test basic calculations
    print(f"Stock Price: {S}, Strike: {K}, Time: {T}, Volatility: {sigma}")
    
    d1_val = d1(K, S, sigma, T)
    d2_val = d2(d1_val, sigma, T)
    print(f"d1: {d1_val:.6f}")
    print(f"d2: {d2_val:.6f}")
    
    call_val = call_price(K, S, sigma, T, r)
    put_val = put_price(K, S, sigma, T, r)
    print(f"Call Price: {call_val:.6f}")
    print(f"Put Price: {put_val:.6f}")
    
    gamma_val = gamma(d1_val, S, sigma, T)
    charm_val = charm(d1_val, sigma, T)
    print(f"Gamma: {gamma_val:.8f}")
    print(f"Charm: {charm_val:.8f}")
    
    # Test implied volatility
    iv_call = calculate_implied_volatility(call_val, S, K, T, 'call', r)
    iv_put = calculate_implied_volatility(put_val, S, K, T, 'put', r)
    print(f"Implied Vol (Call): {iv_call:.6f}")
    print(f"Implied Vol (Put): {iv_put:.6f}")
    
    print("=== All tests completed ===")
