from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List
from ...core.database import get_db
from ...core.security import verify_token
from ...services.monitoring import MonitoringService
from ...models.schemas import SystemHealthResponse, TableMetricsResponse

router = APIRouter()

@router.get("/status", response_model=SystemHealthResponse)
async def get_system_status(db: Session = Depends(get_db)):
    """Get overall system health status"""
    monitoring_service = MonitoringService(db)
    return await monitoring_service.get_system_health()

@router.get("/tables/metrics", response_model=TableMetricsResponse)
async def get_table_metrics(db: Session = Depends(get_db)):
    """Get metrics for all important database tables"""
    monitoring_service = MonitoringService(db)
    return await monitoring_service.get_table_metrics()

@router.get("/health")
async def health_check():
    """Simple health check endpoint"""
    return {"status": "healthy", "timestamp": "2025-05-24T10:00:00Z"}
