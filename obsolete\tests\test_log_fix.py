import sys
import os
sys.path.append('.')

print("Starting orchestrator test...")

try:
    from app.services.simple_orchestrator import orchestrator
    print("Orchestrator loaded successfully")
    
    # Check available methods
    methods = [method for method in dir(orchestrator) if not method.startswith('_') and callable(getattr(orchestrator, method))]
    print("Available methods:")
    for method in methods:
        print(f"  - {method}")
    
    # Test log methods
    task_id = 'test_123'
    print(f"Testing log methods with task_id: {task_id}")
    
    # Test get_log_tail
    result = orchestrator.get_log_tail(task_id)
    print(f"get_log_tail result: {result}")
    
    # Test get_process_logs
    result = orchestrator.get_process_logs(task_id)
    print(f"get_process_logs result: {result}")
    
    # Test get_active_processes
    result = orchestrator.get_active_processes()
    print(f"get_active_processes result: {result}")
    
    print("All log methods are working!")
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
