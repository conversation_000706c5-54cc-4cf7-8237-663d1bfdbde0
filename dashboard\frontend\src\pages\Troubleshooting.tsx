import React, { useState } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  CardHeader,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Button,
  Pagination
} from '@mui/material';
import {
  BugReport,
  Refresh,
  Download,
  Search
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { useQuery } from '@tanstack/react-query';
import { apiService } from '../services/api';

const Troubleshooting: React.FC = () => {
  const [severityFilter, setSeverityFilter] = useState('');
  const [processFilter, setProcessFilter] = useState('');
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [page, setPage] = useState(1);
  const pageSize = 10;

  const { data: errorLogs, isLoading, refetch } = useQuery({
    queryKey: ['errorLogs', severityFilter, processFilter, startDate, endDate, page],
    queryFn: () => apiService.getErrorLogs({
      severity: severityFilter || undefined,
      process: processFilter || undefined,
      start_date: startDate?.toISOString().split('T')[0],
      end_date: endDate?.toISOString().split('T')[0],
      page,
      page_size: pageSize,
    }),
  });

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'ERROR':
        return 'error';
      case 'WARNING':
        return 'warning';
      case 'INFO':
        return 'info';
      default:
        return 'default';
    }
  };

  const handlePageChange = (event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value);
  };

  const clearFilters = () => {
    setSeverityFilter('');
    setProcessFilter('');
    setStartDate(null);
    setEndDate(null);
    setPage(1);
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box>
        <Typography variant="h4" gutterBottom>
          Troubleshooting & Error Logs
        </Typography>

        <Grid container spacing={3}>
          {/* Filters */}
          <Grid item xs={12}>
            <Card>
              <CardHeader title="Filters" />
              <CardContent>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} sm={6} md={2}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Severity</InputLabel>
                      <Select
                        value={severityFilter}
                        onChange={(e) => setSeverityFilter(e.target.value)}
                        label="Severity"
                      >
                        <MenuItem value="">All</MenuItem>
                        <MenuItem value="ERROR">Error</MenuItem>
                        <MenuItem value="WARNING">Warning</MenuItem>
                        <MenuItem value="INFO">Info</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={6} md={2}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Process</InputLabel>
                      <Select
                        value={processFilter}
                        onChange={(e) => setProcessFilter(e.target.value)}
                        label="Process"
                      >
                        <MenuItem value="">All</MenuItem>
                        <MenuItem value="update_index_options">Index Options</MenuItem>
                        <MenuItem value="update_stock_options">Stock Options</MenuItem>
                        <MenuItem value="copy_view_multidb">Copy Views</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>                  <Grid item xs={12} sm={6} md={2}>
                    <DatePicker
                      label="Start Date"
                      value={startDate}
                      onChange={(newValue) => setStartDate(newValue)}
                      slots={{
                        textField: TextField
                      }}
                      slotProps={{
                        textField: { size: "small", fullWidth: true }
                      }}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6} md={2}>
                    <DatePicker
                      label="End Date"
                      value={endDate}
                      onChange={(newValue) => setEndDate(newValue)}
                      slots={{
                        textField: TextField
                      }}
                      slotProps={{
                        textField: { size: "small", fullWidth: true }
                      }}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6} md={2}>
                    <Button
                      fullWidth
                      variant="outlined"
                      startIcon={<Refresh />}
                      onClick={() => refetch()}
                    >
                      Refresh
                    </Button>
                  </Grid>

                  <Grid item xs={12} sm={6} md={2}>
                    <Button
                      fullWidth
                      variant="outlined"
                      onClick={clearFilters}
                    >
                      Clear Filters
                    </Button>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Error Logs Table */}
          <Grid item xs={12}>
            <Card>
              <CardHeader 
                title="Error Logs" 
                action={
                  <Button startIcon={<Download />} variant="outlined">
                    Export
                  </Button>
                }
              />
              <CardContent>
                <TableContainer component={Paper}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Timestamp</TableCell>
                        <TableCell>Severity</TableCell>
                        <TableCell>Process</TableCell>
                        <TableCell>Message</TableCell>
                        <TableCell>Details</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {isLoading ? (
                        <TableRow>
                          <TableCell colSpan={5} align="center">
                            Loading...
                          </TableCell>
                        </TableRow>
                      ) : errorLogs?.errors.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={5} align="center">
                            No error logs found
                          </TableCell>
                        </TableRow>
                      ) : (
                        errorLogs?.errors.map((error) => (
                          <TableRow key={error.id}>
                            <TableCell>
                              {new Date(error.timestamp).toLocaleString()}
                            </TableCell>
                            <TableCell>
                              <Chip
                                label={error.severity}
                                color={getSeverityColor(error.severity) as any}
                                size="small"
                              />
                            </TableCell>
                            <TableCell>
                              {error.process ? (
                                <Chip
                                  label={error.process}
                                  variant="outlined"
                                  size="small"
                                />
                              ) : (
                                '-'
                              )}
                            </TableCell>
                            <TableCell>{error.message}</TableCell>
                            <TableCell>
                              {error.details ? (
                                <Button size="small" variant="text">
                                  View Details
                                </Button>
                              ) : (
                                '-'
                              )}
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>

                {errorLogs && errorLogs.total_count > pageSize && (
                  <Box display="flex" justifyContent="center" mt={2}>
                    <Pagination
                      count={Math.ceil(errorLogs.total_count / pageSize)}
                      page={page}
                      onChange={handlePageChange}
                      color="primary"
                    />
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* System Diagnostics */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardHeader title="System Diagnostics" />
              <CardContent>
                <Button
                  fullWidth
                  variant="contained"
                  startIcon={<BugReport />}
                  sx={{ mb: 2 }}
                >
                  Run Full Diagnostic
                </Button>
                <Typography variant="body2" color="text.secondary">
                  Performs comprehensive system health check including:
                  • Database connectivity
                  • File system access
                  • External API availability
                  • Performance metrics
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          {/* Quick Actions */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardHeader title="Quick Recovery Actions" />
              <CardContent>
                <Grid container spacing={1}>
                  <Grid item xs={12}>
                    <Button fullWidth variant="outlined" sx={{ mb: 1 }}>
                      Restart Failed Processes
                    </Button>
                  </Grid>
                  <Grid item xs={12}>
                    <Button fullWidth variant="outlined" sx={{ mb: 1 }}>
                      Clear Cache
                    </Button>
                  </Grid>
                  <Grid item xs={12}>
                    <Button fullWidth variant="outlined" sx={{ mb: 1 }}>
                      Validate Data Integrity
                    </Button>
                  </Grid>
                  <Grid item xs={12}>
                    <Button fullWidth variant="outlined">
                      Export System State
                    </Button>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    </LocalizationProvider>
  );
};

export default Troubleshooting;
