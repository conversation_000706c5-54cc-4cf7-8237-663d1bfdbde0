#!/usr/bin/env python3
"""
Kaleido Test Script for SROC_FS.py
This script tests if kaleido is working properly for Plotly image export.
"""

import sys
import os
from datetime import datetime

def test_kaleido():
    """Test kaleido installation and functionality."""
    print("=" * 60)
    print("KALEIDO TEST SCRIPT")
    print("=" * 60)
    
    # Test 1: Import plotly
    print("\n1. Testing Plotly import...")
    try:
        import plotly.graph_objects as go
        import plotly.io as pio
        print("✓ Plotly imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import Plotly: {e}")
        return False
    
    # Test 2: Check kaleido
    print("\n2. Testing kaleido availability...")
    try:
        import kaleido
        print(f"✓ Kaleido imported successfully (version: {kaleido.__version__})")
    except ImportError:
        print("✗ Kaleido not found. Install with: pip install kaleido")
        return False
    
    # Test 3: Create simple figure
    print("\n3. Creating test figure...")
    try:
        fig = go.Figure()
        fig.add_trace(go.Scatter(x=[1, 2, 3, 4], y=[10, 11, 12, 13], name="Test"))
        fig.update_layout(title="Kaleido Test Chart", width=800, height=600)
        print("✓ Test figure created successfully")
    except Exception as e:
        print(f"✗ Failed to create test figure: {e}")
        return False
    
    # Test 4: Test image export with timeout
    print("\n4. Testing image export with timeout...")
    test_dir = "test_output"
    os.makedirs(test_dir, exist_ok=True)

    test_file = f"{test_dir}/kaleido_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"

    try:
        # Test with subprocess timeout to prevent hanging
        import subprocess
        import tempfile
        import pickle

        # Create temporary files
        temp_script = tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False)
        temp_pickle = tempfile.NamedTemporaryFile(suffix='.pkl', delete=False)

        try:
            # Save figure to pickle
            with open(temp_pickle.name, 'wb') as f:
                pickle.dump(fig, f)

            # Create test script
            script_content = f'''
import pickle
import sys
import os

try:
    with open(r"{temp_pickle.name}", "rb") as f:
        fig = pickle.load(f)
    fig.write_image(r"{test_file}", width=800, height=600)
    print("SUCCESS")
except Exception as e:
    print(f"ERROR: {{type(e).__name__}}: {{e}}")
    sys.exit(1)
'''
            temp_script.write(script_content)
            temp_script.close()

            print("   Running kaleido with 15-second timeout...")

            # Run with timeout
            result = subprocess.run([
                sys.executable, temp_script.name
            ], capture_output=True, text=True, timeout=15)

            if result.returncode == 0 and "SUCCESS" in result.stdout:
                if os.path.exists(test_file):
                    file_size = os.path.getsize(test_file)
                    print(f"✓ Image exported successfully: {test_file} ({file_size} bytes)")
                    return True
                else:
                    print("✗ Process succeeded but file not found")
                    return False
            else:
                error_msg = result.stderr or result.stdout or "Unknown error"
                print(f"✗ Export failed: {error_msg}")
                return False

        finally:
            # Cleanup
            try:
                os.unlink(temp_script.name)
                os.unlink(temp_pickle.name)
            except:
                pass

    except subprocess.TimeoutExpired:
        print("✗ Image export timed out after 15 seconds (kaleido hanging)")
        return False
    except Exception as e:
        print(f"✗ Failed to export image: {type(e).__name__}: {e}")
        return False

def print_troubleshooting():
    """Print troubleshooting guide."""
    print("\n" + "=" * 60)
    print("TROUBLESHOOTING GUIDE")
    print("=" * 60)
    print("""
If kaleido tests fail, try these solutions:

1. REINSTALL KALEIDO:
   pip uninstall kaleido
   pip install kaleido
   
2. FORCE REINSTALL:
   pip install --force-reinstall kaleido
   
3. USE CONDA (if applicable):
   conda install -c conda-forge python-kaleido
   
4. CHECK ANTIVIRUS:
   - Windows Defender may block kaleido executable
   - Add Python/kaleido to antivirus exceptions
   
5. PERMISSIONS:
   - Run as administrator (Windows)
   - Check write permissions to output directory
   
6. ALTERNATIVE SOLUTIONS:
   - Use HTML export instead: fig.write_html("chart.html")
   - Use orca (deprecated): pip install psutil requests plotly-orca
   - Use matplotlib backend: pip install plotly-matplotlib
   
7. ENVIRONMENT ISSUES:
   - Try in a fresh virtual environment
   - Check PATH environment variable
   - Restart IDE/terminal after installation

8. SYSTEM-SPECIFIC:
   Windows: May need Visual C++ Redistributable
   Linux: May need additional system libraries
   macOS: May need Xcode command line tools
""")

def main():
    """Main function."""
    success = test_kaleido()
    
    if success:
        print("\n🎉 All tests passed! Kaleido is working correctly.")
        print("Your SROC_FS.py script should now work with PNG export.")
    else:
        print("\n❌ Kaleido tests failed.")
        print_troubleshooting()
        print("\nThe SROC_FS.py script will fall back to HTML export.")
    
    print(f"\nTest completed at: {datetime.now()}")

if __name__ == "__main__":
    main()
