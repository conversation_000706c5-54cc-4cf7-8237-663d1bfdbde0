#!/usr/bin/env python3
"""
Test script to verify the backend server and log endpoints are working correctly.
This will start the server temporarily and test the fixed endpoints.
"""
import sys
import os
import time
import requests
import threading
import subprocess
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

def start_server():
    """Start the FastAPI server in a separate process"""
    try:
        from app.main import app
        import uvicorn
        
        print("🚀 Starting FastAPI server...")
        uvicorn.run(
            app, 
            host="127.0.0.1", 
            port=8000, 
            log_level="info"
        )
    except Exception as e:
        print(f"❌ Error starting server: {e}")

def test_endpoints():
    """Test the backend endpoints"""
    base_url = "http://127.0.0.1:8000"
    
    # Wait for server to start
    print("⏳ Waiting for server to start...")
    time.sleep(3)
    
    try:
        # Test health endpoint
        print("\n🔍 Testing health endpoint...")
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print(f"✅ Health check passed: {response.json()}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
            
        # Test processes endpoint
        print("\n🔍 Testing processes endpoint...")
        response = requests.get(f"{base_url}/processes", timeout=5)
        if response.status_code == 200:
            print(f"✅ Processes endpoint working: {len(response.json())} processes")
        else:
            print(f"❌ Processes endpoint failed: {response.status_code}")
            
        # Test log-tail endpoint (this was the failing one)
        print("\n🔍 Testing log-tail endpoint...")
        response = requests.get(f"{base_url}/logs/test-task/tail", timeout=5)
        if response.status_code == 200:
            print(f"✅ Log-tail endpoint working: {response.json()}")
        elif response.status_code == 404:
            print(f"✅ Log-tail endpoint working (404 expected for non-existent task)")
        else:
            print(f"❌ Log-tail endpoint failed: {response.status_code}")
            try:
                print(f"Response: {response.text}")
            except:
                pass
                
        # Test task start to see if we get 500 errors
        print("\n🔍 Testing task start endpoint...")
        task_data = {
            "script_type": "test",
            "script_path": "echo 'test task'",
            "description": "Test task for verification"
        }
        response = requests.post(f"{base_url}/start-task", json=task_data, timeout=10)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Task start working: {result.get('task_id', 'No task_id')}")
            
            # Wait a moment for task to process
            time.sleep(2)
            
            # Test log-tail with real task
            task_id = result.get('task_id')
            if task_id:
                print(f"\n🔍 Testing log-tail with real task: {task_id}")
                response = requests.get(f"{base_url}/logs/{task_id}/tail", timeout=5)
                if response.status_code == 200:
                    print(f"✅ Real task log-tail working: {response.json()}")
                else:
                    print(f"⚠️  Real task log-tail response: {response.status_code}")
        else:
            print(f"❌ Task start failed: {response.status_code}")
            try:
                print(f"Response: {response.text}")
            except:
                pass
                
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to server")
    except Exception as e:
        print(f"❌ Test error: {e}")

if __name__ == "__main__":
    print("🧪 Starting Backend Server Test")
    print("=" * 50)
    
    # Start server in a separate thread
    server_thread = threading.Thread(target=start_server, daemon=True)
    server_thread.start()
    
    # Run tests
    test_endpoints()
    
    print("\n" + "=" * 50)
    print("🏁 Backend Server Test Complete")
