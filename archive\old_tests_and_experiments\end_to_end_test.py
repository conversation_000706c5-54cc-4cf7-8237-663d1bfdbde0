#!/usr/bin/env python3
"""
Comprehensive End-to-End Test for HKEX Dashboard System
Tests the complete workflow from backend startup to process execution
"""

import asyncio
import os
import sys
import time
import json
import requests
import websocket
import threading
from pathlib import Path

# Configuration
BACKEND_URL = "http://localhost:8000"
WEBSOCKET_URL = "ws://localhost:8000/ws"
FRONTEND_URL = "http://localhost:3000"

class HKEXDashboardE2ETest:
    def __init__(self):
        self.test_results = []
        self.ws = None
        self.ws_messages = []
        
    def log_test(self, name, success, message=""):
        result = "✅ PASS" if success else "❌ FAIL"
        print(f"{result}: {name}")
        if message:
            print(f"      {message}")
        self.test_results.append({
            "name": name,
            "success": success,
            "message": message
        })
        
    def test_backend_health(self):
        """Test backend API health endpoint"""
        try:
            response = requests.get(f"{BACKEND_URL}/health", timeout=5)
            self.log_test("Backend Health Check", 
                         response.status_code == 200, 
                         f"Status: {response.status_code}")
            return response.status_code == 200
        except Exception as e:
            self.log_test("Backend Health Check", False, str(e))
            return False
            
    def test_process_types_api(self):
        """Test process types API endpoint"""
        try:
            response = requests.get(f"{BACKEND_URL}/api/processes/types", timeout=5)
            if response.status_code == 200:
                data = response.json()
                count = len(data)
                self.log_test("Process Types API", True, f"Found {count} process types")
                return True
            else:
                self.log_test("Process Types API", False, f"Status: {response.status_code}")
                return False
        except Exception as e:
            self.log_test("Process Types API", False, str(e))
            return False
            
    def test_websocket_connection(self):
        """Test WebSocket connection"""
        try:
            def on_message(ws, message):
                self.ws_messages.append(message)
                
            def on_error(ws, error):
                print(f"WebSocket error: {error}")
                
            def on_close(ws, close_status_code, close_msg):
                print("WebSocket connection closed")
                
            self.ws = websocket.WebSocketApp(WEBSOCKET_URL,
                                           on_message=on_message,
                                           on_error=on_error,
                                           on_close=on_close)
            
            # Run WebSocket in background thread
            ws_thread = threading.Thread(target=self.ws.run_forever)
            ws_thread.daemon = True
            ws_thread.start()
            
            time.sleep(2)  # Give time to establish connection
            
            if self.ws.sock and self.ws.sock.connected:
                self.log_test("WebSocket Connection", True, "Connected successfully")
                return True
            else:
                self.log_test("WebSocket Connection", False, "Failed to connect")
                return False
                
        except Exception as e:
            self.log_test("WebSocket Connection", False, str(e))
            return False
            
    def test_process_execution(self):
        """Test process execution via API"""
        try:
            # Test with update_index_options process
            payload = {
                "process_type": "update_index_options",
                "parameters": {
                    "txn_date": "2024-01-15"  # Test date
                }
            }
            
            response = requests.post(f"{BACKEND_URL}/api/processes/start", 
                                   json=payload, timeout=10)
            
            if response.status_code in [200, 201]:
                data = response.json()
                task_id = data.get("task_id")
                self.log_test("Process Execution Start", True, f"Task ID: {task_id}")
                
                # Wait a bit and check status
                time.sleep(3)
                status_response = requests.get(f"{BACKEND_URL}/api/processes/status/{task_id}", 
                                             timeout=5)
                
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    self.log_test("Process Status Check", True, 
                                f"Status: {status_data.get('status', 'unknown')}")
                    return True
                else:
                    self.log_test("Process Status Check", False, 
                                f"Status code: {status_response.status_code}")
                    return False
            else:
                self.log_test("Process Execution Start", False, 
                            f"Status: {response.status_code}, Response: {response.text}")
                return False
                
        except Exception as e:
            self.log_test("Process Execution", False, str(e))
            return False
            
    def test_log_retrieval(self):
        """Test log retrieval APIs"""
        try:
            # Get active processes first
            response = requests.get(f"{BACKEND_URL}/api/processes/active", timeout=5)
            
            if response.status_code == 200:
                processes = response.json()
                
                if processes:
                    # Test with first active process
                    task_id = processes[0]["task_id"]
                    
                    # Test log tail
                    log_response = requests.get(f"{BACKEND_URL}/api/processes/logs/{task_id}/tail?lines=10", 
                                              timeout=5)
                    
                    if log_response.status_code == 200:
                        self.log_test("Log Tail Retrieval", True, f"Retrieved logs for {task_id}")
                        return True
                    else:
                        self.log_test("Log Tail Retrieval", False, 
                                    f"Status: {log_response.status_code}")
                        return False
                else:
                    self.log_test("Log Retrieval", True, "No active processes to test with")
                    return True
            else:
                self.log_test("Log Retrieval", False, f"Status: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("Log Retrieval", False, str(e))
            return False
            
    def test_process_history(self):
        """Test process history API"""
        try:
            response = requests.get(f"{BACKEND_URL}/api/processes/history", timeout=5)
            
            if response.status_code == 200:
                history = response.json()
                count = len(history)
                self.log_test("Process History", True, f"Retrieved {count} historical processes")
                return True
            else:
                self.log_test("Process History", False, f"Status: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("Process History", False, str(e))
            return False
            
    def test_frontend_accessibility(self):
        """Test if frontend is accessible"""
        try:
            response = requests.get(FRONTEND_URL, timeout=5)
            self.log_test("Frontend Accessibility", 
                         response.status_code == 200,
                         f"Status: {response.status_code}")
            return response.status_code == 200
        except Exception as e:
            self.log_test("Frontend Accessibility", False, str(e))
            return False
            
    def cleanup(self):
        """Cleanup test resources"""
        if self.ws:
            try:
                self.ws.close()
            except:
                pass
                
    def run_all_tests(self):
        """Run all end-to-end tests"""
        print("🚀 HKEX Dashboard End-to-End Test Suite")
        print("=" * 60)
        
        # Core Backend Tests
        print("\n📡 Backend API Tests:")
        self.test_backend_health()
        self.test_process_types_api()
        
        # Process Management Tests
        print("\n⚙️ Process Management Tests:")
        self.test_process_execution()
        self.test_log_retrieval()
        self.test_process_history()
        
        # Real-time Communication Tests
        print("\n🔄 Real-time Communication Tests:")
        self.test_websocket_connection()
        
        # Frontend Tests
        print("\n🖥️ Frontend Tests:")
        self.test_frontend_accessibility()
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        
        passed = sum(1 for result in self.test_results if result["success"])
        total = len(self.test_results)
        
        print(f"Tests Passed: {passed}/{total}")
        
        if passed == total:
            print("🎉 ALL TESTS PASSED! System is ready for production.")
            return True
        else:
            print("⚠️ Some tests failed. Review issues before production deployment.")
            print("\nFailed Tests:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  ❌ {result['name']}: {result['message']}")
            return False
            
def main():
    """Main test execution"""
    # Check if we're in the right directory
    dashboard_path = Path("o:/Github/MaxPain/MaxPain2024/dashboard")
    if not dashboard_path.exists():
        print("❌ Dashboard directory not found. Please run from the correct location.")
        return False
        
    test_suite = HKEXDashboardE2ETest()
    
    try:
        success = test_suite.run_all_tests()
        return success
    finally:
        test_suite.cleanup()

if __name__ == "__main__":
    print("Starting HKEX Dashboard End-to-End Test...")
    print("Note: Make sure both backend and frontend servers are running!")
    print("Backend should be at: http://localhost:8000")
    print("Frontend should be at: http://localhost:3000")
    print()
    
    success = main()
    sys.exit(0 if success else 1)
