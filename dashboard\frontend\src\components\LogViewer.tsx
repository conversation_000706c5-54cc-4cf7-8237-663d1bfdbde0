import React, { useState, useEffect, useRef, useCallback } from 'react';
import { <PERSON>, Typo<PERSON>, Card, CardContent, Button, CircularProgress, Alert, <PERSON>bs, Tab, IconButton, Tooltip } from '@mui/material';
import { Refresh, GetApp, FullscreenExit, Fullscreen } from '@mui/icons-material';
import { format } from 'date-fns';

interface ProcessLog {
  task_id: string;
  process_type: string;
  status: string;
  started_at: string;
  completed_at?: string;
  parameters: Record<string, any>;
  message?: string;
  error?: string;
  detailed_logs?: string;
  output?: string[];
}

interface LogViewerProps {
  taskId: string;
  onClose?: () => void;
}

const LogViewer: React.FC<LogViewerProps> = ({ taskId, onClose }) => {
  const [logs, setLogs] = useState<ProcessLog | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState(0);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(false);
  const logContainerRef = useRef<HTMLDivElement>(null);
  const refreshIntervalRef = useRef<NodeJS.Timeout>();
  const fetchLogs = useCallback(async () => {
    try {
      const response = await fetch(`/api/v1/processes/${taskId}/logs`);
      if (!response.ok) {
        throw new Error(`Failed to fetch logs: ${response.statusText}`);
      }
      const data = await response.json();
      setLogs(data);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch logs');
    } finally {
      setLoading(false);
    }
  }, [taskId]);

  useEffect(() => {
    fetchLogs();
  }, [fetchLogs]);
  useEffect(() => {
    if (autoRefresh && logs && (logs.status === 'running' || logs.status === 'starting')) {
      refreshIntervalRef.current = setInterval(fetchLogs, 3000);
    } else {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
    }

    return () => {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
    };
  }, [autoRefresh, logs, fetchLogs]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleRefresh = () => {
    setLoading(true);
    fetchLogs();
  };

  const handleDownloadLogs = () => {
    if (!logs?.detailed_logs) return;

    const blob = new Blob([logs.detailed_logs], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${taskId}.log`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const toggleAutoRefresh = () => {
    setAutoRefresh(!autoRefresh);
  };

  const formatTimestamp = (timestamp: string) => {
    return format(new Date(timestamp), 'PPpp');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'failed':
        return 'error';
      case 'running':
        return 'info';
      case 'starting':
        return 'warning';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
        <Typography variant="h6" sx={{ ml: 2 }}>Loading logs...</Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" action={
        <Button color="inherit" size="small" onClick={handleRefresh}>
          Retry
        </Button>
      }>
        {error}
      </Alert>
    );
  }

  if (!logs) {
    return (
      <Alert severity="warning">
        No logs found for task ID: {taskId}
      </Alert>
    );
  }

  return (
    <Card sx={{ 
      height: isFullscreen ? '100vh' : 'auto', 
      position: isFullscreen ? 'fixed' : 'relative',
      top: isFullscreen ? 0 : 'auto',
      left: isFullscreen ? 0 : 'auto',
      width: isFullscreen ? '100vw' : 'auto',
      zIndex: isFullscreen ? 9999 : 'auto',
      display: 'flex',
      flexDirection: 'column'
    }}>
      <CardContent sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        {/* Header */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Box>
            <Typography variant="h5" component="h2">
              Process Logs: {logs.task_id}
            </Typography>
            <Typography variant="subtitle1" color="textSecondary">
              Type: {logs.process_type} | Status: 
              <Box component="span" color={`${getStatusColor(logs.status)}.main`} fontWeight="bold" ml={0.5}>
                {logs.status.toUpperCase()}
              </Box>
            </Typography>
          </Box>
          
          <Box display="flex" gap={1}>
            <Tooltip title="Toggle Auto Refresh">
              <Button
                variant={autoRefresh ? "contained" : "outlined"}
                size="small"
                onClick={toggleAutoRefresh}
                disabled={logs.status !== 'running' && logs.status !== 'starting'}
              >
                Auto Refresh
              </Button>
            </Tooltip>
            
            <Tooltip title="Refresh">
              <IconButton onClick={handleRefresh} size="small">
                <Refresh />
              </IconButton>
            </Tooltip>
            
            <Tooltip title="Download Logs">
              <IconButton 
                onClick={handleDownloadLogs} 
                size="small"
                disabled={!logs.detailed_logs}
              >
                <GetApp />
              </IconButton>
            </Tooltip>
            
            <Tooltip title={isFullscreen ? "Exit Fullscreen" : "Fullscreen"}>
              <IconButton onClick={toggleFullscreen} size="small">
                {isFullscreen ? <FullscreenExit /> : <Fullscreen />}
              </IconButton>
            </Tooltip>
            
            {onClose && (
              <Button variant="outlined" size="small" onClick={onClose}>
                Close
              </Button>
            )}
          </Box>
        </Box>

        {/* Process Info */}
        <Box mb={2}>
          <Typography variant="body2" color="textSecondary">
            Started: {formatTimestamp(logs.started_at)}
            {logs.completed_at && (
              <> | Completed: {formatTimestamp(logs.completed_at)}</>
            )}
          </Typography>
          
          {logs.message && (
            <Typography variant="body2" color="textSecondary">
              Latest: {logs.message}
            </Typography>
          )}
          
          {logs.error && (
            <Alert severity="error" sx={{ mt: 1 }}>
              {logs.error}
            </Alert>
          )}
        </Box>

        {/* Parameters */}
        <Box mb={2}>
          <Typography variant="h6">Parameters:</Typography>
          <Box component="pre" sx={{ 
            bgcolor: 'grey.100', 
            p: 1, 
            borderRadius: 1, 
            fontSize: '0.875rem',
            overflow: 'auto'
          }}>
            {JSON.stringify(logs.parameters, null, 2)}
          </Box>
        </Box>

        {/* Tabs for different log views */}
        <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
          <Tabs value={activeTab} onChange={handleTabChange} aria-label="log tabs">
            <Tab label="Detailed Logs" />
            <Tab label="Recent Output" />
          </Tabs>

          {/* Tab Content */}
          {activeTab === 0 && (
            <Box 
              ref={logContainerRef}
              sx={{ 
                flex: 1,
                mt: 2,
                p: 2,
                bgcolor: 'grey.900',
                color: 'grey.100',
                fontFamily: 'monospace',
                fontSize: '0.875rem',
                borderRadius: 1,
                overflow: 'auto',
                minHeight: '300px',
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-word'
              }}
            >
              {logs.detailed_logs || 'No detailed logs available'}
            </Box>
          )}

          {activeTab === 1 && (
            <Box 
              sx={{ 
                flex: 1,
                mt: 2,
                p: 2,
                bgcolor: 'grey.900',
                color: 'grey.100',
                fontFamily: 'monospace',
                fontSize: '0.875rem',
                borderRadius: 1,
                overflow: 'auto',
                minHeight: '300px'
              }}
            >
              {logs.output && logs.output.length > 0 ? (
                logs.output.map((line, index) => (
                  <Box key={index} component="div">
                    {line}
                  </Box>
                ))
              ) : (
                'No recent output available'
              )}
            </Box>
          )}
        </Box>
      </CardContent>
    </Card>
  );
};

export default LogViewer;
