#!/usr/bin/env python3
"""
Final Live API Test for HKEX Dashboard
Tests the complete system functionality with correct API format
"""

import requests
import json
import time
from datetime import datetime

def test_hkex_dashboard_complete():
    """Complete test of the HKEX Dashboard system"""
    
    base_url = "http://localhost:8000"
    api_base = f"{base_url}/api/v1"
    
    print("=" * 60)
    print("HKEX Dashboard Complete System Test")
    print("=" * 60)
    print(f"Backend URL: {base_url}")
    print(f"API Base: {api_base}")
    print("-" * 60)
    
    # Test 1: Check process types
    print("\n1. Checking available process types...")
    try:
        response = requests.get(f"{api_base}/processes/types", timeout=5)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            types_data = response.json()
            print("   [PASS] Process types retrieved")
            print(f"   Available: {len(types_data.get('process_types', []))} process types")
            for ptype in types_data.get('process_types', [])[:3]:
                print(f"   - {ptype.get('label', 'N/A')}: {ptype.get('description', 'N/A')}")
    except Exception as e:
        print(f"   [FAIL] Error: {str(e)}")
    
    # Test 2: Start Index Options Update Process
    print("\n2. Starting HKEX Index Options Update...")
    
    # Correct API format based on schema
    process_request = {
        "process": "update_index_options",
        "parameters": {
            "txn_date": "2025-05-24",
            "dry_run": True,
            "batch_size": 10
        }
    }
    
    try:
        response = requests.post(f"{api_base}/processes/start", 
                               json=process_request, timeout=15)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            task_id = result.get("task_id")
            print(f"   Task ID: {task_id}")
            print("   [PASS] Index Options update started successfully")
            
            # Monitor the process
            if task_id:
                print("\n3. Monitoring process execution...")
                for i in range(5):  # Check for 5 iterations
                    time.sleep(2)
                    try:
                        status_response = requests.get(f"{api_base}/processes/{task_id}/status", timeout=5)
                        if status_response.status_code == 200:
                            status_data = status_response.json()
                            status = status_data.get("status", "unknown")
                            message = status_data.get("message", "")
                            print(f"   [{i+1}/5] Status: {status} - {message}")
                            
                            if status in ["success", "failed", "cancelled"]:
                                break
                        else:
                            print(f"   [{i+1}/5] Status check failed: {status_response.status_code}")
                    except Exception as e:
                        print(f"   [{i+1}/5] Status check error: {str(e)}")
                
                print("   [PASS] Process monitoring completed")
        else:
            print(f"   [FAIL] Process start failed: {response.text}")
            
    except Exception as e:
        print(f"   [FAIL] Process start error: {str(e)}")
    
    # Test 3: Test Stock Options Update
    print("\n4. Testing Stock Options Update...")
    
    stock_process_request = {
        "process": "update_stock_options",
        "parameters": {
            "txn_date": "2025-05-24",
            "dry_run": True
        }
    }
    
    try:
        response = requests.post(f"{api_base}/processes/start", 
                               json=stock_process_request, timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            task_id = result.get("task_id")
            print(f"   Task ID: {task_id}")
            print("   [PASS] Stock Options update started successfully")
        else:
            print(f"   [FAIL] Stock Options start failed: {response.text}")
            
    except Exception as e:
        print(f"   [FAIL] Stock Options error: {str(e)}")
    
    # Test 4: Check active processes
    print("\n5. Checking active processes...")
    try:
        response = requests.get(f"{api_base}/processes/active", timeout=5)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            processes = response.json()
            print(f"   Active processes: {len(processes)}")
            print("   [PASS] Active processes retrieved")
            
            for proc in processes[:3]:  # Show first 3
                proc_id = proc.get("task_id", "N/A")
                proc_status = proc.get("status", "N/A")
                proc_type = proc.get("process", "N/A")
                print(f"   - {proc_id}: {proc_type} ({proc_status})")
        else:
            print(f"   [FAIL] Active processes failed: {response.text}")
            
    except Exception as e:
        print(f"   [FAIL] Active processes error: {str(e)}")
    
    # Test 5: Test compatibility endpoint
    print("\n6. Testing compatibility endpoint...")
    
    compat_request = {
        "process": "update_index_options",
        "parameters": {
            "dry_run": True
        }
    }
    
    try:
        response = requests.post(f"{base_url}/start-process", 
                               json=compat_request, timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("   [PASS] Compatibility endpoint working")
            print(f"   Response: {result.get('message', 'N/A')}")
        else:
            print(f"   [FAIL] Compatibility endpoint failed: {response.text}")
            
    except Exception as e:
        print(f"   [FAIL] Compatibility endpoint error: {str(e)}")
    
    print("\n" + "=" * 60)
    print("HKEX Dashboard System Test Complete")
    print("=" * 60)
    print(f"Timestamp: {datetime.now().isoformat()}")
    print("System is ready for production use!")

if __name__ == "__main__":
    test_hkex_dashboard_complete()
