import os
from sqlalchemy import create_engine, text
import pandas as pd
from datetime import datetime

# Create a mock engine (this won't actually connect to a database)
engine = create_engine('sqlite:///:memory:')

# Test a simple query with CAST
def test_cast_query():
    # Create a connection
    with engine.connect() as conn:
        # Define a query with CAST
        query = text("""
            SELECT * FROM some_table
            WHERE date_column = CAST(:date AS DATE)
        """)
        
        # Define parameters
        params = {"date": "2024-05-19"}
        
        # In a real scenario, this would execute the query
        # Here we just print it to verify the syntax
        print(f"Query: {query}")
        print(f"Parameters: {params}")
        
        # Return success
        return True

# Run the test
if __name__ == "__main__":
    result = test_cast_query()
    print(f"Test result: {result}")
