#!/usr/bin/env python3
"""
Simple Verification Test for HKEX Dashboard Core Functionality
Quick test to verify the system is working without starting servers
"""

import sys
import os
from pathlib import Path

def test_core_functionality():
    """Test core functionality without servers"""
    print("🔧 HKEX Dashboard Core Functionality Verification")
    print("=" * 60)
    
    results = []
    
    # Test 1: Directory Structure
    print("\n1. Testing Directory Structure...")
    dashboard_path = Path("o:/Github/MaxPain/MaxPain2024/dashboard")
    backend_path = dashboard_path / "backend"
    frontend_path = dashboard_path / "frontend"
    
    if dashboard_path.exists() and backend_path.exists() and frontend_path.exists():
        print("   ✅ Dashboard directory structure verified")
        results.append(True)
    else:
        print("   ❌ Dashboard directory structure incomplete")
        results.append(False)
    
    # Test 2: Core Scripts
    print("\n2. Testing Core HKEX Scripts...")
    scripts_to_check = [
        "UpdateIndexOptionPostgres.py",
        "UpdateIndexOptionPostgres_wrapper.py", 
        "UpdateStockOptionReportPostgres.py",
        "copyViewMultiDB.py"
    ]
    
    missing_scripts = []
    for script in scripts_to_check:
        script_path = Path(f"o:/Github/MaxPain/MaxPain2024/{script}")
        if script_path.exists():
            print(f"   ✅ {script} found")
        else:
            print(f"   ❌ {script} missing")
            missing_scripts.append(script)
    
    results.append(len(missing_scripts) == 0)
    
    # Test 3: Backend Components
    print("\n3. Testing Backend Components...")
    backend_components = [
        "app/main.py",
        "app/services/simple_orchestrator.py",
        "app/api/routes/processes.py",
        "app/models/schemas.py",
        "app/websocket/manager.py"
    ]
    
    missing_components = []
    for component in backend_components:
        component_path = backend_path / component
        if component_path.exists():
            print(f"   ✅ {component} found")
        else:
            print(f"   ❌ {component} missing")
            missing_components.append(component)
    
    results.append(len(missing_components) == 0)
    
    # Test 4: Frontend Components  
    print("\n4. Testing Frontend Components...")
    frontend_components = [
        "src/App.tsx",
        "src/components/ProcessStarter.tsx",
        "package.json"
    ]
    
    missing_frontend = []
    for component in frontend_components:
        component_path = frontend_path / component
        if component_path.exists():
            print(f"   ✅ {component} found")
        else:
            print(f"   ❌ {component} missing")
            missing_frontend.append(component)
    
    results.append(len(missing_frontend) == 0)
    
    # Test 5: Windows Subprocess Fix
    print("\n5. Testing Windows Subprocess Fix...")
    try:
        main_py = backend_path / "app/main.py"
        if main_py.exists():
            content = main_py.read_text()
            if "WindowsProactorEventLoopPolicy" in content:
                print("   ✅ Windows subprocess fix implemented")
                results.append(True)
            else:
                print("   ❌ Windows subprocess fix missing")
                results.append(False)
        else:
            print("   ❌ main.py not found")
            results.append(False)
    except Exception as e:
        print(f"   ❌ Error checking fix: {e}")
        results.append(False)
    
    # Test 6: Environment Configuration
    print("\n6. Testing Environment Configuration...")
    env_files = [
        dashboard_path / ".env.dev",
        dashboard_path / ".env.production",
        backend_path / "requirements.txt"
    ]
    
    env_missing = []
    for env_file in env_files:
        if env_file.exists():
            print(f"   ✅ {env_file.name} found")
        else:
            print(f"   ❌ {env_file.name} missing")
            env_missing.append(env_file.name)
    
    results.append(len(env_missing) == 0)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 VERIFICATION SUMMARY")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 CORE FUNCTIONALITY VERIFIED! System appears ready for testing.")
        print("\nNext Steps:")
        print("  1. Start backend server: cd dashboard/backend && uvicorn app.main:app --reload")
        print("  2. Start frontend server: cd dashboard/frontend && npm start")
        print("  3. Run end-to-end tests: python end_to_end_test.py")
        return True
    else:
        print("⚠️ Some verification checks failed. Review issues before proceeding.")
        return False

def main():
    """Main verification function"""
    return test_core_functionality()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
