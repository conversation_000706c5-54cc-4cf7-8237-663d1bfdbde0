// Test script to verify duration calculation fixes
// Run this in browser console or as a Node.js script

function calculateListDuration(startTime, endTime, status) {
  if (!startTime) return 'N/A';
  try {
    const start = new Date(startTime);
    if (isNaN(start.getTime())) return 'Invalid start';
    
    // For completed/failed/error processes, MUST use endTime to prevent ongoing calculation
    // For running processes, use current time to show live duration
    let end;
    const isCompleted = status === 'completed' || status === 'failed' || status === 'error';
    
    if (isCompleted) {
      if (endTime) {
        end = new Date(endTime);
        if (isNaN(end.getTime())) {
          console.warn(`Invalid end time ${endTime} for completed process, using start time + 1s`);
          end = new Date(start.getTime() + 1000); // Use start + 1 second as fallback
        }
      } else {
        // Completed process without end_time - this should not happen, but handle it
        console.warn(`Completed process ${status} without end_time, using start time + 1s as fallback`);
        end = new Date(start.getTime() + 1000);
      }
    } else {
      // Running or starting process - use current time for live updates
      end = new Date();
    }

    let durationMs = end.getTime() - start.getTime();
    
    // Handle negative durations gracefully
    if (durationMs < 0) {
      if (isCompleted) {
        console.warn(`Negative duration for completed process. Start: ${startTime}, End: ${endTime}. Using 1s.`);
        durationMs = 1000; // 1 second fallback
      } else {
        return 'Starting...'; // Process hasn't really started yet
      }
    }

    const totalSeconds = Math.floor(durationMs / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    if (hours > 0) return `${hours}h ${minutes}m ${seconds}s`;
    if (minutes > 0) return `${minutes}m ${seconds}s`;
    return `${seconds}s`;
  } catch (e) {
    console.error("Error calculating duration:", e, "Start:", startTime, "End:", endTime, "Status:", status);
    return "Calc err";
  }
}

// Test cases
console.log('=== Duration Calculation Tests ===');

const now = new Date();
const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);
const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

// Test 1: Running process (should use current time)
console.log('Test 1 - Running process:');
console.log(calculateListDuration(fiveMinutesAgo.toISOString(), null, 'running'));

// Test 2: Completed process with end time (should NOT use current time)
console.log('Test 2 - Completed process with end time:');
console.log(calculateListDuration(fiveMinutesAgo.toISOString(), now.toISOString(), 'completed'));

// Test 3: Completed process without end time (should use fallback)
console.log('Test 3 - Completed process without end time:');
console.log(calculateListDuration(fiveMinutesAgo.toISOString(), null, 'completed'));

// Test 4: Long running process
console.log('Test 4 - Long running process:');
console.log(calculateListDuration(oneHourAgo.toISOString(), null, 'running'));

// Test 5: Long completed process
console.log('Test 5 - Long completed process:');
console.log(calculateListDuration(oneHourAgo.toISOString(), fiveMinutesAgo.toISOString(), 'completed'));

console.log('=== Tests Complete ===');

// Export for use in React component testing
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { calculateListDuration };
}
