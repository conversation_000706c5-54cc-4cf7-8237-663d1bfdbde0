#!/usr/bin/env python3
import sys
import os
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

print("Testing server import...")
try:
    from app.main import app
    print("✅ Server import successful!")
    print(f"App: {app}")
    
    # Test the orchestrator import
    from app.services.simple_orchestrator import orchestrator
    print("✅ Orchestrator import successful!")
    print(f"Orchestrator: {orchestrator}")
    
    print("\n🔍 Testing process types:")
    process_types = orchestrator.get_process_types()
    for name, config in process_types.items():
        print(f"  - {name}: {config['description']}")
    
    print("\n✅ All imports and basic functionality work!")
    
except Exception as e:
    print(f"❌ Import failed: {e}")
    import traceback
    traceback.print_exc()
