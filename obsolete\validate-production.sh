#!/bin/bash

# Production Deployment Validation Script
# This script checks if the production environment is ready for deployment

echo "=== MaxPain2024 Production Deployment Validation ==="
echo "Version: 2.0 - Updated $(date '+%Y-%m-%d')"
echo ""

# Initialize validation status
VALIDATION_ERRORS=0
VALIDATION_WARNINGS=0

# Function to log errors
log_error() {
    echo "❌ ERROR: $1"
    ((VALIDATION_ERRORS++))
}

# Function to log warnings
log_warning() {
    echo "⚠️  WARNING: $1"
    ((VALIDATION_WARNINGS++))
}

# Function to log success
log_success() {
    echo "✅ $1"
}

# Function to check command availability
check_command() {
    if command -v "$1" &> /dev/null; then
        log_success "$1 is installed"
        return 0
    else
        log_error "$1 is not installed"
        return 1
    fi
}

# Load environment variables from .env file
echo "=== Environment Configuration ==="
if [ -f .env ]; then
    log_success ".env file found"
    source .env

    # Check critical environment variables
    if [ -n "$FRONTEND_PORT" ]; then
        log_success "FRONTEND_PORT is set to: $FRONTEND_PORT"
    else
        log_warning "FRONTEND_PORT not set, using default: 3080"
        FRONTEND_PORT=3080
    fi

    if [ -n "$BACKEND_PORT" ]; then
        log_success "BACKEND_PORT is set to: $BACKEND_PORT"
    else
        log_warning "BACKEND_PORT not set, using default: 8004"
        BACKEND_PORT=8004
    fi

    if [ -n "$DATABASE_URL" ]; then
        log_success "DATABASE_URL is configured"
    else
        log_error "DATABASE_URL is not set in .env file"
    fi
else
    log_error ".env file not found. Please create one from .env.example"
fi

echo ""

# Check if required directories exist
echo "=== Directory Structure Validation ==="
REQUIRED_DIRS=(
    "C:/output/MaxPain/logs"
    "C:/output/MaxPain"
    "dashboard"
    "dashboard/backend"
    "dashboard/frontend"
    "dashboard/nginx"
    "scripts"
    "logs"
)

for dir in "${REQUIRED_DIRS[@]}"; do
    if [ ! -d "$dir" ]; then
        log_error "Missing directory: $dir"
        echo "   Attempting to create directory..."
        mkdir -p "$dir" 2>/dev/null
        if [ $? -eq 0 ]; then
            log_success "Created: $dir"
        else
            log_error "Failed to create: $dir"
        fi
    else
        log_success "Directory exists: $dir"
    fi
done

echo ""

# Check Docker and Docker Compose
echo "=== Docker Environment Validation ==="
check_command "docker"
if [ $? -eq 0 ]; then
    if docker info &> /dev/null; then
        log_success "Docker daemon is running"

        # Check Docker version
        DOCKER_VERSION=$(docker --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' | head -1)
        log_success "Docker version: $DOCKER_VERSION"
    else
        log_error "Docker daemon is not running. Please start Docker Desktop."
    fi
fi

check_command "docker-compose"
if [ $? -eq 0 ]; then
    COMPOSE_VERSION=$(docker-compose --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' | head -1)
    log_success "Docker Compose version: $COMPOSE_VERSION"
fi

echo ""

# Validate Docker Compose files
echo "=== Docker Compose Configuration Validation ==="
if [ -f "docker-compose.yml" ]; then
    log_success "docker-compose.yml exists"
    if docker-compose -f docker-compose.yml config &> /dev/null; then
        log_success "docker-compose.yml is valid"
    else
        log_error "docker-compose.yml has validation errors"
        echo "   Running docker-compose config for details:"
        docker-compose -f docker-compose.yml config 2>&1 | head -10
    fi
else
    log_error "docker-compose.yml not found"
fi

if [ -f "docker-compose.dev.yml" ]; then
    log_success "docker-compose.dev.yml exists"
    if docker-compose -f docker-compose.dev.yml config &> /dev/null; then
        log_success "docker-compose.dev.yml is valid"
    else
        log_error "docker-compose.dev.yml has validation errors"
    fi
else
    log_error "docker-compose.dev.yml not found"
fi

# Check required files
echo "=== Essential Files Validation ==="
REQUIRED_FILES=(
    "docker-compose.yml"
    "docker-compose.dev.yml"
    "dashboard/backend/Dockerfile"
    "dashboard/backend/Dockerfile.dev"
    "dashboard/frontend/Dockerfile.prod"
    "dashboard/frontend/Dockerfile.dev"
    "dashboard/nginx/Dockerfile"
    "dashboard/nginx/nginx.conf.template"
    "dashboard/nginx/start-nginx.sh"
    "requirements.txt"
    "dashboard/frontend/package.json"
    "run.bat"
    "start-nginx.sh"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [ -f "$file" ]; then
        log_success "File exists: $file"

        # Additional file-specific checks
        case "$file" in
            "requirements.txt")
                if grep -q "fastapi" "$file" && grep -q "uvicorn" "$file"; then
                    log_success "requirements.txt contains essential packages"
                else
                    log_warning "requirements.txt may be missing essential packages"
                fi
                ;;
            "dashboard/frontend/package.json")
                if grep -q "react" "$file" && grep -q "react-scripts" "$file"; then
                    log_success "package.json contains essential React packages"
                else
                    log_warning "package.json may be missing essential packages"
                fi
                ;;
            "dashboard/nginx/start-nginx.sh")
                if [ -x "$file" ]; then
                    log_success "start-nginx.sh is executable"
                else
                    log_warning "start-nginx.sh is not executable"
                    chmod +x "$file" 2>/dev/null && log_success "Made start-nginx.sh executable"
                fi
                ;;
        esac
    else
        log_error "Missing file: $file"
    fi
done

echo ""

# Check application structure
echo "=== Application Structure Validation ==="
BACKEND_FILES=(
    "dashboard/backend/app"
    "dashboard/backend/app/main.py"
    "dashboard/backend/app/core"
    "dashboard/backend/app/tasks"
)

for item in "${BACKEND_FILES[@]}"; do
    if [ -e "$item" ]; then
        log_success "Backend component exists: $item"
    else
        log_error "Missing backend component: $item"
    fi
done

FRONTEND_FILES=(
    "dashboard/frontend/src"
    "dashboard/frontend/public"
    "dashboard/frontend/src/components"
)

for item in "${FRONTEND_FILES[@]}"; do
    if [ -e "$item" ]; then
        log_success "Frontend component exists: $item"
    else
        log_error "Missing frontend component: $item"
    fi
done

echo ""

# Check port availability
echo "=== Port Availability Check ==="
PORTS_TO_CHECK=($FRONTEND_PORT $BACKEND_PORT 6379 5432)

for port in "${PORTS_TO_CHECK[@]}"; do
    if command -v netstat &> /dev/null; then
        if netstat -an 2>/dev/null | grep ":$port " | grep LISTEN &> /dev/null; then
            log_warning "Port $port is already in use"
        else
            log_success "Port $port is available"
        fi
    elif command -v ss &> /dev/null; then
        if ss -tuln 2>/dev/null | grep ":$port " &> /dev/null; then
            log_warning "Port $port is already in use"
        else
            log_success "Port $port is available"
        fi
    else
        log_warning "Cannot check port $port (netstat/ss not available)"
    fi
done

echo ""

# Check system resources
echo "=== System Resources Check ==="
if command -v free &> /dev/null; then
    MEMORY_GB=$(free -g | awk '/^Mem:/{print $2}')
    if [ "$MEMORY_GB" -ge 4 ]; then
        log_success "System memory: ${MEMORY_GB}GB (sufficient)"
    else
        log_warning "System memory: ${MEMORY_GB}GB (may be insufficient for production)"
    fi
fi

if command -v df &> /dev/null; then
    DISK_USAGE=$(df -h . | awk 'NR==2{print $5}' | sed 's/%//')
    if [ "$DISK_USAGE" -lt 90 ]; then
        log_success "Disk usage: ${DISK_USAGE}% (sufficient space)"
    else
        log_warning "Disk usage: ${DISK_USAGE}% (low disk space)"
    fi
fi

echo ""

# Network connectivity check
echo "=== Network Connectivity Check ==="
if command -v ping &> /dev/null; then
    if ping -c 1 google.com &> /dev/null; then
        log_success "Internet connectivity available"
    else
        log_warning "Internet connectivity issues detected"
    fi
fi

# Check database connectivity (if DATABASE_URL is set)
if [ -n "$DATABASE_URL" ]; then
    echo "=== Database Connectivity Check ==="
    if command -v psql &> /dev/null; then
        if echo "SELECT 1;" | psql "$DATABASE_URL" &> /dev/null; then
            log_success "Database connection successful"
        else
            log_warning "Database connection failed (may be expected if DB is not running)"
        fi
    else
        log_warning "psql not available, cannot test database connection"
    fi
fi

echo ""

# Final validation summary
echo "=== Validation Summary ==="
echo "Errors: $VALIDATION_ERRORS"
echo "Warnings: $VALIDATION_WARNINGS"

if [ $VALIDATION_ERRORS -eq 0 ]; then
    echo ""
    log_success "Production environment validation PASSED!"
    echo ""
    echo "🚀 Ready to deploy! You can start the production environment with:"
    echo "   ./run.bat prod"
    echo "   or"
    echo "   docker-compose -f docker-compose.yml up -d --build"
    echo ""
    echo "📱 The application will be available at:"
    echo "   - Main application: http://localhost:$FRONTEND_PORT"
    echo "   - API documentation: http://localhost:$FRONTEND_PORT/docs"
    echo "   - API health check: http://localhost:$FRONTEND_PORT/health"
    echo "   - Backend API: http://localhost:$BACKEND_PORT"
    echo ""
    exit 0
else
    echo ""
    log_error "Production environment validation FAILED!"
    echo "   Please fix the $VALIDATION_ERRORS error(s) before deploying."
    if [ $VALIDATION_WARNINGS -gt 0 ]; then
        echo "   Also consider addressing the $VALIDATION_WARNINGS warning(s)."
    fi
    echo ""
    exit 1
fi
