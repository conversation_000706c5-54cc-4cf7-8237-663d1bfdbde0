#!/usr/bin/env python3
"""
Test script to verify the process history API endpoint
"""
import requests
import json
from config_utils import get_urls

def test_history_endpoint():
    """Test the /api/v1/processes/history endpoint"""
    urls = get_urls()
    try:
        url = f"{urls['backend']}/api/v1/processes/history"
        print(f"Testing endpoint: {url}")
        
        response = requests.get(url)
        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Response type: {type(data)}")
            print(f"Number of items: {len(data) if isinstance(data, list) else 'N/A'}")
            
            if isinstance(data, list) and len(data) > 0:
                print("First item structure:")
                first_item = data[0]
                for key, value in first_item.items():
                    print(f"  {key}: {type(value).__name__} = {value}")
                    
                # Check for the field we fixed
                if 'completed_at' in first_item:
                    print("\n✅ SUCCESS: 'completed_at' field found!")
                elif 'end_time' in first_item:
                    print("\n❌ ISSUE: Still using 'end_time' instead of 'completed_at'")
                else:
                    print("\n⚠️  WARNING: Neither 'completed_at' nor 'end_time' found")
            else:
                print("No process history data found (empty list)")
                
        else:
            print(f"Error response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ ERROR: Cannot connect to backend server. Is it running on port 8000?")
    except Exception as e:
        print(f"❌ ERROR: {e}")

if __name__ == "__main__":
    test_history_endpoint()
