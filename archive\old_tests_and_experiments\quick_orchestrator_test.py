#!/usr/bin/env python3
"""
Quick test to verify our Windows subprocess fixes are working
without starting the full FastAPI server
"""
import asyncio
import sys
import os
from pathlib import Path

# Add the backend to the path
backend_path = Path(__file__).parent / 'dashboard' / 'backend'
sys.path.insert(0, str(backend_path))

async def test_orchestrator_directly():
    """Test the orchestrator directly"""
    print("=== Direct Orchestrator Test ===")
    
    try:
        # Import after path is set
        from app.services.simple_orchestrator import ProcessOrchestratorService
        
        # Create orchestrator instance
        orchestrator = ProcessOrchestratorService()
        print("✅ Orchestrator created successfully")
        
        # Check event loop
        loop = asyncio.get_running_loop()
        print(f"Event loop type: {type(loop)}")
        
        # Test process start
        print("Starting update_index_options process...")
        task_id = await orchestrator.start_process('update_index_options', {})
        print(f"✅ Process started with task_id: {task_id}")
        
        # Wait and check status
        await asyncio.sleep(3)
        status = orchestrator.get_process_status(task_id)
        
        if status:
            print(f"Process status: {status['status']}")
            print(f"Process message: {status['message']}")
            
            if status['status'] == 'running':
                print("✅ Process is running - subprocess creation successful!")
            elif status['status'] == 'completed':
                print("✅ Process completed successfully!")
            elif status['status'] == 'failed':
                print(f"❌ Process failed: {status.get('error', 'Unknown error')}")
                if 'NotImplementedError' in str(status.get('error', '')):
                    print("🔍 This is the Windows subprocess issue we were fixing")
            else:
                print(f"Process in status: {status['status']}")
        else:
            print("❌ Could not retrieve process status")
            
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Testing Windows subprocess fixes in orchestrator...")
    print(f"Platform: {sys.platform}")
    
    try:
        result = asyncio.run(test_orchestrator_directly())
        if result:
            print("\n🎉 Test completed successfully!")
        else:
            print("\n💥 Test failed")
    except Exception as e:
        print(f"\n💥 Test crashed: {e}")
        import traceback
        traceback.print_exc()
