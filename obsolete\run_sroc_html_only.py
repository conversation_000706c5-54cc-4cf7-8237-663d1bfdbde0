#!/usr/bin/env python3
"""
Emergency runner for SROC_FS.py that forces HTML-only output.
Use this if kaleido keeps hanging and you need to get charts generated.
"""

import os
import sys

def main():
    """Run SROC_FS.py with PNG export disabled."""
    print("=" * 60)
    print("EMERGENCY SROC_FS RUNNER - HTML ONLY")
    print("=" * 60)
    print("This script runs SROC_FS.py with PNG export disabled.")
    print("All charts will be saved as interactive HTML files.")
    print("=" * 60)
    
    # Set environment variable to skip PNG
    os.environ['SKIP_PNG'] = '1'
    
    # Import and run the main script
    try:
        print("Starting SROC_FS.py with HTML-only export...")
        
        # Change to the script directory
        script_dir = os.path.dirname(os.path.abspath(__file__))
        os.chdir(script_dir)
        
        # Execute the main script
        exec(open('SROC_FS.py').read())
        
        print("\n" + "=" * 60)
        print("✓ SROC_FS.py completed successfully!")
        print("All charts saved as HTML files.")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ Error running SROC_FS.py: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
