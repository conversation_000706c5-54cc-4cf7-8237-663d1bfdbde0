annotated-types==0.7.0
anyio==4.9.0
asttokens==3.0.0
attrs==25.3.0
beautifulsoup4==4.13.4
bs4==0.0.2
cattrs==24.1.3
celery==5.4.0
certifi==2025.4.26
cffi==1.17.1
charset-normalizer==3.4.2
click==8.2.1
colorama==0.4.6
comm==0.2.2
cryptography==45.0.2
curl_cffi==0.11.1
debugpy==1.8.14
decorator==5.2.1
ecdsa==0.19.1
executing==2.2.0
fastapi==0.115.12
firecrawl-py
frozendict==2.4.6
greenlet==3.2.2
h11==0.16.0
idna==3.10
ipykernel==6.29.5
ipython==9.2.0
ipython_pygments_lexers==1.1.1
jedi==0.19.2
jupyter_client==8.6.3
jupyter_core==5.7.2
kaleido==0.1.0
lxml==5.4.0
matplotlib-inline==0.1.7
multitasking==0.0.11
narwhals==1.40.0
nest-asyncio==1.6.0
numpy==2.2.6
openpyxl
packaging==25.0
pandas==2.2.3
parso==0.8.4
peewee==3.18.1
platformdirs==4.3.8
plotly==6.1.1
prompt_toolkit==3.0.51
protobuf==6.31.0
psutil==7.0.0
psycopg2-binary==2.9.10
pure_eval==0.2.3
pyasn1==0.4.8
pycparser==2.22
pydantic==2.11.5
pydantic-settings==2.9.1
pydantic_core==2.33.2
Pygments==2.19.1
PyMySQL==1.1.1
pyrate-limiter==2.10.0
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
python-jose==3.4.0
pytz==2025.2
pyzmq==26.4.0
redis==6.1.0
requests==2.32.3
requests-cache==1.2.1
requests-ratelimiter==0.7.0
rsa==4.9.1
scipy==1.15.3
six==1.17.0
sniffio==1.3.1
soupsieve==2.7
SQLAlchemy==2.0.41
stack-data==0.6.3
starlette==0.46.2
ta==0.11.0
tornado==6.5.1
traitlets==5.14.3
typing-inspection==0.4.1
typing_extensions==4.13.2
tzdata==2025.2
url-normalize==2.2.1
urllib3==2.4.0
uvicorn==0.34.2
wcwidth==0.2.13
# websockets==15.0.1  # Handled explicitly in Dockerfile to avoid dependency conflicts
# websocket-client==1.8.0  # Handled explicitly in Dockerfile
trio==0.30.0
trio-websocket==0.12.2
outcome==1.3.0.post0
sortedcontainers==2.4.0
wsproto==1.2.0
PySocks==1.7.1
yfinance==0.2.61
# python-socketio==5.10.0  # REMOVED - Not used in codebase, causes websocket conflicts
aiofiles==0.8.0
selenium==4.33.0
webdriver-manager==4.0.2