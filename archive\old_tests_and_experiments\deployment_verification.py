#!/usr/bin/env python3
"""
Deployment Verification Script for HKEX Dashboard
Final checks before production deployment
"""

import sys
import os
import json
import subprocess
from pathlib import Path
from datetime import datetime

def check_system_requirements():
    """Check system requirements"""
    print("🔧 System Requirements Check")
    print("-" * 40)
    
    checks = []
    
    # Python version
    try:
        python_version = sys.version_info
        if python_version >= (3, 8):
            print(f"   ✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
            checks.append(True)
        else:
            print(f"   ❌ Python {python_version.major}.{python_version.minor} (requires 3.8+)")
            checks.append(False)
    except Exception as e:
        print(f"   ❌ Python version check failed: {e}")
        checks.append(False)
    
    # Node.js (for frontend)
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print(f"   ✅ Node.js {result.stdout.strip()}")
            checks.append(True)
        else:
            print("   ❌ Node.js not found")
            checks.append(False)
    except Exception:
        print("   ❌ Node.js not available")
        checks.append(False)
    
    return all(checks)

def check_dependencies():
    """Check Python dependencies"""
    print("\n📦 Dependencies Check")
    print("-" * 40)
    
    backend_path = Path("o:/Github/MaxPain/MaxPain2024/dashboard/backend")
    requirements_file = backend_path / "requirements.txt"
    
    if not requirements_file.exists():
        print("   ❌ requirements.txt not found")
        return False
    
    try:
        # Read requirements
        requirements = requirements_file.read_text().strip().split('\n')
        requirements = [req.strip() for req in requirements if req.strip() and not req.startswith('#')]
        
        print(f"   📋 Checking {len(requirements)} dependencies...")
        
        missing = []
        for req in requirements:
            package_name = req.split('==')[0].split('>=')[0].split('~=')[0]
            try:
                __import__(package_name.replace('-', '_'))
                print(f"   ✅ {package_name}")
            except ImportError:
                print(f"   ❌ {package_name} - MISSING")
                missing.append(package_name)
        
        if missing:
            print(f"\n   ⚠️ Missing packages: {', '.join(missing)}")
            print("   Run: pip install -r requirements.txt")
            return False
        else:
            print("   ✅ All dependencies satisfied")
            return True
            
    except Exception as e:
        print(f"   ❌ Error checking dependencies: {e}")
        return False

def check_configuration():
    """Check configuration files"""
    print("\n⚙️ Configuration Check")
    print("-" * 40)
    
    dashboard_path = Path("o:/Github/MaxPain/MaxPain2024/dashboard")
    
    config_files = [
        (".env.dev", "Development environment config"),
        (".env.production", "Production environment config"),
        ("docker-compose.yml", "Docker configuration"),
        ("backend/requirements.txt", "Backend dependencies"),
        ("frontend/package.json", "Frontend dependencies")
    ]
    
    checks = []
    for file_path, description in config_files:
        full_path = dashboard_path / file_path
        if full_path.exists():
            print(f"   ✅ {description}")
            checks.append(True)
        else:
            print(f"   ❌ {description} - {file_path} missing")
            checks.append(False)
    
    return all(checks)

def check_database_scripts():
    """Check database migration and setup scripts"""
    print("\n🗄️ Database Scripts Check")
    print("-" * 40)
    
    base_path = Path("o:/Github/MaxPain/MaxPain2024")
    
    db_scripts = [
        ("UpdateIndexOptionPostgres.py", "HKEX Index Options processor"),
        ("UpdateStockOptionReportPostgres.py", "Stock Options processor"),
        ("copyViewMultiDB.py", "Database view synchronization"),
        ("UpdateIndexOptionPostgres_wrapper.py", "Script wrapper")
    ]
    
    checks = []
    for script, description in db_scripts:
        script_path = base_path / script
        if script_path.exists():
            print(f"   ✅ {description}")
            checks.append(True)
        else:
            print(f"   ❌ {description} - {script} missing")
            checks.append(False)
    
    return all(checks)

def check_security():
    """Check security configuration"""
    print("\n🔒 Security Check")
    print("-" * 40)
    
    checks = []
    
    # Check for .env files with sensitive data
    dashboard_path = Path("o:/Github/MaxPain/MaxPain2024/dashboard")
    env_files = [".env.dev", ".env.production"]
    
    for env_file in env_files:
        env_path = dashboard_path / env_file
        if env_path.exists():
            content = env_path.read_text()
            
            # Check for placeholder values
            if "your_password_here" in content or "changeme" in content:
                print(f"   ⚠️ {env_file} contains placeholder values")
                checks.append(False)
            else:
                print(f"   ✅ {env_file} appears configured")
                checks.append(True)
        else:
            print(f"   ❌ {env_file} missing")
            checks.append(False)
    
    # Check for exposed secrets in git
    try:
        gitignore_path = Path("o:/Github/MaxPain/MaxPain2024/.gitignore")
        if gitignore_path.exists():
            gitignore_content = gitignore_path.read_text()
            if ".env" in gitignore_content:
                print("   ✅ .env files are gitignored")
                checks.append(True)
            else:
                print("   ⚠️ .env files may not be gitignored")
                checks.append(False)
        else:
            print("   ⚠️ .gitignore file missing")
            checks.append(False)
    except Exception as e:
        print(f"   ⚠️ Could not check .gitignore: {e}")
        checks.append(False)
    
    return all(checks)

def generate_deployment_report():
    """Generate deployment readiness report"""
    print("\n📋 Generating Deployment Report...")
    
    report = {
        "deployment_check": {
            "timestamp": datetime.now().isoformat(),
            "system": os.name,
            "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
            "checks": {}
        }
    }
    
    # Run all checks
    report["deployment_check"]["checks"]["system_requirements"] = check_system_requirements()
    report["deployment_check"]["checks"]["dependencies"] = check_dependencies()
    report["deployment_check"]["checks"]["configuration"] = check_configuration()
    report["deployment_check"]["checks"]["database_scripts"] = check_database_scripts()
    report["deployment_check"]["checks"]["security"] = check_security()
    
    # Save report
    report_path = Path("o:/Github/MaxPain/MaxPain2024/deployment_report.json")
    with open(report_path, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"   💾 Report saved to: {report_path}")
    
    return report

def main():
    """Main deployment verification"""
    print("🚀 HKEX Dashboard Deployment Verification")
    print("=" * 60)
    
    report = generate_deployment_report()
    checks = report["deployment_check"]["checks"]
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 DEPLOYMENT READINESS SUMMARY")
    print("=" * 60)
    
    passed_checks = sum(1 for check in checks.values() if check)
    total_checks = len(checks)
    
    for check_name, result in checks.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status}: {check_name.replace('_', ' ').title()}")
    
    print(f"\nOverall: {passed_checks}/{total_checks} checks passed")
    
    if passed_checks == total_checks:
        print("\n🎉 SYSTEM IS READY FOR DEPLOYMENT!")
        print("\nDeployment Steps:")
        print("  1. Configure production database connection")
        print("  2. Set up production environment variables")
        print("  3. Build frontend: cd dashboard/frontend && npm run build")
        print("  4. Start production servers")
        print("  5. Run end-to-end tests")
        return True
    else:
        print("\n⚠️ SYSTEM NOT READY FOR DEPLOYMENT")
        print("Please address the failed checks before deploying to production.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
