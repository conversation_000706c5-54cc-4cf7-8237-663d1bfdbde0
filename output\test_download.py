#!/usr/bin/env python3
"""
Manual test of HKEX download
"""

import requests
import os

# Download URL
url = 'https://www.hkex.com.hk/chi/services/trading/securities/securitieslists/ListOfSecurities_c.xlsx'
filename = 'ListOfSecurities_c.xlsx'

print(f"Downloading from: {url}")
print(f"Saving to: {filename}")

try:
    response = requests.get(url, timeout=30)
    response.raise_for_status()
    
    with open(filename, 'wb') as f:
        f.write(response.content)
    
    file_size = os.path.getsize(filename)
    print(f"✓ Download successful! File size: {file_size:,} bytes")
    
except Exception as e:
    print(f"✗ Error: {e}")
