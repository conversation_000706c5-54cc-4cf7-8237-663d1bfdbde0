DEBUG: Starting simple_orchestrator.py
DEBUG: Set Windows ProactorEventLoop policy for subprocess support
DEBUG: Imports completed
DEBUG: <PERSON><PERSON> created
DEBUG: About to create orchestrator instance
DEBUG: Current event loop: <class 'asyncio.windows_events.ProactorEventLoop'>
DEBUG: Orchestrator created successfully
INFO:     Started server process [52596]
INFO:     Waiting for application startup.
INFO:app.main:Starting HKEX Dashboard API server
INFO:app.main:Environment: development
INFO:app.main:Debug mode: True
INFO:app.main:Process orchestrator connected to WebSocket manager
INFO:     Application startup complete.
ERROR:    [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8000): [winerror 10048] only one usage of each socket address (protocol/network address/port) is normally permitted
INFO:     Waiting for application shutdown.
INFO:app.main:Shutting down HKEX Dashboard API server
INFO:     Application shutdown complete.
