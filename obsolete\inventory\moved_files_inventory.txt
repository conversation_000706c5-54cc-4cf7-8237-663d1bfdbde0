c:\dev\Maxpain2024\MaxPain2024\obsolete:
_dashboard_flow_.png
_web_app_architecture_.png
copyView.py
copyView_test.py
copyViewheroku.py
copyViewMultiDB_backup.py
copyViewMultiDB_fixed.py
copyViewNeon.py
CURRENT_STATUS.md
demo_dashboard.py*
DEV_ENVIRONMENT_FIX.md
DEVELOPMENT_ENVIRONMENT_TEST_RESULTS.md
docker-compose.dev.yml
docker-compose.dev.yml.bak
docker-compose.yml.bak
Dockerfile
Dockerfile.dev
FAVICON_IMPLEMENTATION_SUMMARY.md
FIXES_SUMMARY.md
flowchart LR.mmd
flowchart TD.mmd
inventory/
nginx.conf
Nginx_setup.md
nohup.out
precision_fix_analysis.ipynb
project-structure.txt
quick-start.sh*
README.md
requirements copy.txt
ROUNDING_FIX_SUMMARY.md
run.bat
sequenceDiagram.mmd
SROC_FS_fixed.py
start-app.sh*
start-backend-fixed.sh*
start-backend-test.bat
start-dev.bat
start-dev.sh*
start-nginx.sh*
start-prod.bat
start-prod.sh*
start-simple.sh*
test.py
test_dashboard_status.py*
test_database_persistence.py*
test_dtype_fix.py*
test_fixes.py*
test_history_api.py*
test_overflow_fix.py*
test_process_creation.py*
test_rounding_fix.py
test_sqlalchemy.py
test_websocket_connection.html
test2.py
test-backend-import.py*
test-dev-environment.sh*
test-docker-setup.py*
tests/
validate-production.sh*
WEBSOCKET_AND_CONFIG_FIXES_SUMMARY.md

c:\dev\Maxpain2024\MaxPain2024\obsolete/inventory:
moved_files_inventory.txt

c:\dev\Maxpain2024\MaxPain2024\obsolete/tests:
api_test.py*
async_test.py*
comprehensive_test.py*
final_verification.py*
final_verification_test.py*
final_windows_subprocess_test.py*
simple_asyncio_test.py
simple_test.py*
simple_verification_test.py*
test_400_error_debug.py*
test_api_fix.py
test_api_methods.py
test_backend_server.py*
test_backend_subprocess.py*
test_basic.py
test_complete_backend.py*
test_config_fix.py*
test_final_fix.py
test_import.py*
test_log_endpoint.py
test_log_fix.py
test_log_methods.py
test_orchestrator_detailed.py*
test_orchestrator_fix.py*
test_script_execution.py*
test_setup.py*
test_simple_400.py*
test_simple_backend.py*
test_subprocess.py*
test_syntax.py*
test_task.json
test_windows_subprocess_fix.py*
VERIFICATION_COMPLETE.md
\n---\nInventory updated on Tue, Jun  3, 2025  4:11:41 PM\n---\n
