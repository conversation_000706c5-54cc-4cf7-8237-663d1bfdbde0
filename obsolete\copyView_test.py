#%%
import pandas as pd
import os
import psycopg2
from dotenv import load_dotenv
import pymysql
from sqlalchemy import create_engine, text
load_dotenv()

local_db = create_engine(os.environ.get('WILL6700_DB'), isolation_level="AUTOCOMMIT",)
# db = os.environ.get('REMOTE_DATABASE')
db = os.environ.get('HEROKU_DATABASE_URL')
print(f"{db=}")
#db ="postgresql://vdpbbxpmswyyfo:<EMAIL>:5432/d27tclro6qeosh"
remote_db = create_engine(db)
#q= f"select * from public.test;"
#q=f"select txn_date from option_daily_report where txn_id = (select max(txn_id) from option_daily_report);"
#txn_date= pd.read_sql(q, local_db)
#%%
# Check Last Txn Date
# q_date=f"select max(txn_date) from v_index_option_value;"
# txn_date= pd.read_sql(q_date, local_db).iloc[0][0]

#%%
# option_value_tables =['v_index_option_value','v_weekly_option_value','v_stock_option_value','v_weekly_iv' ]
# option_value_tables =['v_index_option_value','v_weekly_option_value']
option_value_tables =['v_monthly_iv' ]
for t in option_value_tables:
    # Check Last Txn Date
    q_date=f"select max(txn_date) from {t};"
    txn_date= pd.read_sql(q_date, local_db).iloc[0][0]
    print(f'{t} => Last Txn Date= {txn_date}')
    q=f"select * from {t} where txn_date='{txn_date}';"
    df= pd.read_sql(q, local_db)
    print(q)
    print(f"Local Rows = {len(df)}")
    # Keep Last day's Txn Only due to Heroku Free Plan Limitation
    try:
        with remote_db.connect() as conn:
            trans = conn.begin()
            result = conn.execute(text(f'TRUNCATE TABLE {t};'))
            trans.commit()
    except Exception as e:
        print(f"Error Truncate table {t} =>" + str(e), flush = True)
    df.to_sql(name= t, con=remote_db, if_exists = 'append', index=False)    
    q_insert=f"select txn_date, count(*) from {t}  where txn_date>='{txn_date}' group by txn_date;"
    check_insert = pd.read_sql(q_insert, remote_db)
    print(q_insert)
    print(check_insert)

#%%
local_db.dispose()

# q=f"select * from v_weekly_option_value where txn_date>'{txn_date}';"
# df= pd.read_sql(q, local_db)
# df.to_sql(name='v_weekly_option_value', con=remote_db, if_exists = 'append', index=False)    
#df2= pd.read_sql(q, remote_db)

#%%
# q=f"select * from v_stock_option_value where txn_date>'{txn_date}';"
# df= pd.read_sql(q, local_db)
# df.to_sql(name='v_stock_option_value', con=remote_db, if_exists = 'append', index=False)    
#df2= pd.read_sql(q, remote_db)

#%%
