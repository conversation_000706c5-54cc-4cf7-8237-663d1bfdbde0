SELECT  substr(a.inst_name, 5,5) as cm<PERSON>h, left(a.inst_name, 3) as symb, \
a.txn_date as txn_date, a.stock_price, \
substr(a.inst_name,11,6)  as strike,  \
a.oi as coi, a.delta as cdelta, a.gamma as cgamma, a.close as c_close, \
a.gamma*a.oi as cnetgamma, a.delta*a.oi as cnetdelta,\
-1*b.oi as poi, -1*b.delta as pdelta, b.gamma as pgamma,  b.close as b_close,\
b.gamma*b.oi as pnetgamma, \
-1*b.delta*b.oi as pnetdelta, \
(a.gamma*a.oi + b.gamma*b.oi) as net_gamma, \
(a.delta*a.oi - b.delta*b.oi) as net_delta,\
a.oi_change as coi_change, \
-1*b.oi_change as poi_change \
FROM stock_option_report a, stock_option_report b \
where substr(a.inst_name,18,1) ='C' \
and substr(b.inst_name,18,1) ='P' \
and substr(a.inst_name,1,16)= substr(b.inst_name,1,16) \
and a. txn_date= b.txn_date \
and left(a.inst_name,3)='{name}' and a.txn_date='{txn_date}';


SELECT left(inst_name, 3) as hkats_code, 
txn_date,
sum(oi) as option_oi,
round(sum(oi*stock_price*delta)) as option_oi_stock_val,
sum(oi*close) as option_oi_val,
sum(volume) as option_volumne,
round(sum(volume*close)) as option_volumne_amt
FROM stock_option_report
where txn_date ='2023-08-25'
and  left(inst_name, 3) ='TCH'
group by left(inst_name, 3) , txn_date

SELECT left(inst_name, 3) as hkats_code, 
txn_date,
sum(oi) as option_oi,
avg(iv) as option_iv,
round(sum(oi*stock_price*delta*contract_size)) as option_oi_stock_val,
sum(oi*close*contract_size) as option_oi_val,
sum(volume) as option_volumne,
round(sum(volume*close*contract_size)) as option_volumne_amt
FROM stock_option_report as r, hkats_code as c
where txn_date ='2023-08-25'
and  left(inst_name, 3) ='TCH'
and left(inst_name, 3) = c.hkats_code
group by left(inst_name, 3) , txn_date

SELECT left(inst_name, 3) as hkats_code, 
txn_date,
sum(oi) as option_oi,
round(sum(oi*stock_price*delta*contract_size)) as option_oi_stock_val,
sum(oi*close*contract_size) as option_oi_val,
sum(volume) as option_volume,
round(sum(volume*close*contract_size)) as option_volume_amt
FROM stock_option_report as r, hkats_code as c
where txn_date ='2023-08-25'
and left(inst_name, 3) = c.hkats_code
group by left(inst_name, 3) , txn_date
order by option_volume_amt desc

CREATE MATERIALIZED VIEW option_daily_volume
AS
SELECT left(inst_name, 3) as hkats_code, 
txn_date,
sum(oi) as option_oi,
round(sum(oi*stock_price*delta*contract_size)) as option_oi_stock_val,
sum(oi*close*contract_size) as option_oi_val,
sum(volume) as option_volume,
round(sum(volume*close*contract_size)) as option_volume_amt
FROM stock_option_report as r, hkats_code as c
where left(inst_name, 3) = c.hkats_code
group by left(inst_name, 3) , txn_date
with data;

select hkats_code, round(avg(option_volume_amt)) 
from option_daily_volume
where hkats_code ='TCH'
group by hkats_code

-- Top option volume
select hkats_code, round(avg(option_volume_amt)) 
from option_daily_volume
group by hkats_code
order by 2 desc

REFRESH MATERIALIZED VIEW CONCURRENTLY tickets_view;
REFRESH MATERIALIZED VIEW view_name;


select

from option_daily_volume


select inst_name, iv
FROM stock_option_report
where txn_date ='2023-08-25'
and  left(inst_name, 3) ='TCH'

select left(inst_name, 3) as hkats_code, , avg(iv) as option_iv
FROM stock_option_report
where txn_date ='2023-08-25'
and  left(inst_name, 3) ='TCH'
and SUBSTRING(inst_name, 11,7)



select inst_name, iv, SUBSTRING(inst_name, 11,6) as strike
FROM stock_option_report
where txn_date ='2023-08-25'
and  left(inst_name, 3) ='TCH'
and cast(SUBSTRING(inst_name, 11,6) as decimal) between stock_price *0.95 and stock_price*1.05

select avg(iv)
FROM stock_option_report
where txn_date ='2023-08-25'
and  left(inst_name, 3) ='TCH'
and cast(SUBSTRING(inst_name, 11,6) as decimal) between stock_price *0.95 and stock_price*1.05

select txn_date, round(avg(iv),1) as option_iv
FROM stock_option_report
where left(inst_name, 3) ='JDH'
and cast(SUBSTRING(inst_name, 11,6) as decimal) between stock_price *0.95 and stock_price*1.05
and txn_date > '2023-08-01'
group by txn_date
order by 1


select left(inst_name, 3) as hkats_code, round(avg(iv),1) as option_iv
FROM stock_option_report
where cast(SUBSTRING(inst_name, 11,6) as decimal) between stock_price *0.95 and stock_price*1.05
and txn_date = '2023-08-25'
group by left(inst_name, 3) 
order by 2 desc

drop materialized view option_daily_iv;
CREATE MATERIALIZED VIEW option_daily_iv
as
select 
left(inst_name, 3) as hkats_code, 
txn_date, 
round(avg(stock_price),2) as stock_price, 
round(avg(iv),1) as option_iv
FROM stock_option_report
where cast(SUBSTRING(inst_name, 11,6) as decimal) between stock_price *0.95 and stock_price*1.05
group by txn_date, LEFT(inst_name, 3);

select *
from stock_option_report
where inst_name like '%,%'

delete
from stock_option_report
where SUBSTRING(inst_name, 11,6) = '1,000.'

delete
from stock_option_report
where inst_name like '%,%'

create view latest_option_daily_iv as
select * from option_daily_iv
where txn_date = (
    select MAX(txn_date) from option_daily_iv
)

select txn_date, stock_price,
ln(stock_price / (LAG(stock_price, 1) over (order by txn_date) )) as price_change,
LAG(stock_price, 1) over (order by txn_date) as prev_stock_price,
avg(stock_price) OVER (ORDER BY txn_date ASC ROWS 10 PRECEDING) ,
stddev(stock_price) OVER (ORDER BY txn_date ASC ROWS 10 PRECEDING) 
from option_daily_iv
where hkats_code ='TCH'
and txn_date > '2023-08-01'


select txn_date, stock_price,
ln(stock_price / (LAG(stock_price, 1) over (PARTITION BY hkats_code order by txn_date) )) as price_change,
LAG(stock_price, 1) over (order by txn_date) as prev_stock_price,
avg(stock_price) OVER (PARTITION BY hkats_code ORDER BY txn_date ASC ROWS 9 PRECEDING) ,
stddev(stock_price) OVER (PARTITION BY hkats_code ORDER BY txn_date ASC ROWS 9 PRECEDING) 
from option_daily_iv
where hkats_code ='JDC'
and txn_date > '2023-08-01'

select hkats_code, txn_date, stock_price,prev_stock_price, price_change,
(stddev(price_change) OVER (ORDER BY txn_date asc ROWS 9 PRECEDING) )*sqrt(246) as hv_10d,
(stddev(price_change) OVER (ORDER BY txn_date asc ROWS 19 PRECEDING) )*sqrt(246) as hv_20d,
(stddev(price_change) OVER (ORDER BY txn_date asc ROWS 29 PRECEDING) )*sqrt(246) as hv_30d
from 
(select hkats_code, txn_date, stock_price, 
 LAG(stock_price, 1) over (PARTITION BY hkats_code order by txn_date) as prev_stock_price,
 ln(stock_price / (LAG(stock_price, 1) over (PARTITION BY hkats_code order by txn_date) )) as price_change
from option_daily_iv
) as delta
where txn_date > '2023-08-01'
 and hkats_code ='JDC'

drop VIEW option_daily_hv;

create VIEW option_daily_hv as
select hkats_code, txn_date, 
(stddev(price_change) OVER (PARTITION BY hkats_code ORDER BY txn_date asc ROWS 9 PRECEDING) )*sqrt(246)*100 as hv_10d,
(stddev(price_change) OVER (PARTITION BY hkats_code ORDER BY txn_date asc ROWS 19 PRECEDING) )*sqrt(246)*100 as hv_20d,
(stddev(price_change) OVER (PARTITION BY hkats_code ORDER BY txn_date asc ROWS 29 PRECEDING) )*sqrt(246)*100 as hv_30d
from 
(select hkats_code, txn_date, ln(stock_price / (LAG(stock_price, 1) over (PARTITION BY hkats_code order by txn_date) )) as price_change
from option_daily_iv
) as delta;

-- Check HV of a stock
select * from option_daily_hv
where hkats_code ='JDC'
and txn_date > txn_date - interval '30 days'


## compare hv and iv
select iv.hkats_code, date_part('month', iv.txn_date), 
avg(option_iv) AS option_iv, 
avg(hv_10d) as hv_10d, avg(hv_20d) as hv_20d, avg(hv_30d) as hv_30d,
avg(option_iv - hv_10d) as iv_hv_10d, avg(option_iv - hv_20d) as iv_hv_20d, avg(option_iv - hv_30d) as iv_hv_30d
from option_daily_iv as iv, option_daily_hv as hv
where iv.hkats_code = hv.hkats_code
and iv.txn_date = hv.txn_date
/* and iv.txn_date > '2023-08-24' */
and iv.hkats_code ='CNC'
group by 1 ,2


-- IV should always be higher than HV
select iv.hkats_code, date_trunc('quarter', iv.txn_date), 
avg(option_iv) AS option_iv, 
avg(hv_10d) as hv_10d, avg(hv_20d) as hv_20d, avg(hv_30d) as hv_30d,
avg(option_iv - hv_10d) as iv_hv_10d, avg(option_iv - hv_20d) as iv_hv_20d, avg(option_iv - hv_30d) as iv_hv_30d
from option_daily_iv as iv, option_daily_hv as hv
where iv.hkats_code = hv.hkats_code
and iv.txn_date = hv.txn_date
and iv.hkats_code ='CNC'
group by 1

-- OPTION with highlt IV - HV difference
select iv.hkats_code, top.avg_daily_volume,
round( avg(option_iv),2) AS option_iv, 
round( avg(hv_10d),2) as hv_10d, 
round(avg(hv_20d),2) as hv_20d, 
round(avg(hv_30d),2) as hv_30d,
round(avg(option_iv - hv_10d),2) as iv_hv_10d, 
round(avg(option_iv - hv_20d),2) as iv_hv_20d, 
round(avg(option_iv - hv_30d),2) as iv_hv_30d
from option_daily_iv as iv, option_daily_hv as hv,
(select hkats_code, round(avg(option_volume_amt)) as avg_daily_volume
from option_daily_volume
WHERE txn_date >  '2023-08-01'
group by hkats_code
) as top
where iv.hkats_code = top.hkats_code
and iv.hkats_code = hv.hkats_code
and iv.txn_date = hv.txn_date
and iv.txn_date >  '2023-08-01'
group by 1,2
having round( avg(option_iv),2) > 20
ORDER BY 2 DESC



-- Index Options
select 
left(inst_name, 3) as hkats_code, 
txn_date, 
round(avg(stock_price),2) as stock_price, 
round(avg(iv),1) as option_iv
FROM option_daily_report
where cast(SUBSTRING(inst_name, 12,5) as decimal) between stock_price *0.95 and stock_price*1.05
and  txn_date = '2023-08-25'
group by txn_date, LEFT(inst_name, 3);


drop materialized view index_daily_iv;
CREATE MATERIALIZED VIEW index_daily_iv
as
select 
left(inst_name, 3) as hkats_code, 
txn_date, 
round(avg(stock_price),2) as stock_price, 
round(avg(iv),1) as option_iv
FROM option_daily_report
where cast(SUBSTRING(inst_name, 12,5) as decimal) between stock_price *0.95 and stock_price*1.05
group by txn_date, LEFT(inst_name, 3);

create VIEW index_daily_hv as
select hkats_code, txn_date, 
(stddev(price_change) OVER (PARTITION BY hkats_code ORDER BY txn_date asc ROWS 9 PRECEDING) )*sqrt(246)*100 as hv_10d,
(stddev(price_change) OVER (PARTITION BY hkats_code ORDER BY txn_date asc ROWS 19 PRECEDING) )*sqrt(246)*100 as hv_20d,
(stddev(price_change) OVER (PARTITION BY hkats_code ORDER BY txn_date asc ROWS 29 PRECEDING) )*sqrt(246)*100 as hv_30d
from 
(select hkats_code, txn_date, ln(stock_price / (LAG(stock_price, 1) over (PARTITION BY hkats_code order by txn_date) )) as price_change
from index_daily_iv
) as delta;
;

select iv.hkats_code, date_trunc('month', iv.txn_date),
round( avg(option_iv),2) AS option_iv, 
round( avg(hv_10d),2) as hv_10d, 
round(avg(hv_20d),2) as hv_20d, 
round(avg(hv_30d),2) as hv_30d,
round(avg(option_iv - hv_10d),2) as iv_hv_10d, 
round(avg(option_iv - hv_20d),2) as iv_hv_20d, 
round(avg(option_iv - hv_30d),2) as iv_hv_30d
from index_daily_iv as iv, index_daily_hv as hv
where iv.hkats_code = hv.hkats_code
and iv.option_iv >0
and iv.txn_date = hv.txn_date
--and iv.txn_date >  '2023-08-01'
group by 1,2
order by 1,2

select * from index_daily_iv where hkats_code='HSI'
and txn_date between '2022-10-01' and '2022-10-30'
order  by txn_date;

select to_char(DATE '2022-10-24', 'MON-YY');

select *
FROM option_daily_report
where cast(SUBSTRING(inst_name, 12,5) as decimal) between stock_price *0.99 and stock_price*1.09
and left(inst_name, 3) ='HSI'
and  substr(inst_name, 5,6) =  to_char(txn_date, 'MON-YY')
and txn_date ='2022-10-28'

-- Include only current month options and tighten strike price range to 1%
drop materialized view index_daily_iv cascade;
CREATE MATERIALIZED VIEW index_daily_iv
as
select 
left(inst_name, 3) as hkats_code, 
txn_date, 
round(avg(stock_price),0) as stock_price, 
round(avg(iv),1) as option_iv
FROM option_daily_report
where cast(SUBSTRING(inst_name, 12,5) as decimal) between stock_price *0.99 and stock_price*1.01
and  substr(inst_name, 5,6) =  to_char(txn_date, 'MON-YY')
group by 1,2;


create VIEW index_daily_hv as
select hkats_code, txn_date, 
(stddev(price_change) OVER (PARTITION BY hkats_code ORDER BY txn_date asc ROWS 9 PRECEDING) )*sqrt(246)*100 as hv_10d,
(stddev(price_change) OVER (PARTITION BY hkats_code ORDER BY txn_date asc ROWS 19 PRECEDING) )*sqrt(246)*100 as hv_20d,
(stddev(price_change) OVER (PARTITION BY hkats_code ORDER BY txn_date asc ROWS 29 PRECEDING) )*sqrt(246)*100 as hv_30d
from 
(select hkats_code, txn_date, ln(stock_price / (LAG(stock_price, 1) over (PARTITION BY hkats_code order by txn_date) )) as price_change
from index_daily_iv
) as delta;
;


select iv.hkats_code, iv.txn_date,
round( (option_iv),2) AS option_iv, 
round( (hv_10d),2) as hv_10d, 
round((hv_20d),2) as hv_20d, 
round((hv_30d),2) as hv_30d,
round((option_iv - hv_10d),2) as iv_hv_10d, 
round((option_iv - hv_20d),2) as iv_hv_20d, 
round((option_iv - hv_30d),2) as iv_hv_30d
from index_daily_iv as iv, index_daily_hv as hv
where iv.hkats_code = hv.hkats_code
and iv.option_iv >0
and iv.txn_date = hv.txn_date
and iv.txn_date > '2023-08-01'
and iv.hkats_code ='HSI'

select *
FROM option_daily_report
where cast(SUBSTRING(inst_name, 12,5) as decimal) between stock_price *0.99 and stock_price*1.01
and left(inst_name, 3) ='HSI'
and  substr(inst_name, 5,6) =  to_char(txn_date, 'MON-YY')
and txn_date >'2023-09-06'

-- Tighten strike price range to 2%
select *
FROM stock_option_report
where cast(SUBSTRING(inst_name, 11,6) as decimal) between stock_price *0.98 and stock_price*1.02
and  substr(inst_name, 5,5) =  to_char(txn_date, 'MONYY')
and txn_date ='2023-08-25'
-- and  left(inst_name, 3) ='TCH'

drop materialized view option_daily_iv cascade;
CREATE MATERIALIZED VIEW option_daily_iv
as
select 
left(inst_name, 3) as hkats_code, 
txn_date, 
round(avg(stock_price),2) as stock_price, 
round(avg(iv),1) as option_iv
FROM stock_option_report
where cast(SUBSTRING(inst_name, 11,6) as decimal) between stock_price *0.98 and stock_price*1.02
and  substr(inst_name, 5,5) =  to_char(txn_date, 'MONYY')
group by txn_date, LEFT(inst_name, 3);

-- Latest Stock Option IV Current Month
create view latest_option_iv as
select * from option_daily_iv
where txn_date = (
    select MAX(txn_date) from option_daily_iv
)

create VIEW option_daily_hv as
select hkats_code, txn_date, 
(stddev(price_change) OVER (PARTITION BY hkats_code ORDER BY txn_date asc ROWS 9 PRECEDING) )*sqrt(246)*100 as hv_10d,
(stddev(price_change) OVER (PARTITION BY hkats_code ORDER BY txn_date asc ROWS 19 PRECEDING) )*sqrt(246)*100 as hv_20d,
(stddev(price_change) OVER (PARTITION BY hkats_code ORDER BY txn_date asc ROWS 29 PRECEDING) )*sqrt(246)*100 as hv_30d
from 
(select hkats_code, txn_date, ln(stock_price / (LAG(stock_price, 1) over (PARTITION BY hkats_code order by txn_date) )) as price_change
from option_daily_iv
) as delta;


-- OPTION with highlt IV - HV difference
select * from option_daily_iv
where hkats_code ='ALB'
and txn_date > '2023-08-01'

select iv.hkats_code, iv.txn_date, 
top.daily_volume,
round( (option_iv),2) AS option_iv, 
round( (hv_10d),2) as hv_10d, 
round((hv_20d),2) as hv_20d, 
round((hv_30d),2) as hv_30d,
round((option_iv - hv_10d),2) as iv_hv_10d, 
round((option_iv - hv_20d),2) as iv_hv_20d, 
round((option_iv - hv_30d),2) as iv_hv_30d
from option_daily_iv as iv, option_daily_hv as hv,
(select hkats_code, txn_date, round((option_volume_amt)) as daily_volume
from option_daily_volume
WHERE txn_date >  '2023-08-01'
) as top
where iv.hkats_code = top.hkats_code
and iv.hkats_code = hv.hkats_code
and iv.txn_date = hv.txn_date
and iv.txn_date = top.txn_date
and iv.hkats_code='ALB'
ORDER BY 1,2 DESC




select iv.hkats_code, top.avg_daily_volume,
round( avg(option_iv),2) AS option_iv, 
round( avg(hv_10d),2) as hv_10d, 
round(avg(hv_20d),2) as hv_20d, 
round(avg(hv_30d),2) as hv_30d,
round(avg(option_iv - hv_10d),2) as iv_hv_10d, 
round(avg(option_iv - hv_20d),2) as iv_hv_20d, 
round(avg(option_iv - hv_30d),2) as iv_hv_30d
from option_daily_iv as iv, option_daily_hv as hv,
(select hkats_code, round(avg(option_volume_amt)) as avg_daily_volume
from option_daily_volume
WHERE txn_date >  '2023-08-01'
group by hkats_code
) as top
where iv.hkats_code = top.hkats_code
and iv.hkats_code = hv.hkats_code
and iv.txn_date = hv.txn_date
group by 1,2
having round( avg(option_iv),2) > 20
ORDER BY 2 DESC


select * 
from stock_sroc_fs s, hkats_code a
WHERE a.ticker=s.ticker

-- Latest Stock Option IV Current Month
select iv.hkats_code, iv.txn_date, 
top.daily_volume,
round( (option_iv),2) AS option_iv, 
round( (hv_10d),2) as hv_10d, 
round((hv_20d),2) as hv_20d, 
round((hv_30d),2) as hv_30d,
round((option_iv - hv_10d),2) as iv_hv_10d, 
round((option_iv - hv_20d),2) as iv_hv_20d, 
round((option_iv - hv_30d),2) as iv_hv_30d
from option_daily_iv as iv, option_daily_hv as hv,
(select hkats_code, txn_date, round((option_volume_amt)) as daily_volume
from option_daily_volume
WHERE txn_date = (select max(txn_date) from option_daily_volume)
) as top
where iv.hkats_code = top.hkats_code
and iv.hkats_code = hv.hkats_code
and iv.txn_date = hv.txn_date
and iv.txn_date = top.txn_date
ORDER BY 1,2 DESC

-- Add SROC_FS ranking
select iv.hkats_code, iv.txn_date, 
top.daily_volume,
round( (option_iv),2) AS option_iv, 
round( (hv_10d),2) as hv_10d, 
round((hv_20d),2) as hv_20d, 
round((hv_30d),2) as hv_30d,
round((option_iv - hv_10d),2) as iv_hv_10d, 
round((option_iv - hv_20d),2) as iv_hv_20d, 
round((option_iv - hv_30d),2) as iv_hv_30d,
s.p_beat_index_f,s.p_beat_index_s, s.avg_r_roc_f, s.avg_r_roc_s
from option_daily_iv as iv, option_daily_hv as hv,
(select hkats_code, txn_date, round((option_volume_amt)) as daily_volume
from option_daily_volume
WHERE txn_date = (select max(txn_date) from option_daily_volume)
) as top,
(select * 
from stock_sroc_fs s, hkats_code a
WHERE a.ticker=s.ticker)
as s
where iv.hkats_code = top.hkats_code
and iv.hkats_code = hv.hkats_code
and iv.hkats_code = s.hkats_code
and iv.txn_date = hv.txn_date
and iv.txn_date = top.txn_date
ORDER BY 1,2 DESC


select iv.hkats_code, iv.txn_date, 
top.daily_volume,
round( (option_iv),2) AS option_iv, 
round( (hv_10d),2) as hv_10d, 
round((hv_20d),2) as hv_20d, 
round((hv_30d),2) as hv_30d,
round((option_iv - hv_10d),2) as iv_hv_10d, 
round((option_iv - hv_20d),2) as iv_hv_20d, 
round((option_iv - hv_30d),2) as iv_hv_30d,
s.p_beat_index_f,s.p_beat_index_s, s.avg_r_roc_f, s.avg_r_roc_s
from option_daily_iv as iv, option_daily_hv as hv,
(select hkats_code, txn_date, round((option_volume_amt)) as daily_volume
from option_daily_volume
WHERE txn_date = (select max(txn_date) from option_daily_volume)
) as top,
(select * 
from stock_sroc_fs s, hkats_code a
WHERE a.ticker=s.ticker)
as s
where iv.hkats_code = top.hkats_code
and iv.hkats_code = hv.hkats_code
and iv.hkats_code = s.hkats_code
and iv.txn_date = hv.txn_date
and iv.txn_date = top.txn_date
ORDER BY 1,2 DESC

