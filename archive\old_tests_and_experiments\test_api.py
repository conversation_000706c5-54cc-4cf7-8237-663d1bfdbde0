#%%
#!/usr/bin/env python3
"""
Quick API test script to verify HKEX Dashboard backend functionality
"""
import requests
import json
import time

def test_api():
    base_url = "http://localhost:8000"
    
    print("🧪 Testing HKEX Dashboard API...")
      # Test 1: Get process types
    try:
        print("\n1️⃣ Testing process types endpoint...")
        response = requests.get(f"{base_url}/api/v1/processes/types")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Process types: {json.dumps(data, indent=2)}")
        else:
            print(f"❌ Failed: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 2: Get system health
    try:
        print("\n2️⃣ Testing system health endpoint...")
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ System health: {json.dumps(data, indent=2)}")
        else:
            print(f"❌ Failed: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 3: Get active processes
    try:
        print("\n3️⃣ Testing active processes endpoint...")
        response = requests.get(f"{base_url}/api/v1/processes/active")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Active processes: {json.dumps(data, indent=2)}")
        else:
            print(f"❌ Failed: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 4: Start a simple test process
    try:
        print("\n4️⃣ Testing process start endpoint...")
        payload = {
            "process_type": "update_index_options",
            "parameters": {
                "test_mode": True,
                "dry_run": True
            }
        }
        response = requests.post(f"{base_url}/api/v1/processes/start", json=payload)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Process started: {json.dumps(data, indent=2)}")
            
            # Monitor the process for a few seconds
            task_id = data.get('task_id')
            if task_id:
                print(f"\n🔍 Monitoring process {task_id}...")
                for i in range(5):
                    time.sleep(1)
                    try:
                        status_response = requests.get(f"{base_url}/api/v1/processes/{task_id}/status")
                        if status_response.status_code == 200:
                            status_data = status_response.json()
                            print(f"📊 Status update {i+1}: {status_data.get('status', 'unknown')} - {status_data.get('message', 'no message')}")
                        else:
                            print(f"⚠️ Status check failed: {status_response.status_code}")
                    except Exception as e:
                        print(f"⚠️ Status check error: {e}")
        else:
            print(f"❌ Failed: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print("\n🏁 API test completed!")
#%%
if __name__ == "__main__":
    test_api()
#%%
print("x")
# %%
