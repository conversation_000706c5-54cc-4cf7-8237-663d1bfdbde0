#!/usr/bin/env python3
"""
Test script execution using the same approach as the orchestrator
This will help us identify the exact error
"""

import asyncio
import os
import sys
from pathlib import Path

# Set Windows event loop policy
if os.name == 'nt':
    asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

async def test_script_execution():
    """Test the script execution with the same setup as orchestrator"""
    
    # Set up paths
    scripts_dir = Path(r"O:\Github\MaxPain\MaxPain2024")
    script_path = scripts_dir / "UpdateIndexOptionPostgres.py"
    
    print(f"Testing script: {script_path}")
    print(f"Scripts directory: {scripts_dir}")
    print(f"Script exists: {script_path.exists()}")
    
    # Test with help command first
    print("\n=== Testing Help Command ===")
    try:
        cmd_args = [sys.executable, str(script_path), "--help"]
        print(f"Command: {' '.join(cmd_args)}")
        
        process = await asyncio.create_subprocess_exec(
            *cmd_args,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.STDOUT,
            cwd=str(scripts_dir),
            env={**os.environ, 'PYTHONPATH': str(scripts_dir)}
        )
        
        # Read output with timeout
        try:
            stdout, _ = await asyncio.wait_for(process.communicate(), timeout=15.0)
            print(f"Return code: {process.returncode}")
            print(f"Output:\n{stdout.decode()}")
        except asyncio.TimeoutError:
            print("Help command timed out")
            process.kill()
            await process.wait()
            
    except Exception as e:
        print(f"Error in help command: {e}")
        import traceback
        traceback.print_exc()
    
    # Test with dry run
    print("\n=== Testing Dry Run ===")
    try:
        cmd_args = [sys.executable, str(script_path), "--dry-run", "--date", "2024-12-20"]
        print(f"Command: {' '.join(cmd_args)}")
        
        process = await asyncio.create_subprocess_exec(
            *cmd_args,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.STDOUT,
            cwd=str(scripts_dir),
            env={**os.environ, 'PYTHONPATH': str(scripts_dir)}
        )
        
        # Read output line by line with timeout
        try:
            line_count = 0
            start_time = asyncio.get_event_loop().time()
            timeout = 30.0
            
            while True:
                try:
                    line = await asyncio.wait_for(process.stdout.readline(), timeout=5.0)
                    if not line:
                        break
                    line_str = line.decode().strip()
                    print(f"[{line_count:03d}] {line_str}")
                    line_count += 1
                    
                    # Check overall timeout
                    if asyncio.get_event_loop().time() - start_time > timeout:
                        print("Overall timeout reached")
                        break
                        
                except asyncio.TimeoutError:
                    print("Line read timeout - checking if process is still running")
                    if process.returncode is not None:
                        print(f"Process finished with code: {process.returncode}")
                        break
                    continue
            
            await process.wait()
            print(f"Final return code: {process.returncode}")
            
        except Exception as e:
            print(f"Error reading output: {e}")
            process.kill()
            await process.wait()
            
    except Exception as e:
        print(f"Error in dry run: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("Starting async script execution test...")
    asyncio.run(test_script_execution())
    print("Test completed.")
