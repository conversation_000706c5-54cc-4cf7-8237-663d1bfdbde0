"""
Test Your Specific Failing URL

This script tests the exact URL that was failing in your original error.
"""

import os
import sys
from pathlib import Path

# Add the scripts directory to Python path
script_dir = Path(__file__).parent
sys.path.append(str(script_dir))

# Set required environment variables for testing
os.environ['out_path'] = str(script_dir / 'output') + '/'
os.environ['LOG_LEVEL'] = '30'  # WARNING level
os.environ['SQL_ECHO'] = '0'
os.environ['WILL9700_DB'] = 'postgresql://dummy:dummy@localhost/dummy'  # Dummy DB for testing

# Import the enhanced functions
from UpdateStockOptionReportPostgres import safe_http_get_with_firecrawl_fallback

def test_your_failing_url():
    """Test the exact URL that was failing in your error message"""
    print("🎯 Testing Your Specific Failing URL")
    print("=" * 60)
    
    # This is the exact URL from your error message
    failing_url = "https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/dqe250616.htm"
    
    print(f"URL: {failing_url}")
    print(f"This was the URL that was timing out in your original error.")
    print()
    
    try:
        print("🔄 Testing with enhanced fallback method...")
        
        # Use the enhanced method with fallback
        # Using shorter timeout to force fallback activation
        response = safe_http_get_with_firecrawl_fallback(
            failing_url,
            timeout=10  # Short timeout to trigger fallback
        )
        
        if response and response.status_code == 200:
            print(f"✅ SUCCESS! The failing URL now works!")
            print(f"📏 Content length: {len(response.content)} bytes")
            
            # Check if it's a valid HKEX report
            content_str = response.text.lower() if hasattr(response, 'text') else str(response.content).lower()
            
            if any(keyword in content_str for keyword in ['stock option', 'hkex', 'hong kong exchange', 'daily market report', 'derivatives']):
                print(f"✅ Content appears to be a valid HKEX stock option report")
                
                # Show some key indicators
                if 'stock option' in content_str:
                    print("   - Contains 'stock option' ✓")
                if 'hkex' in content_str:
                    print("   - Contains 'HKEX' ✓")
                if 'derivatives' in content_str:
                    print("   - Contains 'derivatives' ✓")
                    
                return True
            else:
                print(f"⚠️  Content may not be a valid HKEX report")
                # Show first few lines for debugging
                lines = response.text.split('\n')[:10] if hasattr(response, 'text') else str(response.content).split('\\n')[:10]
                print("First 10 lines of content:")
                for i, line in enumerate(lines, 1):
                    print(f"{i:2}: {line[:100]}")
                return False
        else:
            print(f"❌ FAILED: Still cannot fetch the URL")
            if response:
                print(f"Status code: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🧪 Testing Your Specific Failing URL")
    print("=" * 60)
    
    success = test_your_failing_url()
    
    print("\n" + "=" * 60)
    print("🏁 RESULT")
    print("=" * 60)
    
    if success:
        print("🎉 SUCCESS!")
        print("✅ Your originally failing URL now works with Firecrawl fallback")
        print("✅ The timeout issue has been resolved")
        print()
        print("💡 What happened:")
        print("   1. Direct HTTP was attempted first")
        print("   2. When it timed out, Firecrawl automatically took over")
        print("   3. Firecrawl successfully fetched the content")
        print("   4. Your script can now continue processing normally")
        print()
        print("🚀 Next steps:")
        print("   1. Your main script should now work without timeout issues")
        print("   2. The fallback will activate automatically when needed")
        print("   3. No changes needed to your existing workflow")
    else:
        print("❌ FAILED")
        print("The URL is still not accessible. This could be because:")
        print("   1. The report for 2025-06-16 doesn't exist yet")
        print("   2. HKEX hasn't published it")
        print("   3. There's a network issue")
        print()
        print("💡 Try testing with a known working URL:")
        print("   python scripts/test_firecrawl_fallback.py")

if __name__ == "__main__":
    main()
