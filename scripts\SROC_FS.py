#%%
import warnings
warnings.simplefilter(action='ignore', category=FutureWarning)
from plotly.subplots import make_subplots
import plotly.graph_objects as go
import plotly.io as pio
import sqlalchemy
from sqlalchemy import create_engine, text
import yfinance as yf
import pandas as pd
import ta
import time
from datetime import datetime
import os
import subprocess
import requests
from dotenv import load_dotenv
load_dotenv()

pd.options.mode.chained_assignment = None

# ================================================================================
# Chart Export Configuration
# ================================================================================
print("📊 Chart Export Configuration:")
print(f"   HTML Export: Always enabled (interactive charts)")
print(f"   PNG Export: {'Enabled' if os.environ.get('DISABLE_PNG', '0') != '1' else 'Disabled'}")
print(f"   JPEG Export: {'Enabled' if os.environ.get('DISABLE_PNG', '0') != '1' else 'Disabled'}")
print(f"   SVG Export: {'Enabled' if os.environ.get('SAVE_SVG', '0') == '1' else 'Disabled'}")
print(f"   Image Timeout: 30 seconds")
print("   Set DISABLE_PNG=1 to disable image exports")
print("   Set SAVE_SVG=1 to enable SVG exports")
print("================================================================================\n")

def download_hkex_securities_list(url, output_path):
    """
    Download the latest HKEX securities list from the official website.

    Args:
        url (str): URL to download the XLS file from
        output_path (str): Directory path where to save the file

    Returns:
        bool: True if download successful, False otherwise
    """
    try:
        filename = 'ListOfSecurities_c.xlsx'
        file_path = os.path.join(output_path, filename)

        print(f"Downloading HKEX securities list from: {url}")
        print(f"Saving to: {file_path}")

        # Download the file
        response = requests.get(url, timeout=30)
        response.raise_for_status()  # Raise an exception for bad status codes

        # Save the file
        with open(file_path, 'wb') as f:
            f.write(response.content)

        print(f"✓ Successfully downloaded HKEX securities list: {file_path}")
        return True

    except requests.exceptions.RequestException as e:
        print(f"✗ Failed to download HKEX securities list: {e}")
        return False
    except Exception as e:
        print(f"✗ Error saving HKEX securities list: {e}")
        return False

def save_plotly_figure(fig, file_path_without_ext, ticker):
    """
    Enhanced function to save Plotly figure in both HTML and image formats.

    This function now saves charts in multiple formats:
    1. HTML (interactive, always works)
    2. PNG (static image, named by ticker)
    3. Additional formats if requested

    Args:
        fig: Plotly figure object
        file_path_without_ext: Full path without file extension
        ticker: Ticker symbol for logging and naming

    Returns:
        dict: Dictionary with paths of successfully saved files
    """
    saved_files = {}

    # Always save HTML first (most reliable)
    print(f"📊 Saving {ticker} chart in multiple formats...")

    # Method 1: HTML (always first, most reliable)
    try:
        html_path = f"{file_path_without_ext}.html"
        print(f"   → Saving HTML (interactive)...")
        fig.write_html(html_path, include_plotlyjs='cdn')
        saved_files['html'] = html_path
        print(f"✓ HTML saved: {html_path}")
    except Exception as e:
        print(f"✗ Failed to save HTML: {type(e).__name__}: {e}")

    # Method 2: PNG (static image, named by ticker)
    png_disabled = os.environ.get('DISABLE_PNG', '0') == '1'
    if not png_disabled:
        try:
            # Create a clean ticker name for filename (remove .HK suffix and special chars)
            clean_ticker = ticker.replace('.HK', '').replace('.', '_')

            # Create images directory if it doesn't exist
            images_dir = os.path.dirname(file_path_without_ext) + '/images'
            os.makedirs(images_dir, exist_ok=True)

            # Save PNG with ticker name
            png_path = f"{images_dir}/{clean_ticker}.png"
            print(f"   → Saving PNG image as {clean_ticker}.png...")

            # Use timeout to prevent hanging (cross-platform approach)
            import threading

            def save_png_with_timeout():
                """Save PNG in a separate thread to allow timeout"""
                try:
                    fig.write_image(png_path, width=1920, height=1080, format='png')
                    return True
                except Exception as e:
                    print(f"PNG thread error: {e}")
                    return False

            # Create and start thread for PNG export
            png_thread = threading.Thread(target=save_png_with_timeout)
            png_thread.daemon = True
            png_thread.start()

            # Wait for completion with timeout
            png_thread.join(timeout=30)  # 30 second timeout

            if png_thread.is_alive():
                print(f"⚠️  PNG export timed out for {ticker} - continuing without image")
            elif os.path.exists(png_path):
                saved_files['png'] = png_path
                print(f"✓ PNG saved: {png_path}")
            else:
                print(f"⚠️  PNG export failed for {ticker} - file not created")

        except Exception as e:
            print(f"⚠️  PNG export failed for {ticker}: {type(e).__name__}: {e}")
            print(f"   (This is common due to kaleido issues - HTML version is still available)")
    else:
        print(f"   → PNG export disabled for {ticker} (DISABLE_PNG=1)")

    # Method 3: JPEG (alternative image format)
    # if not png_disabled:
    #     try:
    #         # Only attempt JPEG if PNG was successful (same engine)
    #         if 'png' in saved_files:
    #             clean_ticker = ticker.replace('.HK', '').replace('.', '_')
    #             images_dir = os.path.dirname(file_path_without_ext) + '/images'
    #             jpeg_path = f"{images_dir}/{clean_ticker}.jpg"
    #             print(f"   → Saving JPEG image as {clean_ticker}.jpg...")
    #             fig.write_image(jpeg_path, width=1920, height=1080, format='jpeg')
    #             saved_files['jpeg'] = jpeg_path
    #             print(f"✓ JPEG saved: {jpeg_path}")
    #     except Exception as e:
    #         print(f"⚠️  JPEG export failed for {ticker}: {type(e).__name__}: {e}")

    # Method 4: SVG (vector format, if requested)
    # svg_enabled = os.environ.get('SAVE_SVG', '0') == '1'
    # if svg_enabled:
    #     try:
    #         clean_ticker = ticker.replace('.HK', '').replace('.', '_')
    #         images_dir = os.path.dirname(file_path_without_ext) + '/images'
    #         svg_path = f"{images_dir}/{clean_ticker}.svg"
    #         print(f"   → Saving SVG image as {clean_ticker}.svg...")
    #         fig.write_image(svg_path, width=1920, height=1080, format='svg')
    #         saved_files['svg'] = svg_path
    #         print(f"✓ SVG saved: {svg_path}")
    #     except Exception as e:
    #         print(f"⚠️  SVG export failed for {ticker}: {type(e).__name__}: {e}")

    # Summary
    if saved_files:
        print(f"✓ Successfully saved {ticker} chart in {len(saved_files)} format(s): {list(saved_files.keys())}")
        return saved_files
    else:
        print(f"✗ Failed to save {ticker} chart in any format")
        return None

date_fmt = '%Y-%m-%d'
start_date = '2023-01-27'
print(f"{start_date=}")

# Get and fix output path for Windows compatibility
out_path = os.getenv('out_path', './output/')
if out_path.startswith('/') and os.name == 'nt':
    # Convert Unix-style path to Windows path relative to current directory
    out_path = out_path.strip('/')
    out_path = os.path.join(os.getcwd(), out_path)

# Ensure output path ends with separator
if not out_path.endswith(os.sep):
    out_path += os.sep

print(f"Output path: {out_path}")

# convert start_date to YYMMDD
# start_date = datetime.strptime(start_date, date_fmt).strftime('%y%m%d')
pathname = out_path + f"SROCFS_OUTPUT_{datetime.strptime(start_date, date_fmt).strftime('%y%m%d')}/"
today = datetime.today()
d1 = today.strftime("%y%m%d")
#Create directory if not exist
d1_path = f'{pathname}{d1}'
os.makedirs(d1_path, exist_ok=True)
print("Direcotry Created: ", d1_path)

#%%
# Download lastest xls from https://www.hkex.com.hk/chi/services/trading/securities/securitieslists/ListOfSecurities_c.xlsx
hkex_url = 'https://www.hkex.com.hk/chi/services/trading/securities/securitieslists/ListOfSecurities_c.xlsx'

# Download the HKEX securities list
download_success = download_hkex_securities_list(hkex_url, out_path.rstrip(os.sep))
if not download_success:
    print("⚠️  Failed to download HKEX securities list. Please check your internet connection.")
    print("   The script will continue but ticker name lookup may not work properly.")

# Load the ticker file
try:
    ticker_file_path = os.path.join(out_path.rstrip(os.sep), 'ListOfSecurities_c.xlsx')
    ticker_file = pd.read_excel(ticker_file_path, skiprows=0, header=2)
    print(f"✓ Successfully loaded HKEX securities list: {len(ticker_file)} securities")
    ticker_file['ticker_code'] = ticker_file['股份代號'].astype(str).str.zfill(4) + '.HK'
    ticker_dict = ticker_file.set_index('ticker_code')['股份名稱'].to_dict()
    print(f"✓ Created ticker dictionary with {len(ticker_dict)} entries")
except Exception as e:
    print(f"✗ Failed to load ticker file: {e}")
    print("   Creating empty ticker dictionary. Ticker names will not be available.")
    ticker_dict = {}
# ticker_file['股份名稱']
#%%
# dft = pd.read_csv(pathname+'temp.csv', header=None)
tickers_file_path = os.path.join(out_path.rstrip(os.sep), 'tickers.csv')
dft = pd.read_csv(tickers_file_path, header=None)
print(f"File read {tickers_file_path}: {len(dft)} rows")
dft.head()
# Index DF Benchmark 2021-02-18 Recent Index Peak
d0 = datetime.strptime(start_date, date_fmt)
iticker ='^HSI'
roc_f=10    # ROC Period
roc_s=20
ema_n = 10 # EMA Period

# Prepare Index Benchmark Data
dfi= yf.download( iticker, start=d0)  
dfi=dfi[~dfi.index.duplicated(keep='first')  ]
dfi.columns=dfi.columns.levels[0]
dfi.columns=dfi.columns.str.replace(' ', '_').str.lower()
dfi['roc_f'] = ta.momentum.ROCIndicator(dfi.close, window=roc_f).roc()
dfi['roc_s'] = ta.momentum.ROCIndicator(dfi.close, window=roc_s).roc()
dfi['sroc_f'] = ta.trend.EMAIndicator(dfi.roc_f, window=ema_n).ema_indicator()
dfi['sroc_s'] = ta.trend.EMAIndicator(dfi.roc_s, window=ema_n).ema_indicator()
dfi['i_sroc_f'] = dfi['sroc_f']
dfi['i_sroc_s'] = dfi['sroc_s']
# 0 relative to itself
dfi['r_roc_s'] = dfi['r_sroc_f'] = 0
dfi['ticker'] = iticker
dfi['ticker_name'] = iticker
# ### B Band 
bb_n=10
dev=2
b_band = ta.volatility.BollingerBands(dfi.close, window=bb_n, window_dev=dev)
dfi['high_band'] = b_band.bollinger_hband().round(0)
dfi['mid_band'] = b_band.bollinger_mavg().round(0)
dfi['low_band'] = b_band.bollinger_lband().round(0)
dfi['close_PREV'] = dfi.close.shift(1)
dfi['under_low_band'] = dfi.apply(lambda x: 1 if (x.low < x.low_band) else 0,axis=1)
dfi['over_high_band'] = dfi.apply(lambda x: 1 if (x.high > x.high_band) else 0,axis=1)


#%%
df_sum=[]
# tickerStrings = [['2015.HK'], ['0270.HK'], ['0116.HK']]
# tickerStrings = [['3047.HK']]
# dft=pd.DataFrame(tickerStrings)
# for r in tickerStrings:
for i, r in dft.sort_values(by=0, ascending=False).iterrows():
    try:
        ticker = r[0]
        # Check not found
        try:
            ticker_name=ticker_dict[ticker]
        except:
            ticker_name=ticker
            continue        
        # df = yf.download(ticker, group_by="Ticker", start=d0)   
        df = yf.download(ticker, start=d0)   
        df=df[~df.index.duplicated(keep='first')  ]
        df['ticker'] = ticker  # add this column because the dataframe doesn't contain a column with the ticker
        df['ticker_name'] = ticker_name
        df.columns=df.columns.levels[0]
        df.columns=df.columns.str.replace(' ', '_').str.lower()
        # Adjust Open Hi Low
        # df['open'] = df['open'] + df['adj_close']-df['close']
        # df['high'] = df['high'] + df['adj_close']-df['close']
        # df['low'] = df['low'] + df['adj_close']-df['close']
        # 240905 Use Adj closed to compare ex-div effect 
        # df['close'] = df['adj_close']
        df['i_sroc_f'] = dfi.sroc_f
        df['i_roc_f'] = dfi.roc_f
        df['i_roc_s'] = dfi.roc_s
        df['i_sroc_s'] = dfi.sroc_s
        df['roc_f'] = ta.momentum.ROCIndicator(df.close, window=roc_f).roc()
        df['roc_s'] = ta.momentum.ROCIndicator(df.close, window=roc_s).roc()
        df['sroc_f'] = ta.trend.EMAIndicator(df.roc_f, window=ema_n).ema_indicator()
        df['sroc_s'] = ta.trend.EMAIndicator(df.roc_s, window=ema_n).ema_indicator()
        df['r_roc_f'] = df.roc_f-df.i_roc_f
        df['r_roc_s'] = df.roc_s-df.i_roc_s
        df['r_sroc_f'] = ta.trend.EMAIndicator(df.r_roc_f, window=ema_n).ema_indicator()
        df['r_sroc_s'] = ta.trend.EMAIndicator(df.r_roc_s, window=ema_n).ema_indicator()
        # ### B Band 
        # bb_n=20
        # dev=2
        b_band = ta.volatility.BollingerBands(df.close, window=bb_n, window_dev=dev)
        df['high_band'] = b_band.bollinger_hband().round(2)
        df['mid_band'] = b_band.bollinger_mavg().round(2)
        df['low_band'] = b_band.bollinger_lband().round(2)
        df['close_PREV'] = df.close.shift(1)
        df['under_low_band'] = df.apply(lambda x: 1 if (x.low < x.low_band) else 0,axis=1)
        df['over_high_band'] = df.apply(lambda x: 1 if (x.high > x.high_band) else 0,axis=1)
        # df['long'] = (df.close <= df.low_band) # & (df.close_PREV > df.low_band)
        # df['long_price'] = df.low_band
        # df['short'] = (df.close >= df.high_band) # & (df.close_PREV < df.high_band)
        # df['short_price'] = df.high_band

        # Add latest row to df_sum
        # symb_sum = df.iloc[-1]
        # symb_sum = df.iloc[-1]
        symb_sum = df[['ticker', 'ticker_name', 'close', 'high_band', 'mid_band', 'low_band', 'under_low_band', 'over_high_band']].iloc[-1]
        symb_sum.at['txn_date']=df.index[-1]
        symb_sum.at['p_beat_index_f']=round((len(df[df.roc_f>df.i_roc_f])/len(df)), 2)
        p_beat_index_s=round((len(df[df.roc_s>df.i_roc_s])/len(df)), 2)
        symb_sum.at['p_beat_index_s']=p_beat_index_s
        symb_sum.at['avg_r_roc_f']=round( df.r_roc_f.mean(), 2)
        symb_sum.at['avg_r_roc_f_20']=round( df.r_roc_f.tail(20).mean(), 2)
        symb_sum.at['avg_r_roc_s']=round( df.r_roc_s.mean(), 2)
        symb_sum.at['avg_r_roc_s_20']=round( df.r_roc_s.tail(20).mean(), 2)
        df_sum.append(symb_sum)    
        # p_beatIndex=len(df[df.roc>df.i_roc])/len(df)
        # avg_r_roc= df.r_roc.mean()

        # Create traces
        fig = make_subplots(rows=3, cols=1,
                        shared_xaxes=True,
                        vertical_spacing=0.01)
        fig.add_trace(go.Ohlc(x=df.index,
                            name=ticker_name,
                            open=df['open'],
                            high=df['high'],
                            low=df['low'],
                            close=df['close']),row=1, col=1)
        fig.add_trace(go.Scatter(x=df.index, y=df.high_band,
                            line_dash="dot", line_color="blue", line_width=1,
                            name=f'BBand Hi'),row=1, col=1)
        fig.add_trace(go.Scatter(x=df.index, y=df.mid_band,
                            mode='lines', line_color='blue',
                            name=f'BBand Mid'),row=1, col=1)
        fig.add_trace(go.Scatter(x=df.index, y=df.low_band,
                            line_dash="dot", line_color="blue",line_width=1,
                            name=f'BBand Lo'),row=1, col=1)
        fig.add_hline(y=df['close'][0], 
            annotation_text=f"{ticker} Open@{d0.strftime(date_fmt)} = {round(df['close'][0],2)}", annotation_position="bottom right",
            line_width=1, line_dash="dot", line_color="blue", row=1, col=1)                        
        fig.update_xaxes(rangeslider= {'visible':False}, row=1, col=1)   
        # fig 2                         
        fig.add_trace(go.Ohlc(x=dfi.index,
                            name=iticker, 
                            open=dfi['open'],
                            high=dfi['high'],
                            low=dfi['low'],
                            close=dfi['close']),row=2, col=1)
        fig.add_hline(y=dfi['close'][0], 
            annotation_text=f"{iticker} Open@{d0.strftime(date_fmt)} = {round(dfi['close'][0])}", annotation_position="bottom right",
            line_width=1, line_dash="dot", line_color="green", row=2, col=1)  
        fig.add_trace(go.Scatter(x=dfi.index, y=dfi.high_band,
                            line_dash="dot", line_color="green",line_width=1,
                            name=f'BBand Hi'),row=2, col=1)
        fig.add_trace(go.Scatter(x=dfi.index, y=dfi.mid_band,
                            mode='lines', line_color='green',
                            name=f'BBand Mid'),row=2, col=1)
        fig.add_trace(go.Scatter(x=dfi.index, y=dfi.low_band,
                            line_dash="dot", line_color="green",line_width=1,
                            name=f'BBand Lo'),row=2, col=1)
        fig.update_xaxes(rangeslider= {'visible':False}, row=2, col=1)    
        # fig 3
        fig.add_trace(go.Scatter(x=df.index, y=df.r_sroc_s,
                            mode='lines', line_color='red',
                            name=f'Relative SROC {roc_s}d'),row=3, col=1)
        fig.add_trace(go.Scatter(x=df.index, y=df.r_sroc_f,
                            line_dash="dot", line_color="red",
                            name=f'Relative SROC {roc_f}d'),row=3, col=1)
        fig.add_trace(go.Scatter(x=df.index, y=df.i_sroc_s,
                            mode='lines', line_color="green", line_width=1,
                            name=f'{iticker} SROC_S'),row=3, col=1)
        # fig.add_trace(go.Scatter(x=df.index, y=df.sroc_f,
        #                     line_dash="dot", line_color='blue',
        #                     name=f'{ticker} SROC_F'),row=3, col=1)
        fig.add_trace(go.Scatter(x=df.index, y=df.sroc_s,
                            mode='lines', line_color='blue', line_width=1,
                            name=f'{ticker} SROC_S'),row=3, col=1)
        # fig.add_trace(go.Scatter(x=df.index, y=df.roc_s,
        #                     mode='markers', marker_color = 'blue', 
        #                     marker_size = 3, opacity=0.5,
        #                     name=f'{ticker} ROC'),row=3, col=1)
        # fig.add_trace(go.Scatter(x=df.index, y=df.i_roc_s,
        #                     mode='markers', marker_color = 'green',
        #                     marker_size = 3, opacity=0.5,
        #                     name=f'{iticker} ROC'),row=3, col=1)
        fig.add_hline(y=0, line_width=1, line_color="black", row=3, col=1)
        # Layout
        if p_beat_index_s>0.5:
            fig_bgcolor='rgba(76,175,80,0.3)'
        else:
            fig_bgcolor='rgba(244,63,54,0.3)'
        fig.update_layout(   
            plot_bgcolor=fig_bgcolor,
            width=1920,
            height=1080,
            title={
                'text': f"{ticker}({ticker_name}) vs {iticker} ({d0.strftime(date_fmt)} - {today.strftime(date_fmt)}) " +\
                f"Days above {iticker} {roc_f}d/{roc_s}d ={symb_sum.at['p_beat_index_f']*100:.2f}%/{symb_sum.at['p_beat_index_s']*100:.2f}%    " + \
                f"Avg Relative ROC {roc_f}d/{roc_s}d ={symb_sum.at['avg_r_roc_f']:.2f}%/{symb_sum.at['avg_r_roc_s']:.2f}%]",
                # 'y':0.88,
                # 'x':0.18,
                'xanchor': 'left',
                'yanchor': 'top'},
                title_font_color="red",
            xaxis3_title = 'Date',
            yaxis1_title = ticker ,
            yaxis2_title = iticker,
            yaxis3_title = 'ROC %',            
            xaxis=dict(tickformat="%y-%m"),
            legend=dict(
                yanchor="bottom",
                y=0.01,
                xanchor="left",
                x=0.001))
        fig.update_xaxes(dtick="M1",tickformat="%b\n%Y")

        # Save figure using robust method with fallbacks
        try:
            saved_files = save_plotly_figure(fig, f'{d1_path}/{d1}_{ticker}', ticker)
            if saved_files:
                print(f"Chart saved successfully in {len(saved_files)} format(s): {list(saved_files.keys())}")
                # Log specific file paths
                for format_type, file_path in saved_files.items():
                    print(f"  {format_type.upper()}: {file_path}")
            else:
                print(f"Failed to save chart for {ticker} in any format")
        except Exception as e:
            print(f"Failed to save chart for {ticker}: {e}")
            # Continue with next ticker even if chart save fails

        # fig.show()
        # break
        time.sleep(10)
    except Exception as e:
        print(f'{ticker}-{iticker}:{type(e).__name__} at line {e.__traceback__.tb_lineno} of {__file__}: {e}')
        # continue
        break
    # break
# %%

# %%
symb_sum = dfi[['ticker', 'ticker_name','close', 'high_band', 'mid_band', 'low_band', 'under_low_band', 'over_high_band']].iloc[-1]
symb_sum.at['txn_date']=dfi.index[-1]
symb_sum.at['p_beat_index_f']=0.5
symb_sum.at['p_beat_index_s']=0.5
symb_sum.at['avg_r_roc_f']=0
symb_sum.at['avg_r_roc_f_20']=0
symb_sum.at['avg_r_roc_s']=0
symb_sum.at['avg_r_roc_s_20']=0

df_sum.append( symb_sum)
df_sum=pd.DataFrame(df_sum)
df_sum
# %%
os.makedirs(f'{pathname}xls', exist_ok=True)
print("Direcotry Created: ",f'{pathname}xls')
xls_path= f'{pathname}xls/{d1}_{roc_f}_{roc_s}_summary.xlsx'
df_sum.to_excel(xls_path, index=False)
print(f'XLS Saved: {xls_path}')
# %%
# Write to DB
db = os.environ.get('WILL9700_DB')
print(f"{db=}")
try:
    remote_db = create_engine(db)
    tname='stock_sroc_fs'
    # Truncate table if exists
    with remote_db.connect() as conn:
        sql_results = conn.execute(text(f"DELETE FROM {tname}"))
        conn.commit()
        print(f"{tname} Rows deleted: {sql_results.rowcount}")
    df_sum.to_sql(name= tname, con=remote_db, if_exists = 'append', index=False)    
except Exception as e:
    print(f'Failed Writing to {db=}: {e}')
# %%
