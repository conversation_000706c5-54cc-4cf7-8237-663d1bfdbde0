#!/usr/bin/env python3
"""
Simple WebSocket test script
"""

import asyncio
import websockets
import json
from datetime import datetime

async def test_websocket():
    """Test WebSocket connection"""
    uri = "ws://localhost:8004/ws"
    print(f"Connecting to: {uri}")
    
    try:
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket connection established!")
            
            # Send a ping message
            test_message = {
                "type": "ping",
                "timestamp": datetime.now().isoformat()
            }
            
            await websocket.send(json.dumps(test_message))
            print("📤 Sent ping message")
            
            # Wait for response
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                response_data = json.loads(response)
                print("📥 Received response:")
                print(f"   Type: {response_data.get('type', 'unknown')}")
                print("✅ WebSocket test PASSED!")
                return True
                
            except asyncio.TimeoutError:
                print("⚠️  No response received, but connection worked")
                return True
                
    except Exception as e:
        print(f"❌ WebSocket test FAILED: {e}")
        return False

if __name__ == "__main__":
    result = asyncio.run(test_websocket())
    exit(0 if result else 1)
