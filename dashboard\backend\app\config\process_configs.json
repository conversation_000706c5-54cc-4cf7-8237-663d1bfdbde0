{"scripts": {"update_index_options": {"script": "UpdateIndexOptionPostgres.py", "description": "Update Index Option", "timeout": 1800, "requires_params": [], "optional_params": ["txn_date", "dry_run", "batch_size"], "param_mapping": {"txn_date": "--date", "dry_run": "--dry-run", "batch_size": "--batch-size"}}, "update_stock_options": {"script": "UpdateStockOptionReportPostgres.py", "description": "Update Stock Option", "timeout": 3600, "requires_params": [], "optional_params": ["txn_date", "dry_run", "batch_size"], "param_mapping": {"txn_date": "--date", "dry_run": "--dry-run", "batch_size": "--batch-size"}}, "copy_view_multidb": {"script": "copyViewMultiDB.py", "description": "Publish to multiple databases", "timeout": 2700, "requires_params": [], "optional_params": ["source_db", "target_db", "view_names", "dry_run"], "param_mapping": {"source_db": "--source", "target_db": "--target", "view_names": "--views", "dry_run": "--dry-run"}}, "SROC_FS": {"script": "SROC_FS.py", "description": "Analytical Charts using SROC indicator", "timeout": 1800, "requires_params": [], "optional_params": [], "param_mapping": {}}, "hello_world": {"script": "hello_world.py", "description": "Hello World Test Script", "timeout": 1800, "requires_params": [], "optional_params": ["message", "delay", "steps", "dry_run"], "param_mapping": {"message": "--message", "delay": "--delay", "steps": "--steps", "dry_run": "--dry-run"}}}, "metadata": {"version": "1.0.0", "last_updated": "2024-12-19T00:00:00Z", "description": "Configuration file for HKEX Dashboard script management"}}