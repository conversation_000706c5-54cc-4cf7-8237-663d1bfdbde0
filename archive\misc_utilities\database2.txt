drop table storacle.t_northwater_5day_moneyflow;
CREATE TABLE `storacle.t_northwater_5day_moneyflow` (
  `ticker` varchar(20),
  `stock_name` varchar(60),
  `close` varchar(20),
  `change%` varchar(20),
  `d1` varchar(20),
  `d2` varchar(20),
  `d3` varchar(20),
  `d4` varchar(20),
  `d5` varchar(20),
  `5day_total` varchar(20),
  `1day_ratio` varchar(20),
  `5day_ratio` varchar(20),
  `txn_date` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

select count(*) from storacle.t_northwater_5day_moneyflow where txn_date= '2021-09-03';


SELECT *, left(instm_name,3) as symb, CONVERT(REGEXP_REPLACE(profit,',',''),float) as net_profit FROM storacle.monthly_open_contracts;

SELECT *, left(instm_name,3) as symb, CONVERT(REGEXP_REPLACE(premium,',',''),float) as net_premium
FROM storacle.daily_option_trades;


INSERT INTO daily_stock_price (ticker,txn_date,close, hkats_code) 
select ticker,to_date(txn_date,'dd/mm/yyyy'),close, ticker from daily_stock_price_update
ON CONFLICT (ticker,txn_date) DO UPDATE 
SET close = EXCLUDED.close;

select * from daily_stock_price where txn_date = to_date('04/02/2022','dd/mm/yyyy');

select symb, cmonth,
sum(coi_share - poi_share) as "oi_share" ,
sum(coi_delta_value - poi_delta_value) as "delta_value" ,
sum(coi_option_value - poi_option_value) as "option_value" 
from v_stock_option_value
where txn_date = '2022-02-18' and right(cmonth,2) ='22'
group by symb, cmonth





delete from stock_option_report
where inst_name like '%,%';


drop VIEW option_daily_hv;

create VIEW option_daily_hv as
select hkats_code, txn_date, 
(stddev(price_change) OVER (PARTITION BY hkats_code ORDER BY txn_date asc ROWS 9 PRECEDING) )*sqrt(246)*100 as hv_10d,
(stddev(price_change) OVER (PARTITION BY hkats_code ORDER BY txn_date asc ROWS 19 PRECEDING) )*sqrt(246)*100 as hv_20d,
(stddev(price_change) OVER (PARTITION BY hkats_code ORDER BY txn_date asc ROWS 29 PRECEDING) )*sqrt(246)*100 as hv_30d
from 
(select hkats_code, txn_date, ln(stock_price / (LAG(stock_price, 1) over (PARTITION BY hkats_code order by txn_date) )) as price_change
from option_daily_iv
) as delta;

REFRESH MATERIALIZED VIEW option_daily_volume;
REFRESH MATERIALIZED VIEW option_daily_iv;




drop materialized view index_daily_iv;
CREATE MATERIALIZED VIEW index_daily_iv
as
select 
left(inst_name, 3) as hkats_code, 
txn_date, 
round(avg(stock_price),2) as stock_price, 
round(avg(iv),1) as option_iv
FROM option_daily_report
where cast(SUBSTRING(inst_name, 12,5) as decimal) between stock_price *0.95 and stock_price*1.05
group by txn_date, LEFT(inst_name, 3);

REFRESH MATERIALIZED VIEW index_daily_iv CONCURRENTLY;

DROP INDEX IF EXISTS public.idx_index_daily_iv;

CREATE unique INDEX IF NOT EXISTS idx_index_daily_iv
    ON public.index_daily_iv USING btree
    (txn_date DESC NULLS FIRST, hkats_code COLLATE pg_catalog."default" ASC NULLS LAST)
    WITH (deduplicate_items=True)
    TABLESPACE pg_default;


drop VIEW index_daily_hv;
create VIEW index_daily_hv as
select hkats_code, txn_date, 
(stddev(price_change) OVER (PARTITION BY hkats_code ORDER BY txn_date asc ROWS 9 PRECEDING) )*sqrt(246)*100 as hv_10d,
(stddev(price_change) OVER (PARTITION BY hkats_code ORDER BY txn_date asc ROWS 19 PRECEDING) )*sqrt(246)*100 as hv_20d,
(stddev(price_change) OVER (PARTITION BY hkats_code ORDER BY txn_date asc ROWS 29 PRECEDING) )*sqrt(246)*100 as hv_30d
from 
(select hkats_code, txn_date, ln(stock_price / (LAG(stock_price, 1) over (PARTITION BY hkats_code order by txn_date) )) as price_change
from index_daily_iv
) as delta;


select iv.hkats_code,
round( avg(option_iv),2) AS option_iv, 
round( avg(hv_10d),2) as hv_10d, 
round(avg(hv_20d),2) as hv_20d, 
round(avg(hv_30d),2) as hv_30d,
round(avg(option_iv - hv_10d),2) as iv_hv_10d, 
round(avg(option_iv - hv_20d),2) as iv_hv_20d, 
round(avg(option_iv - hv_30d),2) as iv_hv_30d
from index_daily_iv as iv, index_daily_hv as hv
where iv.hkats_code = hv.hkats_code
and iv.txn_date = hv.txn_date
and iv.txn_date >  '2023-08-01'
group by 1


drop materialized view option_daily_iv cascade;

CREATE MATERIALIZED VIEW option_daily_iv
as
select 
left(inst_name, 3) as hkats_code, 
txn_date, 
round(avg(stock_price),2) as stock_price, 
round(avg(iv),1) as option_iv
FROM stock_option_report
where cast(SUBSTRING(inst_name, 11,6) as decimal) between stock_price *0.98 and stock_price*1.02
and  substr(inst_name, 5,5) =  to_char(txn_date, 'MONYY')
group by txn_date, LEFT(inst_name, 3);

DROP INDEX IF EXISTS public.idx_option_daily_iv;

CREATE unique INDEX IF NOT EXISTS idx_option_daily_iv
    ON public.option_daily_iv USING btree
    (txn_date DESC NULLS FIRST, hkats_code COLLATE pg_catalog."default" ASC NULLS LAST)
    WITH (deduplicate_items=True)
    TABLESPACE pg_default;


create view latest_option_daily_iv as
select * from option_daily_iv
where txn_date = (
    select MAX(txn_date) from option_daily_iv
)

CREATE MATERIALIZED VIEW option_daily_volume
AS
SELECT left(inst_name, 3) as hkats_code, 
txn_date,
sum(oi) as option_oi,
round(sum(oi*stock_price*delta*contract_size)) as option_oi_stock_val,
sum(oi*close*contract_size) as option_oi_val,
sum(volume) as option_volume,
round(sum(volume*close*contract_size)) as option_volume_amt
FROM stock_option_report as r, hkats_code as c
where left(inst_name, 3) = c.hkats_code
group by left(inst_name, 3) , txn_date
with data;

REFRESH MATERIALIZED VIEW CONCURRENTLY option_daily_volume;

DROP INDEX IF EXISTS public.idx_option_daily_volume;

CREATE unique INDEX IF NOT EXISTS idx_option_daily_volume
    ON public.option_daily_volume USING btree
    (txn_date DESC NULLS FIRST, hkats_code COLLATE pg_catalog."default" ASC NULLS LAST)
    WITH (deduplicate_items=True)
    TABLESPACE pg_default;


create view v_stock_screener as
select iv.hkats_code, iv.txn_date, 
top.daily_volume,
round( (option_iv),2) AS option_iv, 
round( (hv_10d),2) as hv_10d, 
round((hv_20d),2) as hv_20d, 
round((hv_30d),2) as hv_30d,
round((option_iv - hv_10d),2) as iv_hv_10d, 
round((option_iv - hv_20d),2) as iv_hv_20d, 
round((option_iv - hv_30d),2) as iv_hv_30d,
s.p_beat_index_f,s.p_beat_index_s, s.avg_r_roc_f, s.avg_r_roc_s
from option_daily_iv as iv, option_daily_hv as hv,
(select hkats_code, txn_date, round((option_volume_amt)) as daily_volume
from option_daily_volume
WHERE txn_date = (select max(txn_date) from option_daily_volume)
) as top,
(select * 
from stock_sroc_fs s, hkats_code a
WHERE a.ticker=s.ticker)
as s
where iv.hkats_code = top.hkats_code
and iv.hkats_code = hv.hkats_code
and iv.hkats_code = s.hkats_code
and iv.txn_date = hv.txn_date
and iv.txn_date = top.txn_date
ORDER BY 1,2 DESC    