#!/bin/bash
# Quick Manual Startup for HKEX Dashboard

echo "🚀 Starting HKEX Dashboard..."
echo "======================================"

# Start backend
echo "📡 Starting backend server..."
cd backend
python -m uvicorn app.main:app --host 127.0.0.1 --port 8000 --reload &
BACKEND_PID=$!
echo "Backend started with PID: $BACKEND_PID"

# Wait for backend to start
sleep 3

# Check if backend is running
if curl -s http://localhost:8000/api/v1/ > /dev/null 2>&1; then
    echo "✅ Backend is running successfully!"
else
    echo "⚠️  Backend may still be starting..."
fi

echo ""
echo "📊 Dashboard Status:"
echo "  - Backend:  http://localhost:8000"
echo "  - API Docs: http://localhost:8000/docs"
echo "  - Frontend: Start with 'cd frontend && npm start'"
echo ""
echo "🛑 To stop: kill $BACKEND_PID"
echo "📋 Backend PID saved to backend.pid"

echo $BACKEND_PID > ../backend.pid

# Keep script running
echo "Press Ctrl+C to stop the backend..."
wait $BACKEND_PID
