#!/usr/bin/env python3
"""
Simple test to manually check if the server endpoints work.
This will test the import and basic functionality.
"""
import sys
import os
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

def test_app_import():
    """Test if we can import the FastAPI app"""
    try:
        print("🔍 Testing FastAPI app import...")
        from app.main import app
        print("✅ FastAPI app imported successfully")
        
        # Check if the app has the expected endpoints
        routes = [route.path for route in app.routes]
        print(f"📋 Available routes: {len(routes)}")
        for route in routes:
            print(f"  - {route}")
            
        # Check for the log-tail endpoint specifically
        log_tail_routes = [r for r in routes if 'logs' in r and 'tail' in r]
        if log_tail_routes:
            print(f"✅ Log-tail endpoint found: {log_tail_routes}")
        else:
            print("❌ Log-tail endpoint not found!")
            
        return True
    except Exception as e:
        print(f"❌ Error importing app: {e}")
        return False

def test_orchestrator_directly():
    """Test the orchestrator directly"""
    try:
        print("\n🔍 Testing orchestrator import...")
        from app.services.simple_orchestrator import SimpleOrchestrator
        
        orchestrator = SimpleOrchestrator()
        print("✅ Orchestrator created successfully")
        
        # Test the methods that were failing
        print("\n🔍 Testing log methods...")
        result = orchestrator.get_log_tail("test_task", 10)
        print(f"get_log_tail result: {result}")
        
        result = orchestrator.get_process_logs("test_task")
        print(f"get_process_logs result: {result}")
        
        result = orchestrator.get_active_processes()
        print(f"get_active_processes result: {result}")
        
        print("✅ All orchestrator methods working!")
        return True
    except Exception as e:
        print(f"❌ Error with orchestrator: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Simple Backend Test")
    print("=" * 40)
    
    app_ok = test_app_import()
    orchestrator_ok = test_orchestrator_directly()
    
    print("\n" + "=" * 40)
    if app_ok and orchestrator_ok:
        print("✅ All tests passed! Backend should work correctly.")
    else:
        print("❌ Some tests failed. Check the errors above.")
