sequenceDiagram
    participant User as User (<PERSON><PERSON>er)
    participant Frontend as React Frontend
    participant API as FastAPI Backend
    participant WS as WebSocket Manager
    participant Orch as Process Orchestrator
    participant DB as PostgreSQL Database
    participant FS as File System (Logs)
    participant <PERSON><PERSON><PERSON> as External Python Scripts

    Note over User, Script: Dashboard Initialization Flow
    
    User->>Frontend: Load Dashboard
    Frontend->>API: GET /api/v1/ (Health Check)
    API-->>Frontend: API Status & Version
    
    Frontend->>API: GET /api/v1/processes/types
    API->>Orch: get_process_types()
    Orch-->>API: Available Process Types
    API-->>Frontend: Process Types Configuration
    
    Frontend->>WS: WebSocket Connect ws://localhost:8000/ws
    WS->>WS: Generate client_id
    WS-->>Frontend: Connection Established
    
    Note over User, Script: Process Start Flow
    
    User->>Frontend: Start Process (select type & params)
    Frontend->>API: POST /api/v1/processes/start
    Note right of API: {process: "type", parameters: {...}}
    
    API->>Orch: start_process(process_type, parameters)
    Orch->>DB: Create ProcessExecution record
    DB-->>Orch: task_id generated
    
    Orch->>FS: Create log files (stdout/stderr)
    Orch->>Script: Start subprocess with parameters
    Script-->>Orch: Process started
    
    Orch->>WS: broadcast_process_update(task_id, process_data)
    WS-->>Frontend: WebSocket message (process_update)
    Frontend->>Frontend: Update UI state
    
    API-->>Frontend: {task_id, message: "Process started"}
    Frontend->>Frontend: Add to active processes list
    
    Note over User, Script: Real-time Process Monitoring
    
    loop Process Execution
        Script->>FS: Write output to log files
        Orch->>Orch: Monitor process output
        Orch->>Orch: _estimate_progress(line, line_count)
        Orch->>WS: broadcast_process_update(status, progress, message)
        WS-->>Frontend: Real-time updates
        Frontend->>Frontend: Update progress bars & status
    end
    
    Note over User, Script: Process Status Queries
    
    User->>Frontend: Request Process Status
    Frontend->>API: GET /api/v1/processes/{task_id}/status
    API->>Orch: get_process_status(task_id)
    Orch-->>API: Process status data
    API-->>Frontend: Current status & progress
    
    Note over User, Script: Log Viewing Flow
    
    User->>Frontend: View Full Logs
    Frontend->>API: GET /api/v1/processes/{task_id}/log-full
    API->>Orch: get_full_log_content(task_id)
    Orch->>FS: Read stdout & stderr log files
    FS-->>Orch: Log content
    Orch-->>API: Combined log content
    API-->>Frontend: Full log data
    Frontend->>Frontend: Display in RealTimeLogViewer
    
    Note over User, Script: Process Completion Flow
    
    Script->>Script: Process completes/fails
    Orch->>Orch: Detect process termination
    Orch->>DB: Update ProcessExecution (end_time, status, return_code)
    Orch->>WS: broadcast_process_update(final_status)
    WS-->>Frontend: Process completion notification
    Frontend->>Frontend: Update UI (move to history)
    
    Note over User, Script: Process History & Monitoring
    
    User->>Frontend: View Process History
    Frontend->>API: GET /api/v1/processes/history
    API->>Orch: get_process_history()
    Orch->>DB: Query ProcessExecution table
    DB-->>Orch: Historical process records
    Orch-->>API: Formatted history data
    API-->>Frontend: Process history list
    
    User->>Frontend: View Active Processes
    Frontend->>API: GET /api/v1/processes/active
    API->>Orch: get_active_processes()
    Orch-->>API: Current active processes
    API-->>Frontend: Active processes list
    
    Note over User, Script: Process Cancellation
    
    User->>Frontend: Cancel Process
    Frontend->>API: POST /api/v1/processes/{task_id}/cancel
    API->>Orch: cancel_process(task_id)
    Orch->>Script: Terminate subprocess
    Orch->>DB: Update status to 'cancelled'
    Orch->>WS: broadcast_process_update(cancelled)
    WS-->>Frontend: Cancellation notification
    API-->>Frontend: Cancellation confirmation
    
    Note over User, Script: System Health Monitoring
    
    loop Periodic Health Checks
        Frontend->>API: GET /api/v1/monitoring/status
        API->>DB: Query system health metrics
        DB-->>API: Health data
        API-->>Frontend: System health status
        
        API->>API: send_periodic_updates()
        API->>WS: Broadcast system health
        WS-->>Frontend: Health updates via WebSocket
    end
    
    Note over User, Script: Error Handling & Alerts
    
    alt Process Error
        Script->>FS: Write error to stderr
        Orch->>Orch: Detect error patterns
        Orch->>DB: Create SystemAlert record
        Orch->>WS: broadcast_process_update(error_status)
        WS-->>Frontend: Error notification
        Frontend->>Frontend: Display error alert
    end
    
    Note over User, Script: Database Operations
    
    Frontend->>API: GET /api/v1/monitoring/tables/metrics
    API->>DB: Query TableMetrics
    DB-->>API: Table statistics
    API-->>Frontend: Database metrics
    
    Note over User, Script: WebSocket Lifecycle
    
    alt WebSocket Disconnect
        Frontend->>WS: Connection lost
        WS->>WS: Remove client from manager
        Frontend->>Frontend: Show disconnected status
        Frontend->>WS: Attempt reconnection
    end
    
    alt Ping/Pong Heartbeat
        Frontend->>WS: Ping message
        WS-->>Frontend: Pong response
    end
