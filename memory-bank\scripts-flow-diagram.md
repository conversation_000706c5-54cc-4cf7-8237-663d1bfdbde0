# HKEX Option Processing Scripts - Flow Diagram Documentation

## Overview
This document provides comprehensive flow diagrams for the Python scripts in the `scripts/` folder that process Hong Kong Exchange (HKEX) option market data.

## System Architecture

### High-Level System Flow

```mermaid
graph TD
    A[HKEX Website<br/>Daily Option Reports] --> B[UpdateIndexOptionPostgres.py<br/>Index Options Processing]
    A --> C[UpdateStockOptionReportPostgres.py<br/>Stock Options Processing]
    
    D[Storacle.py<br/>Mathematical Library] --> B
    D --> C
    
    B --> E[PostgreSQL Database<br/>Local]
    C --> E
    
    E --> F[copyViewMultiDB.py<br/>Data Synchronization]
    F --> G[Remote Databases<br/>Heroku, Aiven, Neon]
    
    H[Yahoo Finance API<br/>Stock Prices] --> B
    H --> C
    
    I[HKEX Holidays XML<br/>Trading Calendar] --> D
```

### Script Dependencies

```mermaid
graph LR
    subgraph "Core Library"
        A[Storacle.py]
    end
    
    subgraph "Data Processing"
        B[UpdateIndexOptionPostgres.py]
        C[UpdateStockOptionReportPostgres.py]
    end
    
    subgraph "Data Synchronization"
        D[copyViewMultiDB.py]
    end
    
    A --> B
    A --> C
    B --> D
    C --> D
```

## Detailed Data Flow

### Index Options Processing Flow (UpdateIndexOptionPostgres.py)

```mermaid
flowchart TD
    Start([Start Index Processing]) --> A[main function]
    A --> B[Process Monthly Options<br/>HSI, HHI, HTI, MHI]
    A --> C[Process Weekly Options<br/>HSI, HHI]
    
    B --> D[getDailyReport]
    C --> E[getDailyWOReport]
    
    D --> F[fetchHKEXReport<br/>Download HTML]
    E --> G[fetchHKEXWeeklyReport<br/>Download HTML]
    
    F --> H[parseHKEXReport<br/>Extract Option Data]
    G --> I[parseHKEXWeeklyReport<br/>Extract Option Data]
    
    H --> J[Calculate Greeks<br/>Using Storacle functions]
    I --> K[Calculate Greeks<br/>Using Storacle functions]
    
    J --> L[insert_report<br/>→ option_daily_report]
    K --> M[insert_weekly_report<br/>→ weekly_option_daily_report]
    
    L --> N[updateDeltaAllStrikes<br/>→ t_index_delta_all_strikes]
    M --> N
    
    N --> O[refreshMaterializedViews]
    O --> End([End])
```

### Stock Options Processing Flow (UpdateStockOptionReportPostgres.py)

```mermaid
flowchart TD
    Start([Start Stock Processing]) --> A[main function]
    A --> B[Get Symbol List<br/>listMonths]
    B --> C[For each Symbol.Month]
    
    C --> D[getDailyStockOptionReport]
    D --> E[fetchHKEXStockReport<br/>Download HTML]
    E --> F[parseHKEXStockReport<br/>Extract Option Data]
    
    F --> G[Calculate Greeks<br/>Using Storacle functions]
    G --> H[insert_stock_option_report<br/>→ stock_option_report]
    
    H --> I[updateStockDeltaAllStrikes<br/>→ stock_option_strikedg]
    I --> J[More Symbols?]
    
    J -->|Yes| C
    J -->|No| K[refreshMaterializedViews]
    K --> End([End])
```

## Function Dependencies

### Storacle.py Core Functions

```mermaid
graph TD
    subgraph "Black-Scholes Calculations"
        A[d1] --> B[d2]
        A --> C[call_price]
        A --> D[put_price]
        B --> C
        B --> D
        A --> E[gamma]
        A --> F[charm]
    end
    
    subgraph "Time Calculations"
        G[getDay2Expiry<br/>Monthly Options]
        H[getWODay2Expiry<br/>Weekly Options]
        I[get_exchange_holidays]
        I --> G
        I --> H
    end
    
    subgraph "Implied Volatility"
        J[CalcIV]
        C --> J
        D --> J
    end
    
    subgraph "Database Utilities"
        K[create_connection]
        L[create_cnx]
    end
```

### Database Schema Flow

```mermaid
erDiagram
    HKEX_REPORTS ||--o{ option_daily_report : "processes"
    HKEX_REPORTS ||--o{ weekly_option_daily_report : "processes"
    HKEX_REPORTS ||--o{ stock_option_report : "processes"
    
    option_daily_report ||--o{ option_daily_strikedg : "calculates"
    weekly_option_daily_report ||--o{ option_daily_strikedg : "calculates"
    stock_option_report ||--o{ stock_option_strikedg : "calculates"
    
    option_daily_report ||--o{ t_index_delta_all_strikes : "aggregates"
    option_daily_strikedg ||--o{ t_index_delta_all_strikes : "aggregates"
    
    option_daily_report ||--o{ v_index_option_value : "materializes"
    weekly_option_daily_report ||--o{ v_weekly_option_value : "materializes"
    stock_option_report ||--o{ v_stock_option_value : "materializes"
    
    v_index_option_value ||--o{ REMOTE_DBS : "syncs"
    v_weekly_option_value ||--o{ REMOTE_DBS : "syncs"
    v_stock_option_value ||--o{ REMOTE_DBS : "syncs"
```

## Execution Sequence

### Daily Processing Workflow

```mermaid
sequenceDiagram
    participant Scheduler
    participant IndexScript as UpdateIndexOptionPostgres.py
    participant StockScript as UpdateStockOptionReportPostgres.py
    participant SyncScript as copyViewMultiDB.py
    participant HKEX
    participant LocalDB as PostgreSQL Local
    participant RemoteDB as Remote Databases
    
    Scheduler->>IndexScript: Execute daily index processing
    IndexScript->>HKEX: Download index option reports
    HKEX-->>IndexScript: HTML reports
    IndexScript->>LocalDB: Insert processed data
    
    Scheduler->>StockScript: Execute daily stock processing
    StockScript->>HKEX: Download stock option reports
    HKEX-->>StockScript: HTML reports
    StockScript->>LocalDB: Insert processed data
    
    Scheduler->>SyncScript: Execute data synchronization
    SyncScript->>LocalDB: Read materialized views
    LocalDB-->>SyncScript: Processed data
    SyncScript->>RemoteDB: Sync data to remote databases
```

## Key Integration Points

### External Dependencies
1. **HKEX Website**: Source of daily option reports (HTML format)
2. **Yahoo Finance API**: Stock price data for Greeks calculations
3. **PostgreSQL Database**: Primary data storage
4. **Remote Databases**: Heroku, Aiven, Neon for data distribution

### Internal Dependencies
1. **Storacle.py**: Mathematical foundation for all calculations
2. **Environment Variables**: Database connections and API keys
3. **Trading Calendar**: HKEX holidays for time-to-expiry calculations

### Error Handling
- Network timeouts for HKEX downloads
- Database connection failures
- Invalid option data parsing
- Yahoo Finance API rate limiting

## Performance Considerations

### Optimization Strategies
1. **Batch Processing**: Process multiple options in single database transactions
2. **API Rate Limiting**: Minimize Yahoo Finance API calls through batching
3. **Database Indexing**: Optimize queries on txn_date and inst_name
4. **Materialized Views**: Pre-calculate complex aggregations

### Monitoring Points
1. **Processing Time**: Track execution duration for each script
2. **Record Counts**: Monitor data volume processed daily
3. **Error Rates**: Track failed downloads and parsing errors
4. **Database Performance**: Monitor query execution times
