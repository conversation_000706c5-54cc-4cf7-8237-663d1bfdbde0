dashboard/
├── backend/
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py
│   │   ├── core/
│   │   │   ├── __init__.py
│   │   │   ├── config.py
│   │   │   ├── database.py
│   │   │   └── security.py
│   │   ├── api/
│   │   │   ├── __init__.py
│   │   │   ├── deps.py
│   │   │   └── routes/
│   │   │       ├── __init__.py
│   │   │       ├── monitoring.py
│   │   │       ├── processes.py
│   │   │       └── data_quality.py
│   │   ├── services/
│   │   │   ├── __init__.py
│   │   │   ├── monitoring.py
│   │   │   ├── process_orchestrator.py
│   │   │   └── data_quality.py
│   │   ├── models/
│   │   │   ├── __init__.py
│   │   │   └── schemas.py
│   │   ├── tasks/
│   │   │   ├── __init__.py
│   │   │   └── celery_app.py
│   │   └── websocket/
│   │       ├── __init__.py
│   │       └── manager.py
│   ├── requirements.txt
│   ├── Dockerfile
│   └── alembic.ini
├── frontend/
│   ├── public/
│   ├── src/
│   │   ├── components/
│   │   ├── services/
│   │   ├── hooks/
│   │   ├── types/
│   │   └── App.tsx
│   ├── package.json
│   └── Dockerfile
├── nginx/
│   └── nginx.conf
├── docker-compose.yml
├── docker-compose.prod.yml
└── README.md
