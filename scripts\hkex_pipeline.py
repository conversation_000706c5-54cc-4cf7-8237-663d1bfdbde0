"""
HKEX Data Processing Pipeline

This module orchestrates the complete HKEX data processing pipeline:
1. Fetch: Download reports from HKEX website
2. Parse: Extract raw data from HTML reports
3. Process: Add Black-Scholes calculations
4. Store: Save to database
5. Analyze: Create summaries and materialized views

This is the main entry point for HKEX data processing workflows.

Debug Mode:
Set HKEX_DEBUG=true in .env file to enable connection testing and debugging.
"""

from datetime import datetime, date
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import pipeline components
from hkex_fetcher import (
    fetch_daily_report, fetch_weekly_report, fetch_hti_report,
    fetch_stock_option_report, fetch_historical_reports,
    test_hkex_connection, check_environment
)
from hkex_parser import (
    parse_daily_report_file, parse_weekly_report_file, parse_hti_report_file,
    parse_stock_option_report_file
)
from hkex_processor import (
    process_daily_option_data, process_weekly_option_data, process_hti_option_data,
    process_stock_option_data
)


class HKEXPipeline:
    """Main pipeline orchestrator for HKEX data processing"""

    def __init__(self, pathname, save_data_func, debug_mode=None):
        """
        Initialize pipeline with required dependencies.

        Args:
            pathname: Base path for file operations
            save_data_func: Function to save processed data to database
            debug_mode: Override debug mode (True/False), if None uses env variable
        """
        self.pathname = pathname
        self.save_data_func = save_data_func

        # Determine debug mode
        if debug_mode is not None:
            self.debug_mode = debug_mode
        else:
            # Check environment variable (case insensitive)
            debug_env = os.getenv('HKEX_DEBUG', 'false').lower()
            self.debug_mode = debug_env in ('true', '1', 'yes', 'on')

        if self.debug_mode:
            print("🐛 HKEX Pipeline running in DEBUG mode - connection tests enabled")
        else:
            print("🚀 HKEX Pipeline running in PRODUCTION mode - connection tests disabled")
    
    def process_daily_report(self, symb, trade_date):
        """
        Complete pipeline for daily option reports.
        
        Args:
            symb: Symbol (HSI, HHI, MHI, HTI)
            trade_date: Trading date
            
        Returns:
            dict: Processing results and statistics
        """
        print(f"\n=== Processing Daily Report: {symb} {trade_date} ===")
        
        results = {
            'symb': symb,
            'trade_date': trade_date,
            'fetch_success': False,
            'parse_success': False,
            'process_success': False,
            'save_success': False,
            'records_processed': 0,
            'records_saved': 0,
            'summary_counts': []
        }
        
        try:
            # Step 1: Fetch report
            print("Step 1: Fetching report...")
            file_path, fetch_success = fetch_daily_report(symb, trade_date, self.pathname)
            results['fetch_success'] = fetch_success
            results['file_path'] = file_path
            
            if not fetch_success:
                print("❌ Fetch failed")
                return results
            
            # Step 2: Parse report
            print("Step 2: Parsing report...")
            parsed_data, summary_counts = parse_daily_report_file(file_path, symb, trade_date)
            results['parse_success'] = len(parsed_data) > 0
            results['records_processed'] = len(parsed_data)
            results['summary_counts'] = summary_counts
            
            if not parsed_data:
                print("❌ Parse failed - no data extracted")
                return results
            
            # Step 3: Process with calculations
            print("Step 3: Processing calculations...")
            processed_data = process_daily_option_data(parsed_data, symb, trade_date)
            results['process_success'] = len(processed_data) > 0
            
            if not processed_data:
                print("❌ Process failed - no calculations completed")
                return results
            
            # Step 4: Save to database
            print("Step 4: Saving to database...")
            records_saved = self.save_data_func(processed_data, 'daily')
            results['save_success'] = records_saved > 0
            results['records_saved'] = records_saved
            
            if records_saved > 0:
                print(f"✅ Pipeline completed successfully: {records_saved} records saved")
            else:
                print("❌ Save failed - no records saved")
            
        except Exception as e:
            print(f"❌ Pipeline error: {e}")
            results['error'] = str(e)
        
        return results
    
    def process_weekly_report(self, symb, trade_date):
        """
        Complete pipeline for weekly option reports.
        
        Args:
            symb: Symbol (HSI, HHI, HTI)
            trade_date: Trading date
            
        Returns:
            dict: Processing results and statistics
        """
        print(f"\n=== Processing Weekly Report: {symb} {trade_date} ===")
        
        results = {
            'symb': symb,
            'trade_date': trade_date,
            'fetch_success': False,
            'parse_success': False,
            'process_success': False,
            'save_success': False,
            'records_processed': 0,
            'records_saved': 0,
            'summary_counts': []
        }
        
        try:
            # Step 1: Fetch report
            print("Step 1: Fetching weekly report...")
            file_path, fetch_success = fetch_weekly_report(symb, trade_date, self.pathname)
            results['fetch_success'] = fetch_success
            results['file_path'] = file_path
            
            if not fetch_success:
                print("❌ Fetch failed")
                return results
            
            # Step 2: Parse report
            print("Step 2: Parsing weekly report...")
            parsed_data, summary_counts = parse_weekly_report_file(file_path, symb, trade_date)
            results['parse_success'] = len(parsed_data) > 0
            results['records_processed'] = len(parsed_data)
            results['summary_counts'] = summary_counts
            
            if not parsed_data:
                print("❌ Parse failed - no data extracted")
                return results
            
            # Step 3: Process with calculations
            print("Step 3: Processing weekly calculations...")
            processed_data = process_weekly_option_data(parsed_data, symb, trade_date)
            results['process_success'] = len(processed_data) > 0
            
            if not processed_data:
                print("❌ Process failed - no calculations completed")
                return results
            
            # Step 4: Save to database
            print("Step 4: Saving weekly data to database...")
            records_saved = self.save_data_func(processed_data, 'weekly')
            results['save_success'] = records_saved > 0
            results['records_saved'] = records_saved
            
            if records_saved > 0:
                print(f"✅ Weekly pipeline completed successfully: {records_saved} records saved")
            else:
                print("❌ Save failed - no records saved")
            
        except Exception as e:
            print(f"❌ Weekly pipeline error: {e}")
            results['error'] = str(e)
        
        return results
    
    def process_hti_report(self, symb, trade_date):
        """
        Complete pipeline for HTI option reports.
        
        Args:
            symb: Symbol (HTI)
            trade_date: Trading date
            
        Returns:
            dict: Processing results and statistics
        """
        print(f"\n=== Processing HTI Report: {symb} {trade_date} ===")
        
        results = {
            'symb': symb,
            'trade_date': trade_date,
            'fetch_success': False,
            'parse_success': False,
            'process_success': False,
            'save_success': False,
            'records_processed': 0,
            'records_saved': 0,
            'summary_counts': []
        }
        
        try:
            # Step 1: Fetch report
            print("Step 1: Fetching HTI report...")
            file_path, fetch_success = fetch_hti_report(symb, trade_date, self.pathname)
            results['fetch_success'] = fetch_success
            results['file_path'] = file_path
            
            if not fetch_success:
                print("❌ Fetch failed")
                return results
            
            # Step 2: Parse report
            print("Step 2: Parsing HTI report...")
            parsed_data, summary_counts = parse_hti_report_file(file_path, symb, trade_date)
            results['parse_success'] = len(parsed_data) > 0
            results['records_processed'] = len(parsed_data)
            results['summary_counts'] = summary_counts
            
            if not parsed_data:
                print("❌ Parse failed - no data extracted")
                return results
            
            # Step 3: Process with calculations
            print("Step 3: Processing HTI calculations...")
            processed_data = process_hti_option_data(parsed_data, symb, trade_date)
            results['process_success'] = len(processed_data) > 0
            
            if not processed_data:
                print("❌ Process failed - no calculations completed")
                return results
            
            # Step 4: Save to database
            print("Step 4: Saving HTI data to database...")
            records_saved = self.save_data_func(processed_data, 'hti')
            results['save_success'] = records_saved > 0
            results['records_saved'] = records_saved
            
            if records_saved > 0:
                print(f"✅ HTI pipeline completed successfully: {records_saved} records saved")
            else:
                print("❌ Save failed - no records saved")
            
        except Exception as e:
            print(f"❌ HTI pipeline error: {e}")
            results['error'] = str(e)
        
        return results

    def process_stock_option_report(self, symb_list, trade_date):
        """
        Complete pipeline for stock option reports.

        Args:
            symb_list: List of symbols to process (or None for all symbols)
            trade_date: Trading date

        Returns:
            dict: Processing results and statistics
        """
        print(f"\n=== Processing Stock Option Report: {trade_date} ===")

        results = {
            'symb_list': symb_list,
            'trade_date': trade_date,
            'fetch_success': False,
            'parse_success': False,
            'process_success': False,
            'save_success': False,
            'records_processed': 0,
            'records_saved': 0,
            'summary_counts': []
        }

        try:
            # Step 1: Fetch report
            print("Step 1: Fetching stock option report...")
            file_path, fetch_success = fetch_stock_option_report(symb_list, trade_date, self.pathname)
            results['fetch_success'] = fetch_success
            results['file_path'] = file_path

            if not fetch_success:
                print("❌ Fetch failed")
                return results

            # Step 2: Parse report
            print("Step 2: Parsing stock option report...")
            parsed_data, summary_counts = parse_stock_option_report_file(file_path, symb_list, trade_date)
            results['parse_success'] = len(parsed_data) > 0
            results['records_processed'] = len(parsed_data)
            results['summary_counts'] = summary_counts

            if not parsed_data:
                print("❌ Parse failed - no data extracted")
                return results

            # Step 3: Process with calculations
            print("Step 3: Processing stock option calculations...")
            processed_data = process_stock_option_data(parsed_data, trade_date)
            results['process_success'] = len(processed_data) > 0

            if not processed_data:
                print("❌ Process failed - no calculations completed")
                return results

            # Step 4: Save to database
            print("Step 4: Saving stock option data to database...")
            records_saved = self.save_data_func(processed_data, 'stock_option')
            results['save_success'] = records_saved > 0
            results['records_saved'] = records_saved

            if records_saved > 0:
                print(f"✅ Stock option pipeline completed successfully: {records_saved} records saved")
            else:
                print("❌ Save failed - no records saved")

        except Exception as e:
            print(f"❌ Stock option pipeline error: {e}")
            results['error'] = str(e)

        return results

    def process_historical_reports(self, symb, start_date, end_date, report_type='daily'):
        """
        Process historical reports in bulk.
        
        Args:
            symb: Symbol or None for all symbols
            start_date: Start date
            end_date: End date
            report_type: 'daily', 'weekly', or 'hti'
            
        Returns:
            dict: Bulk processing results
        """
        print(f"\n=== Processing Historical Reports: {symb} {start_date} to {end_date} ===")
        
        # First, fetch all historical reports
        fetch_summary = fetch_historical_reports(symb, start_date, end_date, self.pathname)
        
        # Then process each fetched report
        # This would be implemented based on specific requirements
        # For now, return the fetch summary
        
        return {
            'fetch_summary': fetch_summary,
            'processing_summary': 'Historical processing not yet implemented'
        }
    
    def run_health_check(self):
        """
        Run pipeline health checks.

        Returns:
            dict: Health check results
        """
        print("\n=== HKEX Pipeline Health Check ===")

        health_results = {
            'environment': check_environment(),
            'file_system': os.path.exists(self.pathname),
            'dependencies': True,  # Could check if all modules import correctly
            'debug_mode': self.debug_mode
        }

        # Only test HKEX connection if not in debug mode
        if not self.debug_mode:
            print("Testing HKEX connection...")
            health_results['hkex_connection'] = test_hkex_connection()
        else:
            print("⏭️  Skipping HKEX connection test (debug mode)")
            health_results['hkex_connection'] = 'SKIPPED'

        # Calculate overall health (excluding skipped tests)
        testable_results = {k: v for k, v in health_results.items()
                           if v != 'SKIPPED' and k != 'debug_mode'}
        all_healthy = all(testable_results.values())

        print(f"\nHealth Check Results:")
        for check, result in health_results.items():
            if result == 'SKIPPED':
                status = "⏭️  SKIPPED"
            elif result is True:
                status = "✅ PASS"
            elif result is False:
                status = "❌ FAIL"
            else:
                status = f"ℹ️  {result}"
            print(f"  {check}: {status}")

        print(f"\nOverall Status: {'✅ HEALTHY' if all_healthy else '❌ UNHEALTHY'}")

        return health_results


# Convenience functions for backward compatibility
def getDailyMarketReport(symb, trade_date, pathname, save_data_func):
    """Backward compatible function for daily market reports"""
    pipeline = HKEXPipeline(pathname, save_data_func)
    results = pipeline.process_daily_report(symb, trade_date)
    return results.get('summary_counts', [])


def getDailyWOReport(symb, trade_date, pathname, save_data_func):
    """Backward compatible function for weekly option reports"""
    pipeline = HKEXPipeline(pathname, save_data_func)
    results = pipeline.process_weekly_report(symb, trade_date)
    return results.get('summary_counts', [])


def getDailyHTIReport(symb, trade_date, pathname, save_data_func):
    """Backward compatible function for HTI reports"""
    pipeline = HKEXPipeline(pathname, save_data_func)
    results = pipeline.process_hti_report(symb, trade_date)
    return results.get('summary_counts', [])


def getStockOptionReport(symb_list, trade_date, pathname, save_data_func):
    """Backward compatible function for stock option reports"""
    pipeline = HKEXPipeline(pathname, save_data_func)
    results = pipeline.process_stock_option_report(symb_list, trade_date)
    return results.get('summary_counts', [])
