#!/usr/bin/env python3
"""
Test script demonstrating the fix for pandas to_sql numeric column preservation.

This script shows how to use explicit SQLAlchemy data type mapping to prevent
pandas from converting numeric columns to text when using df.to_sql().
"""

import pandas as pd
import numpy as np
from sqlalchemy import create_engine, text
from sqlalchemy.types import String, Integer, Float, Date, DECIMAL

def get_sqlalchemy_dtype_mapping(df, table_name):
    """
    Create SQLAlchemy data type mapping based on DataFrame dtypes and table structure.
    This prevents pandas from incorrectly inferring column types during to_sql().
    """
    dtype_mapping = {}
    
    for column in df.columns:
        # Get pandas dtype
        pd_dtype = str(df[column].dtype)
        
        # Map pandas dtypes to SQLAlchemy types
        if pd_dtype.startswith('int'):
            dtype_mapping[column] = Integer()
        elif pd_dtype.startswith('float'):
            # Use DECIMAL for financial data to preserve precision
            if any(keyword in column.lower() for keyword in ['price', 'value', 'delta', 'gamma', 'theta', 'vega', 'rho', 'strike', 'spot', 'iv']):
                dtype_mapping[column] = DECIMAL(precision=15, scale=6)
            else:
                dtype_mapping[column] = Float()
        elif pd_dtype == 'object':
            # Check if it's actually numeric data stored as object
            try:
                # Try to convert to numeric to see if it's really numeric data
                pd.to_numeric(df[column], errors='raise')
                dtype_mapping[column] = DECIMAL(precision=15, scale=6)
            except (ValueError, TypeError):
                # It's really text data
                max_length = df[column].astype(str).str.len().max() if len(df) > 0 else 50
                dtype_mapping[column] = String(max(max_length, 50))
        elif 'datetime' in pd_dtype or 'date' in pd_dtype:
            dtype_mapping[column] = Date()
        else:
            # Default to String for unknown types
            dtype_mapping[column] = String(255)
    
    return dtype_mapping

def ensure_numeric_types(df):
    """
    Ensure numeric columns are properly typed before saving to database.
    """
    df_copy = df.copy()
    
    # Common numeric columns in option data
    numeric_columns = [
        'price', 'value', 'delta', 'gamma', 'theta', 'vega', 'rho',
        'strike', 'spot', 'iv', 'volume', 'oi', 'open', 'high', 'low', 'close'
    ]
    
    for col in df_copy.columns:
        # Check if column name suggests it should be numeric
        if any(num_col in col.lower() for num_col in numeric_columns):
            try:
                # Convert to numeric, handling errors gracefully
                df_copy[col] = pd.to_numeric(df_copy[col], errors='coerce')
            except Exception as e:
                print(f"Warning: Could not convert {col} to numeric: {e}")
    
    return df_copy

def test_dtype_mapping():
    """Test the data type mapping with sample option data."""
    
    # Create sample option data similar to what you might have
    sample_data = {
        'txn_date': pd.to_datetime(['2024-01-01', '2024-01-02', '2024-01-03']),
        'symbol': ['SPY', 'QQQ', 'IWM'],
        'strike_price': [450.0, 380.0, 220.0],
        'option_price': [15.25, 8.75, 5.50],
        'delta_value': [0.65, -0.45, 0.35],
        'gamma_value': [0.02, 0.03, 0.01],
        'theta_value': [-0.15, -0.12, -0.08],
        'vega_value': [0.25, 0.18, 0.12],
        'volume': [1000, 1500, 800],
        'open_interest': [5000, 3200, 1800],
        'iv_value': [0.25, 0.30, 0.28]
    }
    
    df = pd.DataFrame(sample_data)
    
    # Introduce some object-type numeric columns (common issue)
    df['strike_price'] = df['strike_price'].astype(str)  # Simulate string numbers
    df['volume'] = df['volume'].astype('object')  # Simulate object-type numbers
    
    print("Original DataFrame dtypes:")
    print(df.dtypes)
    print("\nSample data:")
    print(df.head())
    
    # Apply our fixes
    print("\n" + "="*60)
    print("APPLYING FIXES")
    print("="*60)
    
    # Step 1: Ensure numeric types
    df_fixed = ensure_numeric_types(df)
    print("\nDataFrame dtypes after ensure_numeric_types:")
    print(df_fixed.dtypes)
    
    # Step 2: Get SQLAlchemy mapping
    dtype_mapping = get_sqlalchemy_dtype_mapping(df_fixed, 'test_option_table')
    print("\nSQLAlchemy dtype mapping:")
    for col, dtype in dtype_mapping.items():
        print(f"  {col}: {dtype}")
    
    print("\n" + "="*60)
    print("USAGE IN COPYVIEWMULTIDB.PY")
    print("="*60)
    print("""
# After reading data from local database:
df = pd.read_sql(query, local_db)

# Apply the fixes:
df = ensure_numeric_types(df)
dtype_mapping = get_sqlalchemy_dtype_mapping(df, table_name)

# Use in to_sql with explicit dtype mapping:
df.to_sql(
    name=table_name, 
    con=remote_db, 
    if_exists='append', 
    index=False, 
    dtype=dtype_mapping  # <-- This is the key addition
)
    """)
    
    return df_fixed, dtype_mapping

if __name__ == "__main__":
    test_dtype_mapping()
