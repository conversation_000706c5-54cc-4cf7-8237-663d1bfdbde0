import requests
import json

def test_log_tail_endpoint():
    """Test the log-tail endpoint that was failing before."""
    try:
        # Test with a mock task ID
        task_id = "update_index_options_20250527_172032_9971"
        url = f"http://localhost:8000/api/v1/processes/{task_id}/log-tail?lines=100"
        
        print(f"Testing endpoint: {url}")
        response = requests.get(url, timeout=5)
        
        print(f"Status code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Log-tail endpoint is working!")
            print(f"Response: {json.dumps(data, indent=2)}")
        else:
            print(f"❌ Endpoint returned error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to backend server. Is it running on port 8000?")
    except Exception as e:
        print(f"❌ Error testing endpoint: {e}")

if __name__ == "__main__":
    test_log_tail_endpoint()
