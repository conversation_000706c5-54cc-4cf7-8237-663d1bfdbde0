ProcessSOStrikeDG= MySQL.Database("localhost:3306", "storacle", [ReturnSingleDatabase=true, Query="
SELECT  substr(a.inst_name,1,3) as symb,  
substr(a.inst_name,5,6) as cont_month,  
substr(a.inst_name,12,5) as strike,  
a.txn_date,
substr(a.inst_name,18,1) as CP,
a.oi ,
'P',
b.oi
FROM storacle.option_daily_oi a, storacle.option_daily_oi b
where substr(a.inst_name,18,1) ='C'
and b.inst_name= replace( a.inst_name, '.C', '.P')
and a. txn_date= b.txn_date"])

SELECT  substr(a.inst_name,1,3) as symb,  
substr(a.inst_name,5,6) as cont_month,  
substr(a.inst_name,12,5) as strike,  
a.txn_date,
substr(a.inst_name,18,1) as CP,
a.oi ,
'P',
b.oi
FROM storacle.option_daily_oi a, storacle.option_daily_oi b
where substr(a.inst_name,18,1) ='C'
and b.inst_name= replace( a.inst_name, '.C', '.P')
and a. txn_date= b.txn_date

SELECT  substr(a.inst_name,1,3) as symb,  
substr(a.inst_name,5,6) as cont_month,  
substr(a.inst_name,12,5) as strike,  
a.txn_date,
substr(a.inst_name,18,1) as CP,
a.iv ,
'P',
b.iv
FROM storacle.option_daily_iv a, storacle.option_daily_iv b
where substr(a.inst_name,18,1) ='C'
and b.inst_name= replace( a.inst_name, '.C', '.P')
and a. txn_date= b.txn_date

SELECT  substr(a.inst_name,1,3) as symb,  
substr(a.inst_name,5,6) as cont_month,  
substr(a.inst_name,12,5) as strike,  
a.txn_date,
substr(a.inst_name,18,1) as CP,
a.iv ,
'P',
b.iv
FROM storacle.option_daily_iv a, storacle.option_daily_iv b
where substr(a.inst_name,18,1) ='C'
and a.inst_name like 'HSI.DEC-20.%'
and b.inst_name= replace( a.inst_name, '.C', '.P')
and a. txn_date= b.txn_date


CREATE TABLE storacle.instruments (
  inst_id INT NOT NULL AUTO_INCREMENT,
  inst_name VARCHAR(45) NULL,
  inst_class VARCHAR(45) NULL,
  PRIMARY KEY (inst_id),
  UNIQUE INDEX inst_name_UNIQUE (inst_name ASC) VISIBLE)
COMMENT = 'Contain all target instrument such as Stocks, Options, futures...etc';

CREATE TABLE option_daily_report (
  txn_id int NOT NULL AUTO_INCREMENT,
  inst_name varchar(45) NOT NULL,
  xtxn_date date NOT NULL,
  xopen float DEFAULT NULL,
  xhigh float DEFAULT NULL,
  xlow float DEFAULT NULL,
  xclose float DEFAULT NULL,
  xvolume float DEFAULT NULL,
  txn_date date NOT NULL,
  open float DEFAULT NULL,
  high float DEFAULT NULL,
  low float DEFAULT NULL,
  close float DEFAULT NULL,
  volume float DEFAULT NULL,
  iv int DEFAULT NULL,
  oi int DEFAULT NULL,
  oi_change int DEFAULT NULL,
  stock_price float DEFAULT NULL,
  delta float DEFAULT NULL,
  gamma float DEFAULT NULL,
  PRIMARY KEY (txn_id),
  UNIQUE KEY idx_txn_date_instm (inst_name,txn_date)
)


# Calaculat Max Pain or 0 Deleta or 0 Gamma
SELECT  a.txn_date, substr(a.inst_name,1,3) as symb,  
substr(a.inst_name,5,6) as cont_month,  
substr(a.inst_name,12,5) as strike,  
substr(a.inst_name,1,16) as inst,  
a.close as cclose, a.iv as civ, a.oi as coi,
b.close as pclose, b.iv as piv, b.oi as poi
FROM storacle.option_daily_report a, storacle.option_daily_report b
where substr(a.inst_name,18,1) ='C'
and b.inst_name= replace( a.inst_name, '.C', '.P')
and a. txn_date= b.txn_date;


SELECT  a.txn_date, substr(a.inst_name,1,3) as symb,  
substr(a.inst_name,5,6) as cont_month,  
substr(a.inst_name,12,5) as strike,  
substr(a.inst_name,1,16) as inst,  
a.close, a.iv, a.oi,
b.close as stock_price
FROM storacle.option_daily_report a, storacle.daily_stock_price b
where substr(a.inst_name,1,3) =b.hkats_code
and a. txn_date= b.txn_date;


CREATE TABLE storacle.daily_stock_price (
  price_id INT NOT NULL AUTO_INCREMENT,
  ticker varchar(45) NOT NULL,
  txn_date DATE NOT NULL,
  open FLOAT NULL,
  high FLOAT NULL,
  low FLOAT NULL,
  close FLOAT NULL,
  adj_close float null,
  volume FLOAT NULL,
  hkats_code varchar(12) NULL,
  PRIMARY KEY (price_id));

SELECT  a.txn_date, substr(a.inst_name,1,3) as symb,  
substr(a.inst_name,5,6) as cont_month,  
substr(a.inst_name,12,5) as strike,  
substr(a.inst_name,1,16) as inst,  
a.stock_price,
a.close as cclose, a.iv as civ, a.oi as coi, a.delta as cdelta, a.gamma as cgamma, 
a.gamma*a.oi as cnetgamma, a.delta*a.oi as cnetdelta,
b.close as pclose, b.iv as piv, b.oi as poi, b.delta as pdelta, b.gamma as pgamma, 
b.gamma*b.oi as pnetgamma, b.delta*b.oi as pnetdelta,
(a.gamma*a.oi + b.gamma*b.oi) as net_gamma, 
(a.delta*a.oi - b.delta*b.oi) as net_delta
FROM storacle.option_daily_report a, storacle.option_daily_report b
where substr(a.inst_name,18,1) ='C'
and b.inst_name= replace( a.inst_name, '.C', '.P')
and a. txn_date= b.txn_date
and substr(a.inst_name,5,6) = 'OCT-20'

SELECT  a.txn_date, substr(a.inst_name,1,3) as symb,  
substr(a.inst_name,5,6) as cont_month,  
substr(a.inst_name,12,5) as strike,  
substr(a.inst_name,1,16) as inst,  
a.stock_price,
a.close as cclose, a.iv as civ, a.oi as coi, a.delta as cdelta, a.gamma as cgamma, 
a.gamma*a.oi as cnetgamma, a.delta*a.oi as cnetdelta,
FROM storacle.option_daily_report a
where substr(inst_name,18,1) ='C'
and substr(inst_name,5,6) = 'OCT-20'


select * from storacle.option_daily_report
where substr(a.inst_name,5,6) = 'OCT-20'
and substr(a.inst_name,12,5)='24000'
and txn_date ='16-10-2020'

CREATE TABLE option_daily_strikedg (
  strikeDG_id int NOT NULL AUTO_INCREMENT,
  txn_id int NOT NULL,
  strike float NOT NULL,
  delta float DEFAULT '0',
  gamma float DEFAULT '0',
  PRIMARY KEY (strikeDG_id),
  KEY fk_strikedg_txn_id_idx (txn_id),
  CONSTRAINT fk_strikedg_txn_id FOREIGN KEY (txn_id) REFERENCES option_daily_report (txn_id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8


SELECT substr(a.inst_name,1,10) as cmonth, a.txn_date,
sum(IF(substr(a.inst_name,18,1) ='C', 1, 0)*a.oi*b.delta) as call_delta, 
sum(IF(substr(a.inst_name,18,1) ='P', 1, 0)*a.oi*b.delta) as put_delta, 
sum(a.oi*b.delta) as abs_delta, 
sum(a.oi*b.gamma) as total_gamma
FROM storacle.option_daily_report a, storacle.option_daily_strikedg b
where a.txn_id= b.txn_id
and b.strike ='24200'
and a.txn_date ='2020-08-07'
group by cmonth, a.txn_date;

SELECT substr(a.inst_name,1,10) as cmonth, a.txn_date,b.strike,
sum(IF(substr(a.inst_name,18,1) ='C', 1, 0)*a.oi*b.delta) as call_delta, 
sum(IF(substr(a.inst_name,18,1) ='P', 1, 0)*a.oi*b.delta) as put_delta, 
sum(a.oi*b.delta) as abs_delta, 
sum(a.oi*b.gamma) as total_gamma
FROM storacle.option_daily_report a, storacle.option_daily_strikedg b
where a.txn_id= b.txn_id
and b.strike ='26000'
and a.txn_date ='2020-10-16'
group by cmonth, a.txn_date, b.strike;

SELECT substr(a.inst_name,1,10) as cmonth, a.txn_date, b.strike,
(a.oi*b.delta) as abs_delta
FROM storacle.option_daily_report a, storacle.option_daily_strikedg b
where a.txn_id= b.txn_id
and txn_date='2020-09-23'
group by cmonth, a.txn_date, b.strike;

select bb.* from
(select a.code,a.price,min(b.id) id from
(select code,min(price) as price from product group by code) a
inner join product b using (code,price)
group by a.code,a.price) aa
inner join product bb using (id);

select b.* from
(
SELECT txn_date, min(abs_delta) as abs_delta
FROM storacle.option_delta_at_all_strikes
group by txn_date) a
inner join storacle.option_delta_at_all_strikes b on a.txn_date = b.txn_date and  a.abs_delta = b.abs_delta;

SELECT substr(a.inst_name,1,10) as cmonth, a.txn_date,b.strike,
sum(IF(substr(a.inst_name,18,1) ='C', 1, 0)*a.oi*b.delta) as call_delta, 
sum(IF(substr(a.inst_name,18,1) ='P', 1, 0)*a.oi*b.delta) as put_delta, 
sum(a.oi*b.delta) as abs_delta, 
sum(a.oi*b.gamma) as total_gamma
FROM storacle.option_daily_report a, storacle.option_daily_strikedg b
where a.txn_id= b.txn_id
and substr(a.inst_name,1,10) ='HSI.SEP-20'
group by cmonth, a.txn_date, b.strike;

SELECT substr(a.inst_name,1,10) as cmonth, a.txn_date, a.inst_name, a.oi,
b.strike,
(IF(substr(a.inst_name,18,1) ='C', 1, 0)*a.oi*b.delta) as call_delta, 
(IF(substr(a.inst_name,18,1) ='P', 1, 0)*a.oi*b.delta) as put_delta, 
(a.oi*b.delta) as abs_delta, 
(a.oi*b.gamma) as total_gamma
FROM storacle.option_daily_report a, storacle.option_daily_strikedg b
where a.txn_id= b.txn_id
and a.txn_date='2020-10-16'
and substr(a.inst_name,1,10) ='HSI.OCT-20'

select * from storacle.option_daily_report
where substr(a.inst_name,5,6) = 'SEP-20'
and substr(a.inst_name,12,5)='24000'
and txn_date ='16-09-2020'

delete FROM storacle.instruments
where substr(inst_name,1,3) ='HHI'
and substr(inst_name,12,1)=' ';

select * FROM storacle.instruments
where substr(inst_name,1,3) ='HHI'
and substr(inst_name,12,5)='09900';

SELECT  a.txn_date, 
substr(a.inst_name,1,10) as inst_month,  
substr(a.inst_name,12,5) as strike,  
a.oi as coi, a.delta as cdelta, a.gamma as cgamma, 
a.gamma*a.oi as cnetgamma, a.delta*a.oi as cnetdelta,
-1*b.oi as poi, -1*b.delta as pdelta, b.gamma as pgamma, 
b.gamma*b.oi as pnetgamma, 
-1*b.delta*b.oi as pnetdelta,
(a.gamma*a.oi + b.gamma*b.oi) as net_gamma, 
(a.delta*a.oi - b.delta*b.oi) as net_delta,
a.oi_change as coi_change,
-1*b.oi_change as poi_change
FROM storacle.option_daily_report a, storacle.option_daily_report b
where substr(a.inst_name,18,1) ='C'
and substr(b.inst_name,18,1) ='P'
and substr(a.inst_name,1,16)= substr(b.inst_name,1,16)
and a. txn_date= b.txn_date
and substr(a.inst_name,1,10) = 'HSI.DEC-20'
and substr(a.inst_name,12,5) between '20000' and '30000'

select * FROM storacle.option_daily_report where txn_date='2020-10-20' and inst_name = 'HSI.OCT-20.24000.C' ;

select txn_date, left(inst_name, 10), count(*) from option_daily_report
group by txn_date,left(inst_name, 10)

select txn_date, left(inst_name, 10), count(*) from option_daily_report
where left(inst_name,3)='HHI'
group by txn_date,left(inst_name, 10)

SELECT left(a.inst_name, 10), max(txn_date) FROM option_daily_report a, storacle.option_daily_strikedg b
where a.txn_id = b.txn_id
and left(a.inst_name,3)='HHI'
group by left(a.inst_name, 10)

SELECT max(txn_date) FROM option_daily_report a, storacle.option_daily_strikedg b
where a.txn_id = b.txn_id

select left(inst_name,10), count(*)  from option_daily_report
group by  txn_date;

where left(inst_name,10)='HSI.OCT-20'
and  txn_date='2020-07-21';

delete from option_daily_report
where left(inst_name,3)='HHI'
and  txn_date='2020-07-21';

delete FROM storacle.option_daily_strikedg
where substr(inst_name,1,3) ='HHI'


create view option_daily_min_delta as
select b.* from
(
SELECT cmonth, txn_date, min(abs_delta) as abs_delta
FROM storacle.option_delta_at_all_strikes
group by cmonth, txn_date) a
inner join storacle.option_delta_at_all_strikes b on a.txn_date = b.txn_date and  a.cmonth = b.cmonth and a.abs_delta = b.abs_delta;

# V2,ignore settlement date whre abs-delta=0
drop  view option_daily_min_delta;
create view option_daily_min_delta as
select b.* from
(
SELECT cmonth, txn_date, min(abs_delta) as abs_delta
FROM storacle.option_delta_at_all_strikes
where abs_delta >0
group by cmonth, txn_date) a
inner join storacle.option_delta_at_all_strikes b on a.txn_date = b.txn_date and  a.cmonth = b.cmonth and a.abs_delta = b.abs_delta;

# reduce scope to 1 month
create view option_daily_min_delta_1m as
select b.* from
(
SELECT cmonth, txn_date, min(abs_delta) as abs_delta
FROM storacle.option_delta_at_all_strikes
where abs_delta >0 and txn_date > (curdate() - interval 1 month)
group by cmonth, txn_date) a
inner join storacle.option_delta_at_all_strikes b on 
a.txn_date = b.txn_date and  
a.cmonth = b.cmonth and 
a.abs_delta = b.abs_delta and
b.txn_date > (curdate() - interval 1 month);


create view weekly_option_daily_min_delta as
select b.* from
(
SELECT cmonth, txn_date, min(abs_delta) as abs_delta
FROM storacle.weekly_option_delta_at_all_strikes
group by cmonth, txn_date) a
inner join storacle.weekly_option_delta_at_all_strikes b on a.txn_date = b.txn_date and  a.cmonth = b.cmonth and a.abs_delta = b.abs_delta;

# V2,ignore settlement date whre abs-delta=0
drop  view weekly_option_daily_min_delta;
create view weekly_option_daily_min_delta as
select b.* from
(
SELECT cmonth, txn_date, min(abs_delta) as abs_delta
FROM storacle.weekly_option_delta_at_all_strikes
where abs_delta >0
group by cmonth, txn_date) a
inner join storacle.weekly_option_delta_at_all_strikes b on a.txn_date = b.txn_date and  a.cmonth = b.cmonth and a.abs_delta = b.abs_delta;

# reduce scope to 1 month
create view weekly_option_daily_min_delta_1m as
select b.* from
(
SELECT cmonth, txn_date, min(abs_delta) as abs_delta
FROM storacle.weekly_option_delta_at_all_strikes
where abs_delta >0 and txn_date > (curdate() - interval 1 month)
group by cmonth, txn_date) a
inner join storacle.weekly_option_delta_at_all_strikes b on 
a.txn_date = b.txn_date and  
a.cmonth = b.cmonth and 
a.abs_delta = b.abs_delta and
b.txn_date > (curdate() - interval 1 month);

SELECT substr(a.inst_name,1,10) as cmonth, count(a.txn_date)
FROM storacle.option_daily_report a, storacle.option_daily_strikedg b
where a.txn_id= b.txn_id
group by cmonth, a.txn_date;

select * from option_daily_strikedg where txn_id in (
  select txn_id from option_daily_report
  where txn_date='2020-10-19' and inst_name like 'HSI.OCT-20%'
)

delete from  option_daily_strikedg where txn_id in (
  select txn_id from option_daily_report
  where txn_date='2020-10-23' and inst_name like 'HSI.DEC-20%'
)


SELECT * FROM option_daily_report a, storacle.option_daily_strikedg b
where a.txn_id = b.txn_id
and left(a.inst_name,10)='HSI.DEC-20'
and txn_date='2020-10-23'


SELECT a.txn_date, b.strike, sum(b.delta) FROM option_daily_report a, storacle.option_daily_strikedg b
where a.txn_id = b.txn_id
and left(a.inst_name,10)='HSI.DEC-20'
and txn_date='2020-10-23'
group by a.txn_date, b.strike


CREATE VIEW option_delta_at_all_strikes AS 
select substr(a.inst_name,1,10) AS cmonth,a.txn_date AS txn_date,b.strike AS strike,
sum(((if((substr(a.inst_name,18,1) = 'C'),1,0) * a.oi) * b.delta)) AS call_delta,
sum(((if((substr(a.inst_name,18,1) = 'P'),1,0) * a.oi) * b.delta)) AS put_delta,
sum((a.oi * b.delta)) AS abs_delta,
sum((a.oi * b.gamma)) AS total_gamma 
from (option_daily_report a join option_daily_strikedg b) 
where (a.txn_id = b.txn_id) 
group by cmonth,a.txn_date,b.strike

select substr(a.inst_name,1,10) AS cmonth, a.*, b.*,
(((if((substr(a.inst_name,18,1) = 'C'),1,0) * a.oi) * b.delta)) AS call_delta,
(((if((substr(a.inst_name,18,1) = 'P'),1,0) * a.oi) * b.delta)) AS put_delta,
((a.oi * b.delta)) AS abs_delta,
((a.oi * b.gamma)) AS total_gamma 
from (option_daily_report a join option_daily_strikedg b) 
where (a.txn_id = b.txn_id) 
and left(a.inst_name,10) ='HHI.SEP-20'
and a.txn_date > '2010-09-29'

CREATE VIEW option_delta_at_major_strikes AS 
select substr(a.inst_name,1,10) AS cmonth,a.txn_date AS txn_date,b.strike AS strike,
sum(((if((substr(a.inst_name,18,1) = 'C'),1,0) * a.oi) * b.delta)) AS call_delta,
sum(((if((substr(a.inst_name,18,1) = 'P'),1,0) * a.oi) * b.delta)) AS put_delta,
sum((a.oi * b.delta)) AS abs_delta,
sum((a.oi * b.gamma)) AS total_gamma 
from (option_daily_report a join option_daily_strikedg b) 
where (a.txn_id = b.txn_id) 
and substr(a.inst_name,12,5) between '22000' and '27000'
group by cmonth,a.txn_date,b.strike

create view option_daily_delta_simp as
select b.* from
(
SELECT cmonth, txn_date, min(abs_delta) as abs_delta
FROM storacle.option_delta_at_major_strikes
group by cmonth, txn_date) a
inner join storacle.option_delta_at_major_strikes b on a.txn_date = b.txn_date and  a.cmonth = b.cmonth and a.abs_delta = b.abs_delta;

select b.* from
(
SELECT cmonth, txn_date, min(abs_delta) as abs_delta
FROM storacle.option_delta_at_all_strikes
group by cmonth, txn_date) a
inner join storacle.option_delta_at_all_strikes b on a.txn_date = b.txn_date and  a.cmonth = b.cmonth and a.abs_delta = b.abs_delta;


SELECT cmonth, txn_date, min(abs_delta) as abs_delta
FROM storacle.option_delta_at_all_strikes
group by cmonth, txn_date
having cmonth='HHI.SEP-20'
and txn_date= '2020-09-29'

SELECT a.*, b.close, (a.strike - b.close) as price_diff
FROM storacle.option_daily_min_delta a, daily_stock_price b
where a.txn_date=b.txn_date and left(a.cmonth,3)= b.hkats_code
and a.abs_delta >0;

CREATE TABLE weekly_option_daily_report (
  txn_id int NOT NULL AUTO_INCREMENT,
  inst_name varchar(45) NOT NULL,
  xtxn_date date NOT NULL,
  xopen float DEFAULT NULL,
  xhigh float DEFAULT NULL,
  xlow float DEFAULT NULL,
  xclose float DEFAULT NULL,
  xvolume float DEFAULT NULL,
  txn_date date NOT NULL,
  open float DEFAULT NULL,
  high float DEFAULT NULL,
  low float DEFAULT NULL,
  close float DEFAULT NULL,
  volume float DEFAULT NULL,
  iv int DEFAULT NULL,
  oi int DEFAULT NULL,
  oi_change int DEFAULT NULL,
  stock_price float DEFAULT NULL,
  delta float DEFAULT NULL,
  gamma float DEFAULT NULL,
  PRIMARY KEY (txn_id),
  UNIQUE KEY idx_txn_date_instm (inst_name,txn_date)
) ENGINE=InnoDB AUTO_INCREMENT=631926 DEFAULT CHARSET=utf8

CREATE TABLE weekly_option_daily_strikedg (
  strikeDG_id int NOT NULL AUTO_INCREMENT,
  txn_id int NOT NULL,
  strike float NOT NULL,
  delta float DEFAULT '0',
  gamma float DEFAULT '0',
  PRIMARY KEY (strikeDG_id),
  UNIQUE KEY idx_txn_date_strike (txn_id,strike),
  KEY fk_strikedg_txn_id_idx (txn_id),
  CONSTRAINT fk_wo_strikedg_txn_id FOREIGN KEY (txn_id) REFERENCES weekly_option_daily_report (txn_id) ON DELETE CASCADE ON UPDATE CASCADE
) 

CREATE VIEW weekly_option_delta_at_all_strikes AS 
select substr(a.inst_name,1,13) AS cmonth,a.txn_date AS txn_date,b.strike AS strike,
sum(((if((substr(a.inst_name,21,1) = 'C'),1,0) * a.oi) * b.delta)) AS call_delta,
sum(((if((substr(a.inst_name,21,1) = 'P'),1,0) * a.oi) * b.delta)) AS put_delta,
sum((a.oi * b.delta)) AS abs_delta,
sum((a.oi * b.gamma)) AS total_gamma 
from (weekly_option_daily_report a join weekly_option_daily_strikedg b) 
where (a.txn_id = b.txn_id) 
group by cmonth,a.txn_date,b.strike


SELECT  a.txn_id, a.txn_date, substr(a.inst_name,1,3) as symb,
substr(a.inst_name,5,6) as cont_month,
convert(substr(a.inst_name,12,5), signed) as strike,
substr(a.inst_name,1,16) as inst,      
substr(a.inst_name,18,1) as cp,
a.close, a.iv/100 as iv, a.oi,    
convert(a.stock_price, signed) as stock_price     
FROM storacle.option_daily_report a     
where a.txn_date > "2020-06-29" and left(a.inst_name,10) = "HSI.AUG-20"

SELECT  a.txn_id, a.txn_date, substr(a.inst_name,1,3) as symb,
substr(a.inst_name,5,6) as cont_month,
convert(substr(a.inst_name,12,5), signed) as strike,
substr(a.inst_name,1,16) as inst,      
substr(a.inst_name,18,1) as cp,
a.close, a.iv/100 as iv, a.oi,    
convert(a.stock_price, signed) as stock_price     

update option_daily_report a,  daily_stock_price p
set a.stock_price = p.close
where a.txn_date = "2020-06-30" and left(a.inst_name,10) = "HSI.AUG-20"
and a.txn_date= p.txn_date and p.hkats_code = left(a.inst_name,3)

SELECT * FROM option_daily_report a, storacle.option_daily_strikedg b
where a.txn_id = b.txn_id
and left(a.inst_name,10)='HHI.DEC-20'
and txn_date='2020-11-06'

SELECT * FROM option_daily_report a
where left(a.inst_name,10)='HHI.DEC-20'
and txn_date='2020-11-06'


'AMC.JUL20.032.00.C'
'HSI.AUG-20.16600.C'
# STOCK OPTION TABLES
CREATE TABLE stock_option_report (
  txn_id int NOT NULL AUTO_INCREMENT,
  inst_name varchar(45) NOT NULL,
  txn_date date NOT NULL,
  open float DEFAULT NULL,
  high float DEFAULT NULL,
  low float DEFAULT NULL,
  close float DEFAULT NULL,
  volume float DEFAULT NULL,
  iv int DEFAULT NULL,
  oi int DEFAULT NULL,
  oi_change int DEFAULT NULL,
  stock_price float DEFAULT NULL,
  delta float DEFAULT NULL,
  gamma float DEFAULT NULL,
  PRIMARY KEY (txn_id),
  UNIQUE KEY idx_txn_date_instm (inst_name,txn_date)
) 

CREATE TABLE stock_option_strikedg (
  strikeDG_id int NOT NULL AUTO_INCREMENT,
  txn_id int NOT NULL,
  strike float NOT NULL,
  delta float DEFAULT '0',
  gamma float DEFAULT '0',
  PRIMARY KEY (strikeDG_id),
  UNIQUE KEY idx_txn_date_strike (txn_id,strike),
  KEY fk_strikedg_txn_id_idx (txn_id),
  CONSTRAINT fk_strikedg_txn_id FOREIGN KEY (txn_id) REFERENCES stock_option_report (txn_id) ON DELETE CASCADE ON UPDATE CASCADE
) 

delete 
select *
from option_daily_report
where left(inst_name,3) not in ('HHI', 'HSI', 'MHI')
and  txn_date='2020-07-02';


create table hkats_code(
  id int NOT NULL AUTO_INCREMENT,
  hkats_code varchar(10) NOT NULL,
  name varchar(45),
  price_lower_q varchar(45),
  price_upper_q varchar(45),
  ticker varchar(10),
  PRIMARY KEY (id),
  UNIQUE KEY idx_hkats_code (hkats_code)
)

INSERT INTO storacle.hkats_code (hkats_code, name, price_lower_q, price_upper_q, ticker) VALUES ('XCC', 'CCB', '4', '8', '0939.HK');


SELECT * FROM stock_option_report a, stock_option_strikedg b
where a.txn_id = b.txn_id
and left(a.inst_name,10)='HHI.DEC-20'
and txn_date='2020-11-06'

CREATE VIEW stock_option_delta_at_all_strikes AS 
select substr(a.inst_name,1,9) AS cmonth,a.txn_date AS txn_date,b.strike AS strike,
sum(((if((right(a.inst_name,1) = 'C'),1,0) * a.oi) * b.delta)) AS call_delta,
sum(((if((right(a.inst_name,1) = 'P'),1,0) * a.oi) * b.delta)) AS put_delta,
sum((a.oi * b.delta)) AS abs_delta,
sum((a.oi * b.gamma)) AS total_gamma 
from (stock_option_report a join stock_option_strikedg b) 
where (a.txn_id = b.txn_id) 
group by cmonth,a.txn_date,b.strike

CREATE
VIEW stock_option_min_delta AS
    SELECT 
        b.cmonth AS cmonth,
        b.txn_date AS txn_date,
        b.strike AS strike,
        b.call_delta AS call_delta,
        b.put_delta AS put_delta,
        b.abs_delta AS abs_delta,
        b.total_gamma AS total_gamma
    FROM
        ((SELECT 
            stock_option_delta_at_all_strikes.cmonth AS cmonth,
                stock_option_delta_at_all_strikes.txn_date AS txn_date,
                MIN(stock_option_delta_at_all_strikes.abs_delta) AS abs_delta
        FROM
            stock_option_delta_at_all_strikes
        GROUP BY stock_option_delta_at_all_strikes.cmonth , stock_option_delta_at_all_strikes.txn_date) a
        JOIN stock_option_delta_at_all_strikes b ON (((a.txn_date = b.txn_date)
            AND (a.cmonth = b.cmonth)
            AND (a.abs_delta = b.abs_delta))))

CREATE
VIEW weekly_option_min_delta AS
    SELECT 
        b.cmonth AS cmonth,
        b.txn_date AS txn_date,
        b.strike AS strike,
        b.call_delta AS call_delta,
        b.put_delta AS put_delta,
        b.abs_delta AS abs_delta,
        b.total_gamma AS total_gamma
    FROM
        ((SELECT 
            weekly_option_delta_at_all_strikes.cmonth AS cmonth,
                weekly_option_delta_at_all_strikes.txn_date AS txn_date,
                MIN(weekly_option_delta_at_all_strikes.abs_delta) AS abs_delta
        FROM
            weekly_option_delta_at_all_strikes
        GROUP BY weekly_option_delta_at_all_strikes.cmonth , weekly_option_delta_at_all_strikes.txn_date) a
        JOIN weekly_option_delta_at_all_strikes b ON (((a.txn_date = b.txn_date)
            AND (a.cmonth = b.cmonth)
            AND (a.abs_delta = b.abs_delta))))

SELECT a.*, b.close, (a.strike - b.close) as price_diff
FROM storacle.option_daily_min_delta as a left join daily_stock_price as b
on a.txn_date=b.txn_date and left(a.cmonth,3)= b.hkats_code
and a.abs_delta >0 and a.txn_date > (curdate() - interval 1 month)
union all 
SELECT a.*, b.close, (a.strike - b.close) as price_diff
FROM storacle.option_daily_min_delta as a left join daily_stock_price as b
on a.txn_date=b.txn_date and left(a.cmonth,3)= b.hkats_code
and a.abs_delta >0 and a.txn_date > (curdate() - interval 1 month)

SET FOREIGN_KEY_CHECKS = 0; 
TRUNCATE table stock_option_report; 
SET FOREIGN_KEY_CHECKS = 1;            

show binary logs;
PURGE BINARY LOGS before '2020-11-08';

select substr(a.inst_name,1,9) AS cmonth,a.txn_date AS txn_date,b.strike AS strike,
sum(((if((right(a.inst_name,1) = 'C'),1,0) * a.oi) * b.delta)) AS call_delta,
sum(((if((right(a.inst_name,1) = 'P'),1,0) * a.oi) * b.delta)) AS put_delta,
sum((a.oi * b.delta)) AS abs_delta,
sum((a.oi * b.gamma)) AS total_gamma , count(*)
from (stock_option_report a join stock_option_strikedg b) 
where (a.txn_id = b.txn_id) 
and a.txn_date = "2020-11-03" and left(a.inst_name,9) = "AIA.NOV20"
group by cmonth,a.txn_date,b.strike

select a.*, b.*
from (stock_option_report a join stock_option_strikedg b) 
where (a.txn_id = b.txn_id) 
and a.txn_date = "2020-11-03" and left(a.inst_name,9) = "AIA.NOV20"
and a.strike =65

create table option_trade(
 trade_id INT NOT NULL AUTO_INCREMENT, 
 ord_id varchar(45) not null,
 t_date date NOT NULL,
 s_date date NOT NULL,
 inst VARCHAR(45)  NOT NULL,
 buy_sell VARCHAR(1)  NOT NULL, 
 price float  NOT NULL, 
 qty int NOT NULL, 
 fee float , 
 PRIMARY KEY (trade_id))

create table stock_trade(
 trade_id INT NOT NULL AUTO_INCREMENT, 
 ord_id varchar(45) not null,
 t_date date NOT NULL,
 s_date date NOT NULL,
 inst VARCHAR(45)  NOT NULL,
 buy_sell VARCHAR(1)  NOT NULL, 
 price float  NOT NULL, 
 qty int NOT NULL, 
 amt  float  NOT NULL, 
 fee float , 
 PRIMARY KEY (trade_id))
 

delete 
select *
from option_daily_report
where txn_date='2021-01-07';

# change xtxn_date nullable
CREATE TABLE option_daily_report (
  txn_id int NOT NULL AUTO_INCREMENT,
  inst_name varchar(45) NOT NULL,
  xtxn_date date DEFAULT NULL,
  xopen float DEFAULT NULL,
  xhigh float DEFAULT NULL,
  xlow float DEFAULT NULL,
  xclose float DEFAULT NULL,
  xvolume float DEFAULT NULL,
  txn_date date NOT NULL,
  open float DEFAULT NULL,
  high float DEFAULT NULL,
  low float DEFAULT NULL,
  close float DEFAULT NULL,
  volume float DEFAULT NULL,
  iv int DEFAULT NULL,
  oi int DEFAULT NULL,
  oi_change int DEFAULT NULL,
  stock_price float DEFAULT NULL,
  delta float DEFAULT NULL,
  gamma float DEFAULT NULL,
  PRIMARY KEY (txn_id),
  UNIQUE KEY idx_txn_date_instm (inst_name,txn_date)
) ENGINE=InnoDB AUTO_INCREMENT=896167 DEFAULT CHARSET=utf8

select close from daily_stock_price  where txn_date = (SELECT max(txn_date)  FROM storacle.daily_stock_price where hkats_code='HTI' and txn_date >= '2020-02-09');
SELECT *  FROM storacle.daily_stock_price where hkats_code='HSI' and txn_date >= '2020-02-08')

CREATE
VIEW stock_option_min_delta_1m AS
    SELECT 
        b.cmonth AS cmonth,
        b.txn_date AS txn_date,
        b.strike AS strike,
        b.call_delta AS call_delta,
        b.put_delta AS put_delta,
        b.abs_delta AS abs_delta,
        b.total_gamma AS total_gamma
    FROM
        ((SELECT 
            stock_option_delta_at_all_strikes.cmonth AS cmonth,
                stock_option_delta_at_all_strikes.txn_date AS txn_date,
                MIN(stock_option_delta_at_all_strikes.abs_delta) AS abs_delta
        FROM
            stock_option_delta_at_all_strikes
        where txn_date >  (curdate() - interval 1 month)
        GROUP BY stock_option_delta_at_all_strikes.cmonth , stock_option_delta_at_all_strikes.txn_date) a
        JOIN stock_option_delta_at_all_strikes b ON (((a.txn_date = b.txn_date)
            AND (a.cmonth = b.cmonth)
            AND (a.abs_delta = b.abs_delta))))


/*StockOption_LastPrice.xlsx 13/2/202139 */
SELECT *, substr(cmonth, 5,5) as contract_month, left(cmonth, 3) as symb FROM storacle.stock_option_delta_at_all_strikes where txn_date >  (curdate() - interval 1 month);

SELECT a.*, b.stock_price, round((a.strike - b.stock_price),2)  as price_diff,
substr(cmonth, 5,5) as contract_month, left(cmonth, 3) as symb
FROM storacle.stock_option_min_delta_1m a, stock_option_report b
where a.txn_date=b.txn_date and left(a.cmonth,9)= left(b.inst_name,9)
and a.strike = convert(substr(b.inst_name,11,6), float)
and right(b.inst_name,1) ='C'
and a.abs_delta >0;

/* Check DB Size */
SELECT table_schema "DB Name",
        ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) "DB Size in MB" 
FROM information_schema.tables 
GROUP BY table_schema; 

CREATE TABLE storacle.last_stock_price (
  price_id INT NOT NULL AUTO_INCREMENT,
  ticker varchar(45) NOT NULL,
  txn_date DATE NOT NULL,
  close FLOAT NULL,
  hkats_code varchar(12) NULL,
  PRIMARY KEY (price_id));

truncate last_stock_price;
insert into last_stock_price(ticker, txn_date, close, hkats_code)
SELECT left(inst_name,3) , date_add(txn_date , INTERVAL 1 DAY) ,  stock_price , left(inst_name, 3)  
FROM storacle.stock_option_report a
where txn_date = (SELECT max(txn_date)  FROM storacle.stock_option_report)
group by left(inst_name,3), txn_date;

CREATE UNIQUE INDEX idx_min_delta on storacle.t_stock_daily_min_delta (cmonth , txn_date ) ;

drop view t_stock_option_min_delta;
CREATE VIEW v_stock_option_min_delta AS
    SELECT 
        b.cmonth AS cmonth,
        b.txn_date AS txn_date,
        b.strike AS strike,
        b.call_delta AS call_delta,
        b.put_delta AS put_delta,
        b.abs_delta AS abs_delta,
        b.total_gamma AS total_gamma, 
        left(b.cmonth, 3) as symb, 
        substr(b.cmonth, 5,5) as month
    FROM
        ((SELECT 
            cmonth, txn_date, MIN(abs_delta) AS abs_delta
        FROM
            t_delta_all_strikes
        GROUP BY cmonth , txn_date) a
        JOIN t_delta_all_strikes b ON (((a.txn_date = b.txn_date)
            AND (a.cmonth = b.cmonth)
            AND (a.abs_delta = b.abs_delta))));

create view stock_report_price as
SELECT left(inst_name,3) as hkats_code, txn_date,  avg(stock_price) FROM stock_option_report
group by hkats_code, txn_date;

CREATE TABLE t_index_delta_all_strikes (
  cmonth varchar(20) DEFAULT NULL,
  txn_date date DEFAULT NULL,
  strike decimal(10,0) DEFAULT NULL,
  call_delta decimal(10,2) DEFAULT NULL,
  put_delta decimal(10,2) DEFAULT NULL,
  abs_delta decimal(10,2) DEFAULT NULL,
  total_gamma decimal(10,2) DEFAULT NULL
) 

CREATE TABLE t_weekly_delta_all_strikes (
  cmonth varchar(20) DEFAULT NULL,
  txn_date date DEFAULT NULL,
  strike decimal(10,0) DEFAULT NULL,
  call_delta decimal(10,2) DEFAULT NULL,
  put_delta decimal(10,2) DEFAULT NULL,
  abs_delta decimal(10,2) DEFAULT NULL,
  total_gamma decimal(10,2) DEFAULT NULL
) 
drop view t_index_option_min_delta;
CREATE VIEW t_index_option_min_delta AS
    SELECT 
        b.cmonth AS cmonth,
        b.txn_date AS txn_date,
        b.strike AS strike,
        b.call_delta AS call_delta,
        b.put_delta AS put_delta,
        b.abs_delta AS abs_delta,
        b.total_gamma AS total_gamma, 
        left(b.cmonth, 3) as symb, 
        substr(b.cmonth, 5,6) as month
    FROM
        ((SELECT 
            cmonth, txn_date, MIN(abs_delta) AS abs_delta
        FROM
            t_index_delta_all_strikes
        GROUP BY cmonth , txn_date) a
        JOIN t_index_delta_all_strikes b ON (((a.txn_date = b.txn_date)
            AND (a.cmonth = b.cmonth)
            AND (a.abs_delta = b.abs_delta))));

drop view  t_weekly_option_min_delta;
CREATE VIEW v_weekly_option_min_delta AS
SELECT 
        b.cmonth AS cmonth,
        b.txn_date AS txn_date,
        b.strike AS strike,
        b.call_delta AS call_delta,
        b.put_delta AS put_delta,
        b.abs_delta AS abs_delta,
        b.total_gamma AS total_gamma, 
        left(b.cmonth, 3) as symb, 
        right(b.cmonth, 6) as month
    FROM
        ((SELECT 
            cmonth, txn_date, MIN(abs_delta) AS abs_delta
        FROM
            t_weekly_delta_all_strikes
        GROUP BY cmonth , txn_date) a
        JOIN t_weekly_delta_all_strikes b ON (((a.txn_date = b.txn_date)
            AND (a.cmonth = b.cmonth)
            AND (a.abs_delta = b.abs_delta))));

// Stock Option update
insert into t_delta_all_strikes
(select substr(a.inst_name,1,9) AS cmonth,a.txn_date AS txn_date,b.strike AS strike,         sum(((if((substr(a.inst_name,18,1) = "C"),1,0) * a.oi) * b.delta)) AS call_delta,         sum(((if((substr(a.inst_name,18,1) = "P"),1,0) * a.oi) * b.delta)) AS put_delta,         sum((a.oi * b.delta)) AS abs_delta,         sum((a.oi * b.gamma)) AS total_gamma         from (stock_option_report a join stock_option_strikedg b) 
       where (a.txn_id = b.txn_id)        
and  a.txn_date ='2021-06-30'
 group by cmonth,a.txn_date,b.strike);
 
 DELETE FROM t_delta_all_strikes WHERE txn_date ="21-06-30";

// Index Option update
delete FROM t_index_delta_all_strikes WHERE txn_date ='2021-06-30';
select * FROM t_index_delta_all_strikes WHERE txn_date ='2021-06-30';
insert into t_index_delta_all_strikes
select substr(a.inst_name,1,10) AS cmonth,a.txn_date AS txn_date,b.strike AS strike,
         round(sum(((if((substr(a.inst_name,18,1) = "C"),1,0) * a.oi) * b.delta)),2) AS call_delta,
         round(sum(((if((substr(a.inst_name,18,1) = "P"),1,0) * a.oi) * b.delta)),2) AS put_delta,
         round(sum((a.oi * b.delta)),2) AS abs_delta,
         round(sum((a.oi * b.gamma)),2) AS total_gamma
         from (option_daily_report a join option_daily_strikedg b)
         where (a.txn_id = b.txn_id)
         and b.delta > 0
         and  a.txn_date ="2021-06-30" 
         group by cmonth,a.txn_date,b.strike;

-- SELECT  substr(a.inst_name, 5,5) as cmonth, 
drop VIEW v_stock_option_report cascade;
CREATE VIEW v_stock_option_report AS
SELECT  (date_trunc('MONTH', 
  ('01-'||substr(a.inst_name, 5,3)||'-'||substr(a.inst_name, 8,2) )::date) 
  + INTERVAL '1 MONTH - 1 day')::DATE  as cmonth, 
left(a.inst_name, 3) as symb,
a.txn_date as txn_date, 
a.stock_price,
substr(a.inst_name,11,6)  as strike,  
a.oi as coi, 
round(a.delta,2) as cdelta, 
round(a.gamma,2) as cgamma,
a.close as c_close,
round(a.gamma*a.oi,2) as cnetgamma, 
round(a.delta*a.oi ,2) as cnetdelta,
-1*b.oi as poi, 
round(-1*b.delta,2) as pdelta, 
round(b.gamma,2) as pgamma,  
b.close as p_close,
round(b.gamma*b.oi,2) as pnetgamma, 
round(-1*b.delta*b.oi,2) as pnetdelta,
round((a.gamma*a.oi + b.gamma*b.oi),2) as net_gamma, 
round((a.delta*a.oi - b.delta*b.oi),2) as net_delta,
a.oi_change as coi_change,
-1*b.oi_change as poi_change
FROM stock_option_report a, stock_option_report b
where substr(a.inst_name,18,1) ='C'
and substr(b.inst_name,18,1) ='P'
and substr(a.inst_name,1,16)= substr(b.inst_name,1,16)
and a. txn_date= b.txn_date;

drop view  v_stock_option_value;
CREATE VIEW v_stock_option_value AS
SELECT a.*, 
round(a.coi * b.contract_size * a.cdelta, 0) as coi_share, 
round (a.coi * b.contract_size * a.stock_price * a.cdelta, 0) as coi_delta_value, 
round(a.coi * b.contract_size * a.c_close, 0) as coi_option_value,
round(-1 * a.poi * b.contract_size * a.pdelta, 0) as poi_share , 
round (-1 * a.poi * b.contract_size * a.stock_price * a.pdelta, 0) as poi_delta_value, 
round(a.poi * b.contract_size * a.p_close, 0) as poi_option_value
FROM v_stock_option_report a, hkats_code b
where a.symb=b.hkats_code;

select * from v_stock_option_value a
where a.symb ='TCH' and  a.txn_date='2022-04-12';

/* Index Chart */
/* substr(a.inst_name, 5,6) as cmonth,  */
drop VIEW v_index_option_report CASCADE;
CREATE VIEW v_index_option_report AS
SELECT  (date_trunc('MONTH', ('01-'|| substr(a.inst_name, 5,6) )::date) 
  + INTERVAL '1 MONTH - 1 day')::DATE  as cmonth, 
left(a.inst_name, 3) as symb,
a.txn_date as txn_date, 
a.stock_price,
substr(a.inst_name,12,5)  as strike,  
a.oi as coi, 
round(a.delta,2) as cdelta, 
round(a.gamma,2) as cgamma,
a.close as c_close,
round(a.gamma*a.oi,2) as cnetgamma, 
round(a.delta*a.oi ,2) as cnetdelta,
-1*b.oi as poi, 
round(-1*b.delta,2) as pdelta, 
round(b.gamma,2) as pgamma,  
b.close as p_close,
round(b.gamma*b.oi,2) as pnetgamma, 
round(-1*b.delta*b.oi,2) as pnetdelta,
round((a.gamma*a.oi + b.gamma*b.oi),2) as net_gamma, 
round((a.delta*a.oi - b.delta*b.oi),2) as net_delta,
a.oi_change as coi_change,
-1*b.oi_change as poi_change
FROM option_daily_report a, option_daily_report b
where substr(a.inst_name,18,1) ='C'
and substr(b.inst_name,18,1) ='P'
and substr(a.inst_name,1,16)= substr(b.inst_name,1,16)
and a. txn_date= b.txn_date


drop view  v_index_option_value;
CREATE VIEW v_index_option_value AS
SELECT a.*, 
round(a.coi * b.contract_size * a.cdelta, 0) as coi_share, 
round (a.coi * b.contract_size * a.stock_price * a.cdelta, 0) as coi_delta_value, 
round(a.coi * b.contract_size * a.c_close, 0) as coi_option_value,
round(-1 * a.poi * b.contract_size * a.pdelta, 0) as poi_share , 
round (-1 * a.poi * b.contract_size * a.stock_price * a.pdelta, 0) as poi_delta_value, 
round(a.poi * b.contract_size * a.p_close, 0) as poi_option_value
FROM v_index_option_report a, hkats_code b
where a.symb=b.hkats_code

select * from v_index_option_value a
where a.symb ='HSI' and  a.txn_date='2022-04-08';


 CREATE FUNCTION ROUND(float,int) RETURNS NUMERIC AS $$
    SELECT ROUND($1::numeric,$2)
 $$ language SQL IMMUTABLE;

/* Weekly Index Chart */
/* SELECT  substr(a.inst_name, 5,9) as cmonth, */
/* Change Date format for display sorting */
drop VIEW v_weekly_option_report CASCADE;
CREATE VIEW v_weekly_option_report AS
SELECT  to_date(substr(a.inst_name, 5,9), 'dd-mon-yy') as cmonth, 
left(a.inst_name, 3) as symb,
a.txn_date as txn_date, 
a.stock_price,
substr(a.inst_name,15,5)  as strike,  
a.oi as coi, 
round(a.delta,2) as cdelta, 
round(a.gamma,2) as cgamma,
a.close as c_close,
round(a.gamma*a.oi,2) as cnetgamma, 
round(a.delta*a.oi ,2) as cnetdelta,
-1*b.oi as poi, 
round(-1*b.delta,2) as pdelta, 
round(b.gamma,2) as pgamma,  
b.close as p_close,
round(b.gamma*b.oi,2) as pnetgamma, 
round(-1*b.delta*b.oi,2) as pnetdelta,
round((a.gamma*a.oi + b.gamma*b.oi),2) as net_gamma, 
round((a.delta*a.oi - b.delta*b.oi),2) as net_delta,
a.oi_change as coi_change,
-1*b.oi_change as poi_change
FROM weekly_option_daily_report a, weekly_option_daily_report b
where right(a.inst_name,1) ='C'
and right(b.inst_name,1) ='P'
and left(a.inst_name,19)= left(b.inst_name,19)
and a. txn_date= b.txn_date;


drop view  v_weekly_option_value;
CREATE VIEW v_weekly_option_value AS
SELECT a.*, 
round(a.coi * b.contract_size * a.cdelta, 0) as coi_share, 
round (a.coi * b.contract_size * a.stock_price * a.cdelta, 0) as coi_delta_value, 
round(a.coi * b.contract_size * a.c_close, 0) as coi_option_value,
round(-1 * a.poi * b.contract_size * a.pdelta, 0) as poi_share , 
round (-1 * a.poi * b.contract_size * a.stock_price * a.pdelta, 0) as poi_delta_value, 
round(a.poi * b.contract_size * a.p_close, 0) as poi_option_value
FROM v_weekly_option_report a, hkats_code b
where a.symb=b.hkats_code;

select * from v_weekly_option_value a
where a.symb ='HSI' and  a.txn_date='2022-apr-08';


create VIEW v_index_option_min_delta AS 
select b.cmonth AS cmonth,b.txn_date AS txn_date,b.strike AS strike,
b.call_delta AS call_delta,b.put_delta AS put_delta,b.abs_delta AS abs_delta,
b.total_gamma AS total_gamma,left(b.cmonth,3) AS symb,substr(b.cmonth,5,6) AS month 
from ((select t_index_delta_all_strikes.cmonth AS cmonth,t_index_delta_all_strikes.txn_date AS txn_date,
min(t_index_delta_all_strikes.abs_delta) AS abs_delta 
from t_index_delta_all_strikes
group by t_index_delta_all_strikes.cmonth,t_index_delta_all_strikes.txn_date) a 
join t_index_delta_all_strikes b on(((a.txn_date = b.txn_date) 
and (a.cmonth = b.cmonth) and (a.abs_delta = b.abs_delta))))


create VIEW v_weekly_option_min_delta AS 
select b.cmonth AS cmonth,b.txn_date AS txn_date,b.strike AS strike,b.call_delta AS call_delta,b.put_delta AS put_delta,b.abs_delta AS abs_delta,
b.total_gamma AS total_gamma,left(b.cmonth,3) AS symb,right(b.cmonth,6) AS month from ((select t_weekly_delta_all_strikes.cmonth AS cmonth,
t_weekly_delta_all_strikes.txn_date AS txn_date,min(t_weekly_delta_all_strikes.abs_delta) AS abs_delta 
from t_weekly_delta_all_strikes 
group by t_weekly_delta_all_strikes.cmonth,t_weekly_delta_all_strikes.txn_date) a 
join t_weekly_delta_all_strikes b on(((a.txn_date = b.txn_date) 
and (a.cmonth = b.cmonth) and (a.abs_delta = b.abs_delta))))

update t_delta_all_strikes set txn_date = to_date(txn_date_txt,'YYYY-MM-DD');

select substr(a.inst_name,1,13) AS cmonth, 
        a.txn_date AS txn_date,b.strike AS strike, 
        sum(((case when substr(a.inst_name,21,1) = 'C' then 1 else 0 end) * a.oi) * b.delta) AS call_delta, 
        sum(((CASE WHEN substr(a.inst_name,21,1) = 'P' THEN 1 ELSE 0 END) * a.oi) * b.delta) AS put_delta, 
        sum((a.oi * b.delta)) AS abs_delta, 
        sum((a.oi * b.gamma)) AS total_gamma 
        from weekly_option_daily_report a, weekly_option_daily_strikedg b 
        where (a.txn_id = b.txn_id) and b.delta > 0 
        and  a.txn_date = to_date('2023-04-12', 'YYYY-MM-DD') 
        group by substr(a.inst_name,1,13), a.txn_date, b.strike

select substr(a.inst_name,1,13) AS cmonth, 
        a.txn_date AS txn_date,
        substr(a.inst_name,15,5) AS strike, 
        round(avg((case when substr(a.inst_name,21,1) = 'C' then a.iv else 0 end) ),1) AS call_iv, 
        round(avg((case when substr(a.inst_name,21,1) = 'P' then a.iv else 0 end) ),1) AS put_iv 
        from weekly_option_daily_report a
        where a.txn_date = to_date('2023-04-12', 'YYYY-MM-DD') 
        group by substr(a.inst_name,1,13), a.txn_date, substr(a.inst_name,15,5)


Driver=MySQL ODBC 8.0 ANSI Driver;SERVER=localhost;DATABASE=storacle;PORT=3306
Driver=MySQL ODBC 8.0 ANSI Driver;SERVER=localhost;UID=admin;PWD=will3290;DATABASE=storacle;PORT=3306
= Value.NativeQuery(PostgreSQL.Database("localhost:5433", "storacle"), ";", null)

# Min Delta Trends
SELECT a.*, b.close, (a.strike - b.close) as price_diff
FROM storacle.option_daily_min_delta a, daily_stock_price b
where a.txn_date=b.txn_date and left(a.cmonth,3)= b.hkats_code
and a.abs_delta >0 and a.txn_date > '2020-10-01';


SELECT *, substr(cmonth, 5,6) as contract_month, left(cmonth, 3) as symb from option_delta_at_all_strikes where txn_date > '2020-10-01'
union all 
SELECT *,substr(cmonth, 8,6) as contract_month, left(cmonth,3) as symb from weekly_option_delta_at_all_strikes where txn_date > '2020-10-01';


SELECT *, substr(cmonth, 5,5) as contract_month, left(cmonth, 3) as symb FROM storacle.stock_option_delta_at_all_strikes;

SELECT  substr(a.inst_name, 5,5) as cmonth, left(a.inst_name, 3) as symb,
a.txn_date as txn_date, 
convert(substr(a.inst_name,11,6) as strike, decimals(4,2)),  
a.oi as coi, a.delta as cdelta, a.gamma as cgamma, 
a.gamma*a.oi as cnetgamma, a.delta*a.oi as cnetdelta,
-1*b.oi as poi, -1*b.delta as pdelta, b.gamma as pgamma, 
b.gamma*b.oi as pnetgamma, 
-1*b.delta*b.oi as pnetdelta,
(a.gamma*a.oi + b.gamma*b.oi) as net_gamma, 
(a.delta*a.oi - b.delta*b.oi) as net_delta,
a.oi_change as coi_change,
-1*b.oi_change as poi_change
FROM stock_option_report a, stock_option_report b
where substr(a.inst_name,18,1) ='C'
and substr(b.inst_name,18,1) ='P'
and substr(a.inst_name,1,16)= substr(b.inst_name,1,16)
and a. txn_date= b.txn_date

SELECT *, substr(cmonth, 5,5) as contract_month, left(cmonth, 3) as symb
FROM stock_option_min_delta

SELECT a.*, b.close, (a.strike - b.close) as price_diff
FROM storacle.option_daily_min_delta a, daily_stock_price b
where a.txn_date=b.txn_date and left(a.cmonth,3)= b.hkats_code
and a.abs_delta >0 and a.txn_date > (curdate() - interval 1 month)

SELECT *, substr(cmonth, 5,6) as contract_month, left(cmonth, 3) as symb from option_delta_at_all_strikes where txn_date >  (curdate() - interval 1 month)
union all 
SELECT *,substr(cmonth, 8,6) as contract_month, left(cmonth,3) as symb from weekly_option_delta_at_all_strikes where txn_date >  (curdate() - interval 1 month);


SELECT *, substr(cmonth, 5,5) as contract_month, left(cmonth, 3) as symb
FROM stock_option_min_delta
union all 
SELECT left(inst_name,9) as cmonth, date_add(txn_date , INTERVAL 1 DAY) ,  stock_price , 0,0,0,0, substr(inst_name, 5,5) as contract_month, left(inst_name, 3) as symb FROM storacle.stock_option_report
where txn_date = (SELECT max(txn_date)  FROM storacle.stock_option_report)
group by cmonth, txn_date, contract_month, symb



SELECT price FROM storacle.stock_option_report where 


select close from daily_stock_price  where txn_date = (SELECT max(txn_date)  FROM storacle.daily_stock_price where hkats_code='HNP' and txn_date >= '2020-02-09');

cmonth	txn_date	strike	call_delta	put_delta	abs_delta	total_gamma	contract_month	symb
HNP.FEB21	9/2/2021	2.9	215.7969387	31.62271783	247.4196566	603.7583832	FEB21	HNP

/* Outer Join*/
SELECT a.*, b.close, (a.strike - b.close) as price_diff
FROM storacle.option_daily_min_delta as a left join daily_stock_price as b
where a.txn_date=b.txn_date and left(a.cmonth,3)= b.hkats_code
and a.abs_delta >0 and a.txn_date > (curdate() - interval 1 month)


SELECT a.*, b.close, (a.strike - b.close) as price_diff
FROM storacle.option_daily_min_delta as a 
left join daily_stock_price as b
on a.txn_date=b.txn_date and left(a.cmonth,3)= b.hkats_code
and a.abs_delta >0 and a.txn_date > (curdate() - interval 1 month)

SELECT a.*, b.close, (a.strike - b.close) as price_diff
FROM 
(select * FROM storacle.option_daily_min_delta
union all
SELECT * FROM storacle.weekly_option_daily_min_delta) as a
left join daily_stock_price as b
on a.txn_date=b.txn_date and left(a.cmonth,3)= b.hkats_code
where a.abs_delta >0 and a.txn_date > (curdate() - interval 1 month)


select * from weekly_option_daily_min_delta where txn_date > (curdate() - interval 1 month)

SELECT a.*, b.stock_price, round((a.strike - b.stock_price),2)  as price_diff,
substr(cmonth, 5,5) as contract_month, left(cmonth, 3) as symb
FROM storacle.stock_option_min_delta_1m a, stock_option_report b
where a.txn_date=b.txn_date and left(a.cmonth,9)= left(b.inst_name,9)
and a.strike = convert(substr(b.inst_name,11,6), float)
and right(b.inst_name,1) ='C'
and a.abs_delta >0
and b.txn_date > (curdate() - interval 14 day);

SELECT a.*, b.close, (a.strike - b.close) as price_diff
FROM 
(select * FROM storacle.option_daily_min_delta
union all
SELECT * FROM storacle.weekly_option_daily_min_delta) as a
left join daily_stock_price as b
on a.txn_date=b.txn_date and left(a.cmonth,3)= b.hkats_code
where a.abs_delta >0 and a.txn_date > (curdate() - interval 1 month)

select  a.*, b.close, (a.strike - b.close) as price_diff 
from 
(select * FROM storacle.option_daily_min_delta_1m
union all
SELECT * FROM storacle.weekly_option_daily_min_delta_1m) as a
left join daily_stock_price as b
on a.txn_date=b.txn_date and left(a.cmonth,3)= b.hkats_code

select * FROM storacle.option_daily_min_delta_1m
union all
SELECT * FROM storacle.weekly_option_daily_min_delta_1m
union all
select hkats_code, txn_date, close, 0, 0, 0, 0 from daily_stock_price
where txn_date > (curdate() - interval 1 month)

SELECT *, substr(cmonth, 5,5) as contract_month, left(cmonth, 3) as symb
FROM t_stock_option_min_delta
union all 
SELECT left(inst_name,9) as cmonth, date_add(txn_date , INTERVAL 1 DAY) ,  stock_price , 0,0,0,0, substr(inst_name, 5,5) as contract_month, left(inst_name, 3) as symb FROM storacle.stock_option_report
where txn_date = (SELECT max(txn_date)  FROM storacle.stock_option_report)
group by cmonth, txn_date, contract_month, symb

SELECT *
FROM t_stock_daily_min_delta
union all 
select 'CLOSE', txn_date, close, 0,0,0,0, 'LAST', hkats_code
FROM last_stock_price

insert into last_stock_price(ticker, txn_date, close, hkats_code)
SELECT left(inst_name,3) , date_add(txn_date , INTERVAL 1 DAY) ,  stock_price , left(inst_name, 3)  
FROM storacle.stock_option_report a
where txn_date = (SELECT max(txn_date)  FROM storacle.stock_option_report)
group by left(inst_name,3), txn_date

drop VIEW v_stock_option_min_delta;
CREATE VIEW v_stock_option_min_delta AS
    SELECT 
        b.cmonth AS cmonth,
        b.txn_date AS txn_date,
        b.strike AS strike,
        b.call_delta AS call_delta,
        b.put_delta AS put_delta,
        b.abs_delta AS abs_delta,
        b.total_gamma AS total_gamma, 
        left(b.cmonth, 3) as symb, 
        substr(b.cmonth, 5,5) as month
    FROM
        ((SELECT 
            cmonth, txn_date, MIN(abs_delta) AS abs_delta
        FROM
            t_delta_all_strikes
        GROUP BY cmonth , txn_date) a
        JOIN t_delta_all_strikes b ON (((a.txn_date = b.txn_date)
            AND (a.cmonth = b.cmonth)
            AND (a.abs_delta = b.abs_delta))));


SELECT *
FROM v_stock_option_min_delta
union all 
SELECT left(inst_name,9) as cmonth, date_add(txn_date , INTERVAL 1 DAY) ,  stock_price , 0,0,0,0, left(inst_name, 3) as symb, substr(inst_name, 5,5) as contract_month FROM storacle.stock_option_report
where txn_date = (SELECT max(txn_date)  FROM storacle.stock_option_report)
group by cmonth, txn_date, contract_month, symb

# v_stock_option_min_delta
SELECT *
FROM v_stock_option_min_delta
union all 
SELECT 'STOCK_PRICE', txn_date ,  stock_price , 0,0,0,0, hkats_code, upper(date_format(txn_date, '%b%y')) FROM stock_report_price

SELECT txn_date, cmonth, count(*) FROM storacle.t_delta_all_strikes where cmonth like 'XCC%' group by cmonth, txn_date;

select * FROM v_index_option_min_delta
union all 
select * FROM v_weekly_option_min_delta
union all
SELECT 'STOCK_PRICE', txn_date ,  close , 0,0,0,0, hkats_code, upper(date_format(txn_date, '%b-%y')) 
FROM daily_stock_price
where txn_date > (curdate() - interval 1 month)

SELECT *, substr(cmonth, 5,6) as contract_month, left(cmonth, 3) as symb from t_index_delta_all_strikes
union all 
SELECT *,substr(cmonth, 8,6) as contract_month, left(cmonth,3) as symb from t_weekly_delta_all_strikes

#Settlement Price Analysis

select a.*, p.*
from v_weekly_option_min_delta a, daily_stock_price p,
(select cmonth, max(txn_date) txn_date from weekly_option_daily_min_delta group by cmonth) b
where a.cmonth= b.cmonth and a.txn_date= b.txn_date
and a.txn_date = p.txn_date-1 and left(a.cmonth,3) = p.hkats_code

# TrendSQL
SELECT a.*, b.close, (a.strike - b.close) as price_diff
FROM 
(select * FROM v_index_option_min_delta
union all
SELECT * FROM v_weekly_option_min_delta) as a
left join daily_stock_price as b
on a.txn_date=b.txn_date and left(a.cmonth,3)= b.hkats_code
where a.abs_delta >0 and a.txn_date > (curdate() - interval 1 month)

#DELTA
SELECT *, substr(cmonth, 5,5) as contract_month, left(cmonth, 3) as symb FROM t_delta_all_strikes

#STOCK TREND
SELECT a.*, b.stock_price, round((a.strike - b.stock_price),2)  as price_diff,
substr(cmonth, 5,5) as contract_month, left(cmonth, 3) as symb
FROM v_stock_option_min_delta a, stock_option_report b
where a.txn_date=b.txn_date and left(a.cmonth,9)= left(b.inst_name,9)
and a.strike = convert(substr(b.inst_name,11,6), float)
and right(b.inst_name,1) ='C'
and a.abs_delta >0
and b.txn_date > (curdate() - interval 14 day);


# Monthly Settlement
select a.*, p.*
from v_index_option_min_delta a, daily_stock_price p,
(
select left(inst_name, 10) cmonth, max(txn_date) txn_date 
from option_daily_report 
where txn_date < curdate()
group by  left(inst_name, 10)
) b
where a.cmonth= b.cmonth and a.txn_date= b.txn_date-1
and a.txn_date = p.txn_date-1 and left(a.cmonth,3) = p.hkats_code

# Weekly Settlement
select a.*, p.*
from v_weekly_option_min_delta a, daily_stock_price p,
(select cmonth, max(txn_date) txn_date from weekly_option_daily_min_delta group by cmonth) b
where a.cmonth= b.cmonth and a.txn_date= b.txn_date
and a.txn_date = p.txn_date-1 and left(a.cmonth,3) = p.hkats_code

# Stock Settlement
select  b.*, a.stock_price
FROM 
(
select left(inst_name, 9) cmonth, max(txn_date) txn_date , stock_price
from stock_option_report 
where LAST_DAY(STR_TO_DATE(concat( '01', substr(inst_name,5,5)),'%d%b%y'))  < curdate()
group by  left(inst_name, 9)
) a, v_stock_option_min_delta b
where a.cmonth = b.cmonth and a.txn_date=b.txn_date+1



select substr(a.inst_name,1,13) AS cmonth,a.txn_date AS txn_date,b.strike AS strike, 
        sum(((if((substr(a.inst_name,21,1) = "C"),1,0) * a.oi) * b.delta)) AS call_delta, 
        sum(((if((substr(a.inst_name,21,1) = "P"),1,0) * a.oi) * b.delta)) AS put_delta, 
        sum((a.oi * b.delta)) AS abs_delta, 
        sum((a.oi * b.gamma)) AS total_gamma 
        from (weekly_option_daily_report a join weekly_option_daily_strikedg b) 
        where (a.txn_id = b.txn_id) 
        and b.delta > 0 
        and  a.txn_date ='2021-05-31'
        group by cmonth,a.txn_date,b.strike

SELECT *, substr(cmonth, 5,6) as contract_month, left(cmonth, 3) as symb from t_index_delta_all_strikes
where txn_date > (curdate() - interval 1 month)
union all 
SELECT *,substr(cmonth, 8,6) as contract_month, left(cmonth,3) as symb from t_weekly_delta_all_strikes
WHERE txn_date > (curdate() - interval 1 month)

# DELTA DECAY
select  left(inst_name, 3) symb, right(inst_name,1) cp, left(inst_name, 10) cmonth, oi*charm/365 d_delta,
option_daily_report.* FROM option_daily_report where left(inst_name, 3) ='HSI' and txn_date='2021-06-18';

select  right(inst_name,1) cp, left(inst_name, 3) symb,  left(inst_name, 10) cmonth, substr(inst_name,12,5) strike, oi*charm/365 d_delta,
        (((if((right(inst_name,1) = 'C'),1,0) * oi) *delta))  call_delta, 
        (((if((right(inst_name,1) = 'P'),1,0) * oi) * delta))  put_delta, 
        ((oi * delta))  abs_delta, 
        ((oi * gamma))  total_gamma, 
        option_daily_report.* FROM option_daily_report where txn_date> (curdate() - interval 5 day);



select substr(a.inst_name,1,9) AS cmonth,a.txn_date AS txn_date,b.strike AS strike,         sum(((if((substr(a.inst_name,18,1) = "C"),1,0) * a.oi) * b.delta)) AS call_delta,         sum(((if((substr(a.inst_name,18,1) = "P"),1,0) * a.oi) * b.delta)) AS put_delta,         sum((a.oi * b.delta)) AS abs_delta,         sum((a.oi * b.gamma)) AS total_gamma         
from (stock_option_report a join stock_option_strikedg b)        
where (a.txn_id = b.txn_id)         
and  a.txn_date ="21-07-05"       
and left(a.inst_name,3) ='A50'  
group by cmonth,a.txn_date,b.strike

SELECT 
        b.cmonth AS cmonth,
        b.txn_date AS txn_date,
        b.strike AS strike,
        b.call_delta AS call_delta,
        b.put_delta AS put_delta,
        b.abs_delta AS abs_delta,
        b.total_gamma AS total_gamma, 
        left(b.cmonth, 3) as symb, 
        right(b.cmonth, 6) as month
    FROM
        ((SELECT 
            cmonth, txn_date, MIN(abs_delta) AS abs_delta
        FROM
            t_weekly_delta_all_strikes
        GROUP BY cmonth , txn_date) a
        JOIN t_weekly_delta_all_strikes b ON (((a.txn_date = b.txn_date)
            AND (a.cmonth = b.cmonth)
            AND (a.abs_delta = b.abs_delta))))
    where b.txn_date = '19-JUL-2021';        


SELECT  substr(a.inst_name, 5,5) as cmonth, left(a.inst_name, 3) as symb,
a.txn_date as txn_date, a.stock_price,
substr(a.inst_name,11,6)  as strike,  
a.oi as coi, a.delta as cdelta, a.gamma as cgamma, a.close as c_close,
a.gamma*a.oi as cnetgamma, a.delta*a.oi as cnetdelta,
-1*b.oi as poi, -1*b.delta as pdelta, b.gamma as pgamma,  b.close as p_close,
b.gamma*b.oi as pnetgamma, 
-1*b.delta*b.oi as pnetdelta,
(a.gamma*a.oi + b.gamma*b.oi) as net_gamma, 
(a.delta*a.oi - b.delta*b.oi) as net_delta,
a.oi_change as coi_change,
-1*b.oi_change as poi_change
FROM stock_option_report a, stock_option_report b
where substr(a.inst_name,18,1) ='C'
and substr(b.inst_name,18,1) ='P'
and substr(a.inst_name,1,16)= substr(b.inst_name,1,16)
and a. txn_date= b.txn_date
and left(a.inst_name,3)='AIA' and  a.txn_date='2021-09-10';


SELECT a.*, 
round(a.coi * b.contract_size * a.cdelta, 0) as coi_share, 
round (a.coi * b.contract_size * a.stock_price * a.cdelta, 0) as coi_delta_value, 
round(a.coi * b.contract_size * a.c_close, 0) as coi_option_value,
round(-1 * a.poi * b.contract_size * a.pdelta, 0) as poi_share , 
round (a.poi * b.contract_size * a.stock_price * a.pdelta, 0) as poi_delta_value, 
round(-1 * a.poi * b.contract_size * a.p_close, 0) as poi_option_value
FROM storacle.v_stock_option_report a, hkats_code b
where a.symb=b.hkats_code
and a.symb ='AIA' and  a.txn_date='2021-09-10';


/*  where left(inst_name,13) ='TCH.SEP21.950' */

select * from stock_option_report
where left(inst_name,9) ='TCH.SEP21' and  txn_date='2021-09-17' ;
select * from stock_option_report
where left(inst_name,9) ='TCH.SEP21' and  txn_date='2021-09-17' ;

select a.txn_date, count(*) from weekly_option_daily_report a, weekly_option_daily_strikedg b
where  txn_date >='2021-09-10' 
and a.txn_id=b.txn_id
group by a.txn_date;

delete from weekly_option_daily_report where  txn_date='2024-08-08' ;

select a.txn_date, count(*) from option_daily_report a, option_daily_strikedg b
where  txn_date >='2021-09-10' 
and a.txn_id=b.txn_id
group by a.txn_date;

delete from option_daily_report where  txn_date='2021-09-17' ;

select a.txn_date, count(*) from stock_option_report a, stock_option_strikedg b
where  txn_date >='2021-09-10' 
and a.txn_id=b.txn_id
group by a.txn_date;


select * from stock_option_report a, stock_option_strikedg b
where inst_name ='PAI.SEP21.100.00.P' and  txn_date='2021-09-17' 
and a.txn_id=b.txn_id;

delete from stock_option_report where  txn_date>='2024-11-12' ;

delete from stock_option_report where  txn_date='2021-09-17' and left(inst_name,9) ='TCH.SEP21';

select * from stock_option_strikedg
where txn_id=3262081 ;

select * from stock_option_report
where txn_id=3262001;

select *, round(oi*delta,0) from stock_option_report where left(inst_name,9) ='ALB.SEP21' and  txn_date='2021-09-17'  and oi*delta > 100;

select * from v_stock_option_value where symb = 'TCH' and  txn_date='2021-09-17' and cmonth='SEP21';

drop view  v_stock_option_value;
CREATE VIEW v_stock_option_value AS
SELECT a.*, 
round(a.coi * b.contract_size * a.cdelta, 0) as coi_share, 
round (a.coi * b.contract_size * a.stock_price * a.cdelta, 0) as coi_delta_value, 
round(a.coi * b.contract_size * a.c_close, 0) as coi_option_value,
round(-1 * a.poi * b.contract_size * a.pdelta, 0) as poi_share , 
round (-1 * a.poi * b.contract_size * a.stock_price * a.pdelta, 0) as poi_delta_value, 
round(a.poi * b.contract_size * a.p_close, 0) as poi_option_value
FROM storacle.v_stock_option_report a, hkats_code b
where a.symb=b.hkats_code


select symb, cmonth, max(txn_date) as txn_date
from v_stock_option_value 
group by  symb, cmonth


SELECT *, substr(cmonth, 5,6) as contract_month, 
left(cmonth, 3) as symb 
from t_index_delta_all_strikes
where txn_date > (CURRENT_DATE - 5)
union all
SELECT *,substr(cmonth, 8,6) as contract_month, left(cmonth,3) as symb 
from t_weekly_delta_all_strikes
WHERE txn_date > (CURRENT_DATE - 5);


select substr(a.inst_name,1,10) AS cmonth, 
        a.txn_date AS txn_date,b.strike AS strike,
        sum(((case when substr(a.inst_name,18,1) = 'C' then 1 else 0 end) * a.oi) * b.delta) AS call_delta, 
        sum(((CASE WHEN substr(a.inst_name,18,1) = 'P' THEN 1 ELSE 0 END) * a.oi) * b.delta) AS put_delta, 
        sum((a.oi * b.delta)) AS abs_delta, 
        sum((a.oi * b.gamma)) AS total_gamma ,
		sum(b.gamma)
        from option_daily_report a, option_daily_strikedg b 
        where (a.txn_id = b.txn_id) and b.delta > 0 
        and  a.txn_date = to_date('2021-12-31', 'YY-MM-DD HH24:MI:SS') 
		and substr(a.inst_name,1,10)='HSI.JAN-22'
        group by substr(a.inst_name,1,10), a.txn_date, b.strike

select *
        from option_daily_report a, option_daily_strikedg b 
        where (a.txn_id = b.txn_id) and b.delta > 0 
        and  a.txn_date = to_date('2021-12-31', 'YY-MM-DD HH24:MI:SS') 
		and substr(a.inst_name,1,10)='HSI.JAN-22'


select * FROM v_index_option_min_delta
union all 
select * FROM v_weekly_option_min_delta
union all
SELECT 'STOCK_PRICE', txn_date ,  close , 0,0,0,0, hkats_code, 
to_char(txn_date, '%b-%y') FROM daily_stock_price
where txn_date > (CURRENT_DATE - 20)


SELECT  a.txn_id, a.txn_date, substr(a.inst_name,1,3) as symb,  
    substr(a.inst_name,5,6) as cont_month,  
    to_number(substr(a.inst_name,12,5),'999999') as strike,  
    a.delta,
    substr(a.inst_name,1,16) as inst,  
    substr(a.inst_name,18,1) as cp,  
    a.close, a.iv as iv, a.oi,
    a.stock_price as stock_price 
    FROM option_daily_report a 
    where a.txn_date >= to_date('2021-12-01', 'YYYY-MM-DD HH24:MI:SS') 
    and left(a.inst_name,10) = 'HSI.DEC-21'

# Stock MinDelta 
SELECT a.*, b.stock_price, round((a.strike - b.stock_price),2)  as price_diff,
substr(cmonth, 5,5) as contract_month, left(cmonth, 3) as symb
FROM v_stock_option_min_delta a, stock_option_report b
where a.txn_date=b.txn_date and left(a.cmonth,9)= left(b.inst_name,9)
and a.strike = to_number(substr(b.inst_name,11,6), '9999.99')
and right(b.inst_name,1) ='P'
and a.abs_delta >0
and b.txn_date > (current_date - 14 );

(SELECT *
FROM v_stock_option_min_delta
where txn_date > (current_date - 30 ))
union all 
(SELECT left(inst_name,9) as cmonth, txn_date +1 ,  stock_price , 0,0,0,0, left(inst_name, 3) as symb, 
substr(inst_name, 5,5) as contract_month FROM stock_option_report
where txn_date = (SELECT max(txn_date)  FROM stock_option_report)
group by cmonth, txn_date, stock_price, contract_month, symb)


select * from v_stock_option_daily_report
where txn_date > (current_date - 30 )
and moneyness >= 0.9 and moneyness<= 1.1 and iv>0

select cmonth, (date_trunc('MONTH', ('01-'|| cmonth )::date) + INTERVAL '1 MONTH - 1 day')::DATE from v_index_option_value limit 1;

select symb, cmonth, txn_date, count(*) as row_count 
    from v_weekly_option_value where txn_date = (select max(txn_date) from v_weekly_option_value ) 
    group by symb, cmonth, txn_date;

delete from weekly_option_daily_report where  txn_date='2023-04-12' ;    

SELECT * from (
select a.inst_name,a.txn_date,
        round(avg((case when substr(a.inst_name,21,1) = 'C' then a.iv else 0 end) ),1) AS call_iv, 
        round(avg((case when substr(a.inst_name,21,1) = 'P' then a.iv else 0 end) ),1) AS put_iv 
        from weekly_option_daily_report a
        where a.txn_date = to_date('2023-04-12', 'YYYY-MM-DD') 
        group by a.inst_name, a.txn_date
) as option_iv
WHERE call_iv > 0 or put_iv > 0

# Select today's date -5
select current_date - 5;


SELECT * from (
select a.inst_name,a.txn_date,
        round(avg((case when substr(a.inst_name,21,1) = 'C' then a.iv else 0 end) ),1) AS call_iv, 
        round(avg((case when substr(a.inst_name,21,1) = 'P' then a.iv else 0 end) ),1) AS put_iv 
        from weekly_option_daily_report a
        where a.txn_date >= current_date - 5
        group by a.inst_name, a.txn_date
) as option_iv
WHERE call_iv > 0 or put_iv > 0;

drop VIEW v_weekly_iv;
create VIEW v_weekly_iv as
select substr(a.inst_name,1,19) as inst_name, a.txn_date,
        round(max((case when substr(a.inst_name,21,1) = 'C' then a.iv else 0 end) ),1)::float AS call_iv, 
        round(max((case when substr(a.inst_name,21,1) = 'P' then a.iv else 0 end) ),1)::float AS put_iv 
        from weekly_option_daily_report a
        group by substr(a.inst_name,1,19) , a.txn_date
        having round(avg((case when substr(a.inst_name,21,1) = 'C' then a.iv else 0 end) ),1) >0
        or round(avg((case when substr(a.inst_name,21,1) = 'P' then a.iv else 0 end) ),1) >0
        ;


select substr(a.inst_name,1,16) as inst_name, a.txn_date,
        round(max((case when substr(a.inst_name,18,1) = 'C' then a.iv else 0 end) ),1)::float AS call_iv, 
        round(max((case when substr(a.inst_name,18,1) = 'P' then a.iv else 0 end) ),1)::float AS put_iv 
        from option_daily_report a
        where txn_date = (select max(txn_date) from option_daily_report)
		and date('01-'||substr(a.inst_name,5,6)) between current_date - 31 and current_date + 400
        group by substr(a.inst_name,1,16) , a.txn_date
        having round(avg((case when substr(a.inst_name,18,1) = 'C' then a.iv else 0 end) ),1) >0
        or round(avg((case when substr(a.inst_name,18,1) = 'P' then a.iv else 0 end) ),1) >0
        and round(avg((case when substr(a.inst_name,18,1) = 'P' then a.iv else 0 end) ),1) < 100
        ;


select inst_name,date('01-'||substr(a.inst_name,5,6)) from option_daily_report a
where substr(a.inst_name,5,6) is null;

select date('01-'||substr(a.inst_name,5,6)) , count(*)
from option_daily_report a 
where date('01-'||substr(a.inst_name,5,6)) < current_date + 300
group by date('01-'||substr(a.inst_name,5,6));

select date('01-'||substr(a.inst_name,5,6)), count(*)   from option_daily_report a
group by date('01-'||substr(a.inst_name,5,6))

select date('01-'||substr(a.inst_name,5,6)), count(*)   from option_daily_report a
where date('01-'||substr(a.inst_name,5,6)) between current_date - 31 and current_date + 400
group by date('01-'||substr(a.inst_name,5,6))
order by 1

select substr(a.inst_name,1,16) as inst_name, a.txn_date,
        round(max((case when substr(a.inst_name,18,1) = 'C' then a.iv else 0 end) ),1)::float AS call_iv, 
        round(max((case when substr(a.inst_name,18,1) = 'P' then a.iv else 0 end) ),1)::float AS put_iv 
        from option_daily_report a
        where txn_date = (select max(txn_date) from option_daily_report)
		and date('01-'||substr(a.inst_name,5,6)) between current_date - 31 and current_date + 400
        group by substr(a.inst_name,1,16) , a.txn_date
        having (round(avg((case when substr(a.inst_name,18,1) = 'C' then a.iv else 0 end) ),1) >0
        or round(avg((case when substr(a.inst_name,18,1) = 'P' then a.iv else 0 end) ),1) >0)
        and round(max((case when substr(a.inst_name,18,1) = 'P' then a.iv else 0 end) ),1)::float < 60
		and round(max((case when substr(a.inst_name,18,1) = 'C' then a.iv else 0 end) ),1)::float < 60
        ;

select * from option_daily_report a 
where length(substr(a.inst_name,5,6)) < 6

drop VIEW v_monthly_iv;
create VIEW v_monthly_iv as
select substr(a.inst_name,1,16) as inst_name, a.txn_date,
        round(max((case when substr(a.inst_name,18,1) = 'C' then a.iv else 0 end) ),1)::float AS call_iv, 
        round(max((case when substr(a.inst_name,18,1) = 'P' then a.iv else 0 end) ),1)::float AS put_iv 
        from option_daily_report a
        where txn_date = (select max(txn_date) from option_daily_report)
		and date('01-'||substr(a.inst_name,5,6)) between current_date - 31 and current_date + 400
        group by substr(a.inst_name,1,16) , a.txn_date
        having (round(avg((case when substr(a.inst_name,18,1) = 'C' then a.iv else 0 end) ),1)::float >0
        or round(avg((case when substr(a.inst_name,18,1) = 'P' then a.iv else 0 end) ),1)::float >0)
        ;

select txn_date, count(*) from weekly_option_daily_report
where txn_date='2025-05-15' group by 1;

delete from weekly_option_daily_report
where txn_date>'2024-03-23';
select txn_date, count(*) from option_daily_report
where txn_date='2025-05-15' group by 1;
delete from option_daily_report
where txn_date>='2025-05-19';
delete from weekly_option_daily_report
where txn_date>='2025-05-19' 


weekly_option_daily_report

-- Check by txn_date
SELECT txn_date, cmonth, count(*) FROM t_weekly_delta_all_strikes where txn_date >='2024-03-22' group by 1,2

/* Filter Last txn_date */
drop VIEW v_weekly_iv;
create VIEW v_weekly_iv as
select substr(a.inst_name,1,19) as inst_name, a.txn_date,
        round(max((case when substr(a.inst_name,21,1) = 'C' then a.iv else 0 end) ),1)::float AS call_iv, 
        round(max((case when substr(a.inst_name,21,1) = 'P' then a.iv else 0 end) ),1)::float AS put_iv 
        from weekly_option_daily_report a
        where txn_date = (select max(txn_date) from option_daily_report)
        group by substr(a.inst_name,1,19) , a.txn_date
        having round(avg((case when substr(a.inst_name,21,1) = 'C' then a.iv else 0 end) ),1) >0
        or round(avg((case when substr(a.inst_name,21,1) = 'P' then a.iv else 0 end) ),1) >0
        ;

delete from stock_option_report where  txn_date>='2025-03-01' ;

SELECT  substr(a.inst_name, 5,7)  as cmonth, 
left(a.inst_name, 3) as symb,
a.txn_date as txn_date, 
substr(a.inst_name,1,18) as instm,
substr(a.inst_name,20,1) as CP,
a.stock_price,
substr(a.inst_name,13,6)  as strike,  
a.oi as coi, 
round(a.delta,2) as cdelta, 
round(a.gamma,2) as cgamma,
a.close as c_close,
round(a.gamma*a.oi,2) as cnetgamma, 
round(a.delta*a.oi ,2) as cnetdelta
FROM stock_option_report a
where a. txn_date= '2025-03-21'
and left(a.inst_name, 3)='TCH';

-- 2023-03-23
drop VIEW v_stock_option_report_weekly cascade;
CREATE VIEW v_stock_option_report_weekly AS
SELECT substr(a.inst_name, 5,7)  as cmonth, 
left(a.inst_name, 3) as symb,
a.txn_date as txn_date, 
a.stock_price,
substr(a.inst_name,13,6)  as strike,  
a.oi as coi, 
round(a.delta,2) as cdelta, 
round(a.gamma,2) as cgamma,
a.close as c_close,
round(a.gamma*a.oi,2) as cnetgamma, 
round(a.delta*a.oi ,2) as cnetdelta,
-1*b.oi as poi, 
round(-1*b.delta,2) as pdelta, 
round(b.gamma,2) as pgamma,  
b.close as p_close,
round(b.gamma*b.oi,2) as pnetgamma, 
round(-1*b.delta*b.oi,2) as pnetdelta,
round((a.gamma*a.oi + b.gamma*b.oi),2) as net_gamma, 
round((a.delta*a.oi - b.delta*b.oi),2) as net_delta,
a.oi_change as coi_change,
-1*b.oi_change as poi_change
FROM stock_option_report a, stock_option_report b
where substr(a.inst_name,20,1) ='C'
and substr(b.inst_name,20,1) ='P'
and substr(a.inst_name,1,18)= substr(b.inst_name,1,18)
and a. txn_date= b.txn_date
and a.txn_date>='2025-01-01';

/* TEST*/
SELECT substr(a.inst_name, 5,7)  as cmonth, 
left(a.inst_name, 3) as symb,
a.txn_date as txn_date, 
a.stock_price,
substr(a.inst_name,13,6)  as strike,  
a.oi as coi, 
round(a.delta,2) as cdelta, 
round(a.gamma,2) as cgamma,
a.close as c_close,
round(a.gamma*a.oi,2) as cnetgamma, 
round(a.delta*a.oi ,2) as cnetdelta,
-1*b.oi as poi, 
round(-1*b.delta,2) as pdelta, 
round(b.gamma,2) as pgamma,  
b.close as p_close,
round(b.gamma*b.oi,2) as pnetgamma, 
round(-1*b.delta*b.oi,2) as pnetdelta,
round((a.gamma*a.oi + b.gamma*b.oi),2) as net_gamma, 
round((a.delta*a.oi - b.delta*b.oi),2) as net_delta,
a.oi_change as coi_change,
-1*b.oi_change as poi_change
FROM stock_option_report a, stock_option_report b
where substr(a.inst_name,20,1) ='C'
and substr(b.inst_name,20,1) ='P'
and substr(a.inst_name,1,18)= substr(b.inst_name,1,18)
and a. txn_date= b.txn_date
and a.txn_date='2025-03-21'
and left(a.inst_name, 3)='TCH';


drop view  v_stock_option_value;
CREATE VIEW v_stock_option_value AS
SELECT a.*, 
round(a.coi * b.contract_size * a.cdelta, 0) as coi_share, 
round (a.coi * b.contract_size * a.stock_price * a.cdelta, 0) as coi_delta_value, 
round(a.coi * b.contract_size * a.c_close, 0) as coi_option_value,
round(-1 * a.poi * b.contract_size * a.pdelta, 0) as poi_share , 
round (-1 * a.poi * b.contract_size * a.stock_price * a.pdelta, 0) as poi_delta_value, 
round(a.poi * b.contract_size * a.p_close, 0) as poi_option_value
FROM v_stock_option_report_weekly a, hkats_code b
where a.symb=b.hkats_code;

select * from v_stock_option_value a
where a.symb ='TCH' and  a.txn_date='2025-03-21';

select * from v_stock_option_value where symb ='MET' 
        and txn_date='2025-05-16' 
        and cmonth='23MAY25' order by strike;	

"cmonth"	"symb"	"txn_date"	"stock_price"	"strike"	"coi"	"cdelta"	"cgamma"	"c_close"	"cnetgamma"	"cnetdelta"	"poi"	"pdelta"	"pgamma"	"p_close"	"pnetgamma"	"pnetdelta"	"net_gamma"	"net_delta"	"coi_change"	"poi_change"	"coi_share"	"coi_delta_value"	"coi_option_value"	"poi_share"	"poi_delta_value"	"poi_option_value"
"23MAY25"	"MET"	"2025-05-16"	131.40	"127.50"	2	0.72	0.05	5.19	0.09	1.44	-449	-0.29	0.04	1.40	20.14	-131.40	20.23	-129.96	2	-130	720	94608	5190	-65105	-8554797	-314300
"23MAY25"	"MET"	"2025-05-16"	131.40	"130.00"	148	0.59	0.05	3.66	7.90	86.93	-524	-0.42	0.05	2.34	26.66	-217.85	34.56	-130.92	148	-177	43660	5736924	270840	-110040	-14459256	-613080
"23MAY25"	"MET"	"2025-05-16"	131.40	"132.50"	107	0.45	0.05	2.48	5.67	48.47	-263	-0.55	0.05	3.56	13.60	-143.41	19.27	-94.94	106	1	24075	3163455	132680	-72325	-9503505	-468140
"23MAY25"	"MET"	"2025-05-16"	131.40	"135.00"	728	0.33	0.05	1.61	34.48	241.75	-262	-0.66	0.05	5.20	12.18	-173.88	46.66	67.88	639	-24	120120	15783768	586040	-86460	-11360844	-681200
"23MAY25"	"MET"	"2025-05-16"	131.40	"137.50"	278	0.23	0.04	0.97	10.93	63.11	-179	-0.76	0.04	7.16	6.83	-135.33	17.76	-72.22	126	-24	31970	4200858	134830	-68020	-8937828	-640820
"23MAY25"	"MET"	"2025-05-16"	131.40	"140.00"	222	0.15	0.03	0.54	6.60	32.22	-64	-0.83	0.03	9.30	1.92	-52.82	8.52	-20.61	100	-3	16650	2187810	59940	-26560	-3489984	-297600
"23MAY25"	"MET"	"2025-05-16"	131.40	"142.50"	74	0.09	0.02	0.28	1.53	6.42	-42	-0.88	0.02	11.55	0.96	-36.78	2.49	-30.36	27	-29	3330	437562	10360	-18480	-2428272	-242550
"23MAY25"	"MET"	"2025-05-16"	131.40	"145.00"	318	0.05	0.01	0.14	4.18	15.44	-11	-0.89	0.02	13.99	0.20	-9.82	4.38	5.61	1	0	7950	1044630	22260	-4895	-643203	-76945
"23MAY25"	"MET"	"2025-05-16"	131.40	"150.00"	157	0.01	0.00	0.03	0.66	1.97	-2	-1.00	0.00	18.60	0.00	-2.00	0.66	-0.03	3	0	785	103149	2355	-1000	-131400	-18600        

-- SELECT  substr(a.inst_name, 5,7) as cmonth, e.g.'23MAY25'
drop VIEW v_stock_option_report_v2 cascade;
CREATE VIEW v_stock_option_report_v2 AS
SELECT   substr(a.inst_name, 5,7) as cmonth, 
left(a.inst_name, 3) as symb,
a.txn_date as txn_date, 
a.stock_price,
substr(a.inst_name,13,6)  as strike,  
a.oi as coi, 
round(a.delta,2) as cdelta, 
round(a.gamma,2) as cgamma,
a.close as c_close,
round(a.gamma*a.oi,2) as cnetgamma, 
round(a.delta*a.oi ,2) as cnetdelta,
-1*b.oi as poi, 
round(-1*b.delta,2) as pdelta, 
round(b.gamma,2) as pgamma,  
b.close as p_close,
round(b.gamma*b.oi,2) as pnetgamma, 
round(-1*b.delta*b.oi,2) as pnetdelta,
round((a.gamma*a.oi + b.gamma*b.oi),2) as net_gamma, 
round((a.delta*a.oi - b.delta*b.oi),2) as net_delta,
a.oi_change as coi_change,
-1*b.oi_change as poi_change
FROM stock_option_report a, stock_option_report b
where substr(a.inst_name,20,1) ='C'
and substr(b.inst_name,20,1) ='P'
and substr(a.inst_name,1,18)= substr(b.inst_name,1,18)
and a. txn_date= b.txn_date;

SELECT   substr(a.inst_name, 5,7) as cmonth, 
left(a.inst_name, 3) as symb,
a.txn_date as txn_date, 
a.stock_price,
substr(a.inst_name,13,6)  as strike,  
a.oi as coi, 
round(a.delta,2) as cdelta, 
round(a.gamma,2) as cgamma,
a.close as c_close,
round(a.gamma*a.oi,2) as cnetgamma, 
round(a.delta*a.oi ,2) as cnetdelta,
-1*b.oi as poi, 
round(-1*b.delta,2) as pdelta, 
round(b.gamma,2) as pgamma,  
b.close as p_close,
round(b.gamma*b.oi,2) as pnetgamma, 
round(-1*b.delta*b.oi,2) as pnetdelta,
round((a.gamma*a.oi + b.gamma*b.oi),2) as net_gamma, 
round((a.delta*a.oi - b.delta*b.oi),2) as net_delta,
a.oi_change as coi_change,
-1*b.oi_change as poi_change
FROM stock_option_report a, stock_option_report b
where substr(a.inst_name,20,1) ='C'
and substr(b.inst_name,20,1) ='P'
and substr(a.inst_name,1,18)= substr(b.inst_name,1,18)
and a. txn_date= b.txn_date
and a.txn_date='2025-05-16';


SELECT   substr(a.inst_name, 5,7) as cmonth, 
left(a.inst_name, 3) as symb,
a.txn_date as txn_date, 
a.stock_price,
substr(a.inst_name,13,6)  as strike,  
a.oi as coi, 
round(a.delta,2) as cdelta, 
round(a.gamma,2) as cgamma,
a.close as c_close,
round(a.gamma*a.oi,2) as cnetgamma, 
round(a.delta*a.oi ,2) as cnetdelta,
a.oi_change as coi_change
FROM stock_option_report a
where substr(a.inst_name,20,1) ='C'
and a.txn_date='2025-05-16'

"cmonth"	"symb"	"txn_date"	"stock_price"	"strike"	"coi"	"cdelta"	"cgamma"	"c_close"	"cnetgamma"	"cnetdelta"	"coi_change"
"27JUN25"	"A50"	"2025-05-16"	14.21	"009.00"	1	1.00	0.00	5.25	0.00	1.00	0
"27JUN25"	"A50"	"2025-05-16"	14.21	"011.00"	50	0.94	0.05	3.32	2.40	47.01	0
"27JUN25"	"A50"	"2025-05-16"	14.21	"011.50"	50	0.94	0.06	2.81	2.94	46.99	0

select txn_date, count(*) from weekly_option_daily_report
where txn_date > '2025-05-22'
group by 1;

delete from weekly_option_daily_report
where txn_date >= '2025-06-17'

create view v_option_screener as
(select iv.hkats_code, iv.txn_date, 
top.daily_volume,
round( (option_iv),2) AS option_iv, 
round( (hv_10d),2) as hv_10d, 
round((hv_20d),2) as hv_20d, 
round((hv_30d),2) as hv_30d,
round((option_iv - hv_10d),2) as iv_hv_10d, 
round((option_iv - hv_20d),2) as iv_hv_20d, 
round((option_iv - hv_30d),2) as iv_hv_30d,
s.p_beat_index_f,s.p_beat_index_s, s.avg_r_roc_f, s.avg_r_roc_s
from option_daily_iv as iv, option_daily_hv as hv,
(select hkats_code, txn_date, round((option_volume_amt)) as daily_volume
from option_daily_volume
WHERE txn_date = (select max(txn_date) from option_daily_volume)
) as top,
(select * 
from stock_sroc_fs s, hkats_code a
WHERE a.ticker=s.ticker)
as s
where iv.hkats_code = top.hkats_code
and iv.hkats_code = hv.hkats_code
and iv.hkats_code = s.hkats_code
and iv.txn_date = hv.txn_date
and iv.txn_date = top.txn_date
ORDER BY 1,2 DESC)