<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Favicon Test - HKEX Dashboard</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .favicon-preview {
            display: inline-block;
            width: 32px;
            height: 32px;
            margin-right: 10px;
            vertical-align: middle;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; }
        .warning { background-color: #fff3cd; color: #856404; }
        .error { background-color: #f8d7da; color: #721c24; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .code {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 HKEX Dashboard - Favicon Implementation Test</h1>
        
        <div class="test-section">
            <h2>📊 Current Environment</h2>
            <p><strong>NODE_ENV:</strong> <span id="environment">Detecting...</span></p>
            <p><strong>Expected Favicon:</strong> <span id="expected-favicon">Loading...</span></p>
            <p><strong>Current Favicon:</strong> <img id="current-favicon" class="favicon-preview" /> <span id="favicon-path">Loading...</span></p>
        </div>

        <div class="test-section">
            <h2>🔄 Favicon Tests</h2>
            <button onclick="testDevelopmentFavicon()">Test Development Favicon</button>
            <button onclick="testProductionFavicon()">Test Production Favicon</button>
            <button onclick="resetFavicon()">Reset to Environment Default</button>
        </div>

        <div class="test-section">
            <h2>🖼️ Favicon Previews</h2>
            <div>
                <h3>Development Favicon (Blue):</h3>
                <img src="favicon-dev.svg" class="favicon-preview" onerror="this.style.display='none'" />
                <img src="favicon-dev.ico" class="favicon-preview" onerror="this.style.display='none'" />
                <span>Blue background with "D" indicator</span>
            </div>
            <div style="margin-top: 10px;">
                <h3>Production Favicon (Red):</h3>
                <img src="favicon-prod.svg" class="favicon-preview" onerror="this.style.display='none'" />
                <img src="favicon-prod.ico" class="favicon-preview" onerror="this.style.display='none'" />
                <span>Red background with "P" indicator</span>
            </div>
        </div>

        <div class="test-section">
            <h2>🧪 Test Results</h2>
            <div id="test-results">
                <p class="status warning">Ready to test...</p>
            </div>
        </div>

        <div class="test-section">
            <h2>🔧 Implementation Details</h2>
            <div class="code" id="implementation-details">
Environment Detection: process.env.NODE_ENV
Development: /favicon-dev.ico (Blue with "D")
Production: /favicon-prod.ico (Red with "P")
Dynamic Loading: updateFavicon() function
Browser Support: SVG and ICO formats
            </div>
        </div>
    </div>

    <script>
        // Favicon testing functions
        function updateFaviconDisplay() {
            const favicon = document.querySelector('link[rel="icon"]');
            const faviconPath = favicon ? favicon.href : 'Not found';
            const faviconImg = document.getElementById('current-favicon');
            
            document.getElementById('favicon-path').textContent = faviconPath;
            if (favicon) {
                faviconImg.src = favicon.href;
                faviconImg.style.display = 'inline-block';
            } else {
                faviconImg.style.display = 'none';
            }
        }

        function setFavicon(path, description) {
            let favicon = document.querySelector('link[rel="icon"]');
            if (!favicon) {
                favicon = document.createElement('link');
                favicon.rel = 'icon';
                favicon.type = 'image/x-icon';
                document.head.appendChild(favicon);
            }
            
            favicon.href = path;
            updateFaviconDisplay();
            
            const results = document.getElementById('test-results');
            results.innerHTML = `<p class="status success">✅ Favicon updated to: ${description} (${path})</p>`;
        }

        function testDevelopmentFavicon() {
            setFavicon('favicon-dev.ico', 'Development (Blue)');
        }

        function testProductionFavicon() {
            setFavicon('favicon-prod.ico', 'Production (Red)');
        }

        function resetFavicon() {
            // Simulate environment detection
            const isDev = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
            const path = isDev ? 'favicon-dev.ico' : 'favicon-prod.ico';
            const env = isDev ? 'Development' : 'Production';
            setFavicon(path, `${env} (Environment Default)`);
        }

        // Initialize display
        window.addEventListener('DOMContentLoaded', function() {
            // Detect environment
            const isDev = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
            const environment = isDev ? 'development' : 'production';
            const expectedFavicon = isDev ? '/favicon-dev.ico' : '/favicon-prod.ico';
            
            document.getElementById('environment').textContent = environment;
            document.getElementById('expected-favicon').textContent = expectedFavicon;
            
            updateFaviconDisplay();
            
            // Auto-test if favicon is working
            setTimeout(() => {
                const favicon = document.querySelector('link[rel="icon"]');
                if (favicon && (favicon.href.includes('favicon-dev') || favicon.href.includes('favicon-prod'))) {
                    const results = document.getElementById('test-results');
                    results.innerHTML = `<p class="status success">✅ Environment-specific favicon is working correctly!</p>`;
                } else {
                    const results = document.getElementById('test-results');
                    results.innerHTML = `<p class="status warning">⚠️ Default favicon detected. Environment-specific favicon may not be loaded.</p>`;
                }
            }, 1000);
        });
    </script>
</body>
</html>
