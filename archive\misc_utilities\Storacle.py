# %%
from lxml import html, etree
import requests as rq
from datetime import datetime
import pandas as pd
import pymysql
from sqlalchemy import create_engine
import calendar
import os
import pandas as pd
from pandas.tseries.offsets import MonthEnd, BMonthEnd
import numpy as np
import datetime as dt
from scipy.stats import norm
from scipy.optimize import bisect
# import matplotlib.dates as mpl_dates
# import matplotlib.pyplot as plt
from scipy.stats import norm
import math

def get_exchange_holidays():
    exchange_holidays = []
    # URL of the page containing the trading calendar
    url = "https://www.hkex.com.hk/Services/Trading/Derivatives/Overview/Trading-Calendar-and-Holiday-Schedule?sc_lang=en"
    
    # Send a GET request to the URL
    response = rq.get(url)
    
    # Parse the HTML content
    tree = html.fromstring(response.content)
    
    # Define the XPath to select the specific table rows
    xpath = '//*[@id="pagecontent_0_maincontent_0_common_content_section"]/div[2]/div[1]/table[26]/tbody/tr'
    
    # Select the table rows using the XPath
    rows = tree.xpath(xpath)
    # Convert the rows to XML content, removing <br> tags
    rows_xml = [etree.tostring(row).decode().replace('<br>', '') for row in rows]  
    # Concatenate rows into XML content
    xml_content = '<root>{}</root>'.format(''.join(rows_xml))
        # Save the XML content to a file
    save_xml_to_file(xml_content, 'hkex_holidays.xml')    
    # Extract and print the content of each row
    for row in rows:
        # Extract text from each cell in the row
        cells = row.xpath('.//td')
        row_data = [cell.text_content().strip() for cell in cells]
        print(row_data)
        exchange_holidays.append(datetime.strptime(row_data[0].split(' ')[0],'%d/%m/%Y'))
    return exchange_holidays

def save_xml_to_file(xml_content, file_path):
    file = open(file_path, 'w', encoding='utf-8')
    file.write(xml_content)
    print("XML content saved to:", file_path)

def get_exchange_holidays_from_file(file_path):
    file=  open(file_path, 'r', encoding='utf-8')
    xml_content = file.read()
    # remove all <br> tags
    xml_content = xml_content.replace('<br>', '')
    xml_content = xml_content.replace('</span>', '')
    xml_content = xml_content.replace('<span>', '')
    # Parse the XML content
    root = etree.fromstring(xml_content) 
    # Iterate through each row
    for row in root.xpath('.//tr'):
        # Extract text from each cell in the row
        cells = row.xpath('.//td')
        for cell in cells:
            print(cell.text.split(' ')[0])
            exchange_holidays.append(datetime.strptime(cell.text.split(' ')[0],'%d/%m/%Y'))
            break
    return exchange_holidays


def d1(inst_strike, stock_price, iv, tx):
    iv=max(iv, 0.0001)
    tx=max(tx, 0.0001)
    return (math.log(stock_price / inst_strike) + (iv ** 2 / 2) * tx) / (iv * math.sqrt(tx))

def d2(d_1, iv, tx):
    iv=max(iv, 0.0001)
    tx=max(tx, 0.0001)    
    return d_1 - iv * math.sqrt(tx)

def call_price(inst_strike, stock_price, iv, tx, risk_free_rate=0):
    d_1 = d1(inst_strike, stock_price, iv, tx) 
    d_2 = d2(d_1, iv, tx)
    call_delta = norm.cdf(d_1)
    discounted_strike = inst_strike * math.exp(-risk_free_rate * tx)
    return stock_price * call_delta - discounted_strike * norm.cdf(d_2)

def put_price(inst_strike, stock_price, iv, tx, risk_free_rate=0):
    d_1 = d1(inst_strike, stock_price, iv, tx) 
    d_2 = d2(d_1, iv, tx)
    put_delta = norm.cdf(-d_2)
    discounted_stock = stock_price * math.exp(-risk_free_rate * tx)
    return inst_strike * put_delta - discounted_stock * norm.cdf(-d_1)



def create_connection():
    host = os.environ.get('dbhost')
    user = os.environ.get('user')
    password = os.environ.get('password')
    return pymysql.connect(  
        host = host,
        user = user,
        password = password,
        db = 'storacle',
        charset = 'utf8mb4',
        cursorclass = pymysql.cursors.DictCursor
        )

def create_cnx():
    host = os.environ.get('dbhost')
    user = os.environ.get('user')
    password = os.environ.get('password')
    cnx = create_engine(f'mysql+pymysql://{user}:{password}@{host}:3306/storacle', echo=False)
    return cnx

# # MONTH OPTIONS
# def getDay2Expiry( cmonth, d):
#     from pandas.tseries.offsets import BMonthEnd
#     #  1st day of contract month
#     if len(cmonth)== 5:
#         d0 = dt.datetime.strptime(cmonth, '%b%y')    
#     elif  len(cmonth)==6:
#         d0 = dt.datetime.strptime(cmonth, '%b-%y')
#     else:
#         d0 = None
#         print( 'Invalid getDay2Expiry parameter ', cmonth)
#     #print(d0)
#     #Last day of current month
#     d_end = BMonthEnd().rollforward(d0)
#     #print(d_end)
#     # Count days until Contract Expiry Date
#     dx = len( pd.bdate_range(d, d_end)) -2
#     return dx

# # Weekly Options
# def getWODay2Expiry( xdate, d):
#     from pandas.tseries.offsets import BMonthEnd
#     #  1st day of contract month
#     d_end = dt.datetime.strptime(xdate, '%d-%b-%y')
#     # Count days until Contract Expiry Date
#     dx = len( pd.bdate_range(d, d_end)) -1
#     #print(d, d_end, dx)
#     return dx


def getDay2Expiry( cmonth, d):
    if len(cmonth)== 5:
        d0 = dt.datetime.strptime(cmonth, '%b%y')    
    elif  len(cmonth)==6:
        d0 = dt.datetime.strptime(cmonth, '%b-%y')
    else:
        d0 = None
        print( 'Invalid getDay2Expiry parameter ', cmonth)
    # Determine Expiry Date
    xdate=BMonthEnd().rollforward(d0)
    #print(d0)
    #  Start couting from next day
    next_d= d + dt.timedelta(days=1)
    #All remaining trading days    
    trading_days = pd.bdate_range( next_d, xdate, 
                freq='C',
                weekmask = weekmask,
                holidays=exchange_holidays)   
    # print(trading_days) 
    # Monthly Contract Last trading date = 2nd last trading day
    # Count days until Contract Expiry Date. d not counted as assume closed price
    # dx = len( pd.bdate_range(d, d_end)) -2
    if len(trading_days) < 2:
        dx=0
        # print( f'Last Trading Day PASSED, dx days to expiry from {d} = {dx}')         
    else:
        dx= len(trading_days) -1
        # print( f'Last Trading Day = {trading_days[-2]}, dx days to expiry from {d} = {dx}') 
    return dx

def getWODay2Expiry( xdate, d):
    #  Start couting from next day
    # next_d= dt.datetime.strptime(d, '%Y-%m-%d')+ dt.timedelta(days=1)
    next_d= d + dt.timedelta(days=1)
    d_end = dt.datetime.strptime(xdate, '%d-%b-%y')
    #All remaining trading days
    trading_days = pd.bdate_range( next_d, d_end, 
                freq='C',
                weekmask = weekmask,
                holidays=exchange_holidays)    
    # print(trading_days)
    # Monthly Contract Last trading date = 2nd last trading day
    # Count days until Contract Expiry Date. d not counted as assume dayend analysis
    # dx = len( pd.bdate_range(d, d_end)) -2
    dx= len(trading_days)
    # print( f'Last Trading Day = {xdate}, dx days to expiry from {d} = {dx}') 
    
    # Count days until Contract Expiry Date
    # dx = len( pd.bdate_range(d, d_end)) -1
    #print(d, d_end, dx)
    return dx


def gamma(id1, ip, iiv, itx):
    p = max( ip, 0.001)
    iv = min(max( iiv, 0.001), 20)
    tx = max( itx, 0.001)
    if p==0.001 or iv==0.001 or tx==0.001 or iv==20:
        # print( f'GAMMA Override parameters: {id1=}, {ip=}, {iiv=}, {itx=}')
        return 0
    # if id1==0 or ip==0 or iiv==0 or itx==0:
    #     return 0
    # elif iiv >20:
    #     print( 'gamma: IV Must be between 0 to 20', str(iiv))
    #     return None
    else:
        # Gaussian at d1 = N'(d1)=> (np.exp(-1*np.power(id1,2)/2)/np.sqrt(2*np.pi))
        # Time-Normalized Vol = v*sqrt(T) => iiv*np.sqrt(itx)
        # Round gamma to 8 decimal places
        return round((np.exp(-1*np.power(id1,2)/2)/np.sqrt(2*np.pi))/(p*iv*np.sqrt(tx)), 8)

def charm(id1, iiv, itx):
    iv = min(max( iiv, 0.001), 20)
    tx = max( itx, 0.001)
    # if iv==0.001 or tx==0.001 or iv==20:
    #     print( f'CHARM Override parameters: {id1=}, {iiv=}, {itx=}')
    NormV = iv*np.sqrt(tx)
    d2= id1 - NormV
    # Gaussian at d1 = N'(d1)=> (np.exp(-1*np.power(id1,2)/2)/np.sqrt(2*np.pi))
    GNd1 = (np.exp(-1*np.power(id1,2)/2)/np.sqrt(2*np.pi))
    ocharm = GNd1*d2/(2*tx)
    return round(ocharm,8)
    #print(f'{NormV=}\n {d2=}\n {GNd1=}\n {ocharm=}')
    # if id1==0 or iiv==0 or itx==0:
    #     return 0
    # elif iiv >20:
    #     print( 'charm: IV Must be between 0 to 20', str(iiv))
    #     return None
    # else:

def insert_inst(inst_name):
    connection = create_connection()
    with connection.cursor() as cursor:
            sql = "INSERT IGNORE INTO instruments(inst_name) VALUES (%s);"
            vals = (inst_name)
            try:
                cursor.execute(sql, vals)
                #print(inst_name + ' Inserted')
            except Exception as e:
                pass
                #print(inst_name + str(e), flush = True)
            # finally:
            #     connection.close()
    return


def listMonths():
    # Complie Month list
    months=[]
    for month_idx in range(1, 13):
        #print (calendar.month_name[month_idx])
        m= calendar.month_abbr[month_idx].upper()
        months.append(m)
    return months
'''
def d1(istrike, ip, iiv, itx):
    return ( (np.log(ip/istrike)) + (itx*(0+(np.square(iiv)/2)))) / ( iiv*np.sqrt(itx) ) 
def gamma(id1, ip, iiv, itx):
    return (np.exp(-1*np.power(id1,2)/2)/np.sqrt(2*np.pi))/(ip*iiv*np.sqrt(itx))
'''

# Calcule the Implied Volatility of an option from Option price, DTE, Strike and Stock Price
def CalcIV( option_price, stock_price, strike, tx, call_put):
    # Define the function to calculate the option price given the implied volatility
    def option_price_func(iv):
        if call_put[0].upper()=='C':
            if option_price==0:
                return 0
            elif call_price(strike, stock_price, 0, tx) - option_price > 0:
                return 0
            else:
                return call_price(strike, stock_price, iv, tx) - option_price
        elif call_put[0].upper()=='P':
            if option_price==0:
                return 0
            elif put_price(strike, stock_price, 0, tx) - option_price > 0:
                return 0
            else:
                return put_price(strike, stock_price, iv, tx) - option_price
        else:
            print( 'Invalid Call/Put parameter:', call_put)
            return 0

    # Use a numerical method (such as the bisection method) to find the implied volatility
    iv = bisect(option_price_func, 0, 20)
    return iv

# %%
weekmask = 'Mon Tue Wed Thu Fri'
# exchange_holidays = [dt.date(2024, 4, 4), dt.date(2024, 4, 1)]
# Call the function to fetch and print the trading calendar
exchange_holidays = []
try:
    exchange_holidays = get_exchange_holidays()
except Exception as e:
    print(str(e))
    exchange_holidays= get_exchange_holidays_from_file( 'hkex_holidays.xml')

if __name__ == '__main__':    
    print(f"{gamma=}: {gamma(-0.10684, 28800, 0.3, 0.030137)}")
    print(f"{charm=}: {charm(-0.10684, 0.3, 0.030137)}")
    print(f"{d1=}: {d1(21600, 21400, 0.001, 0.0809)}")
    print(f"{d2=}: {d2(d1(22600, 24000, 0.2, 0.0122), 0.2, 0.0122)}")
    print(f"{call_price=}: {call_price(23600, 24000, 0.2, 0.0122)}")
    print(f"{put_price=}: {put_price(23600, 24000, 0.2, 0.0122)}")
    print(f"{CalcIV=}: {CalcIV( 67.67715670939651, 24000, 23600, 0.0122, 'put')}")
    print(f"{CalcIV=}: {CalcIV( 467.6771567093965, 24000, 23600, 0.0122, 'call')}")
    print(f"{CalcIV=}: {CalcIV( 0, 24000, 23600, 0,'call')}")
    print(f"{d1=}: {d1(23600, 24000, 0, 0)}")
    print(f"{d2=}: {d2(0.771868, 0, 0)}")
    print(f"{call_price=}: {call_price(24000, 23600, 0.2, 0.0000001, 0)}")
    print(f"{put_price=}: {put_price(24000, 23600, 0.2, 0.0000001, 0)}")
    print(f"{CalcIV=}: {CalcIV(1304, 16723.92, 13100, 0.02024, 'c')}")
    # 2.7097314418496503
    # 467.6771567093965
    # 67.67715670939651


# %%
#updatePrice('HSI')



# def d1(istrike, ip, iiv, itx):
#     strike = max(istrike, 0.001)
#     p = max( ip, 0.001)
#     iv = min(max( iiv, 0.001), 20)
#     tx = max( itx, 0.001)
#     # if strike==0.001 or p==0.001 or iv==0.001 or tx==0.001 or iv==20:
#     #     print( f'd1 Override parameters:  {istrike=}, {ip=}, {iiv=}, {itx=}')
#     return ( (np.log(p/strike)) + (tx*(0+(np.square(iv)/2)))) / ( iv*np.sqrt(tx) )     
# #    if istrike==0 or ip==0 or iiv==0 or itx==0:
# #        return 0
# #    elif iiv >20:
# #        print( 'd1: IV Must be between 0 to 20 ', str(iiv))
# #        return None
# #    else:
# #        return ( (np.log(ip/istrike)) + (itx*(0+(np.square(iiv)/2)))) / ( iiv*np.sqrt(itx) ) 
# def d2(id1, iiv, itx):
#     iv = min(max( iiv, 0.001), 20)
#     tx = max( itx, 0.001)
#     # if iv==0.001 or tx==0.001 or iv==20:
#     #     print( f'd2 Override parameters:  {id1=}, {iiv=}, {itx=}')
#     return ( id1 - iv*np.sqrt(tx) ) 

# def call_price(inst_strike, stock_price, iv, tx):
#     d_1= d1( inst_strike, stock_price, iv, tx) 
#     call_delta = norm.cdf(d_1)
#     d_2= d2( d_1, iv, tx)
#     return call_delta*stock_price - norm.cdf(d_2)*inst_strike

# def put_price(inst_strike, stock_price, iv, tx):
#     return call_price(inst_strike, stock_price, iv, tx) + inst_strike - stock_price

# %%

# %%

# %%
