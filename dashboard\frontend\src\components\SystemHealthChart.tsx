import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, Typography, Box } from '@mui/material';
import {
  Line<PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { SystemHealth } from '../types';

interface SystemHealthChartProps {
  data: SystemHealth[];
  height?: number;
}

const SystemHealthChart: React.FC<SystemHealthChartProps> = ({ 
  data, 
  height = 300 
}) => {  // Format data for recharts
  const chartData = data.map(item => ({
    time: item.timestamp ? new Date(item.timestamp).toLocaleTimeString() : new Date().toLocaleTimeString(),
    cpu: item.cpu_usage || 0,
    memory: item.memory_usage || 0,
    timestamp: item.timestamp
  }));

  return (
    <Card>
      <CardHeader 
        title="System Health Metrics"
        subheader="Real-time CPU and Memory Usage"
      />
      <CardContent>
        <ResponsiveContainer width="100%" height={height}>
          <LineChart data={chartData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              dataKey="time" 
              tick={{ fontSize: 12 }}
            />
            <YAxis 
              domain={[0, 100]}
              tick={{ fontSize: 12 }}
              label={{ value: 'Usage (%)', angle: -90, position: 'insideLeft' }}
            />
            <Tooltip 
              formatter={(value: number, name: string) => [
                `${value.toFixed(1)}%`, 
                name === 'cpu' ? 'CPU Usage' : 'Memory Usage'
              ]}
              labelFormatter={(label) => `Time: ${label}`}
            />
            <Legend />
            <Line
              type="monotone"
              dataKey="cpu"
              stroke="#1976d2"
              strokeWidth={2}
              dot={{ r: 3 }}
              name="CPU Usage"
            />
            <Line
              type="monotone"
              dataKey="memory"
              stroke="#ed6c02"
              strokeWidth={2}
              dot={{ r: 3 }}
              name="Memory Usage"
            />
          </LineChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};

export default SystemHealthChart;
