import requests
import json

def test_api_fix():
    """Test that the API now accepts requests without txn_date parameter"""
    print("Testing API fix...")
    
    url = "http://localhost:8001/api/v1/processes/start"
    headers = {"Content-Type": "application/json"}
    
    # Test 1: Empty parameters (should work now)
    data1 = {
        "process": "update_index_options",
        "parameters": {}
    }
    
    print(f"Test 1: Making request with empty parameters")
    try:
        response = requests.post(url, json=data1, headers=headers)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("✅ SUCCESS: API now accepts empty parameters!")
        else:
            print(f"❌ FAILED: Still getting {response.status_code} error")
    except Exception as e:
        print(f"❌ ERROR: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # Test 2: With txn_date parameter (should still work)
    data2 = {
        "process": "update_index_options", 
        "parameters": {
            "txn_date": "2025-05-27"
        }
    }
    
    print(f"Test 2: Making request with txn_date parameter")
    try:
        response = requests.post(url, json=data2, headers=headers)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("✅ SUCCESS: API still works with txn_date parameter!")
        else:
            print(f"❌ FAILED: Getting {response.status_code} error with txn_date")
    except Exception as e:
        print(f"❌ ERROR: {e}")

def test_process_types():
    """Test the process types endpoint to see the updated configuration"""
    print("Testing process types endpoint...")
    
    url = "http://localhost:8001/api/v1/processes/types"
    
    try:
        response = requests.get(url)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            update_index = data.get('update_index_options')
            if update_index:
                print(f"update_index_options configuration:")
                print(f"  Required params: {update_index.get('requires_params', [])}")
                print(f"  Optional params: {update_index.get('optional_params', [])}")
                
                if 'txn_date' not in update_index.get('requires_params', []):
                    print("✅ SUCCESS: txn_date is no longer required!")
                else:
                    print("❌ FAILED: txn_date is still required")
                    
                if 'txn_date' in update_index.get('optional_params', []):
                    print("✅ SUCCESS: txn_date is now optional!")
                else:
                    print("❌ FAILED: txn_date is not in optional params")
            else:
                print("❌ ERROR: update_index_options not found in response")
        else:
            print(f"❌ FAILED: Got {response.status_code} status")
    except Exception as e:
        print(f"❌ ERROR: {e}")

if __name__ == "__main__":
    print("HKEX Dashboard - API Fix Verification")
    print("="*60)
    
    test_process_types()
    print("\n" + "="*60 + "\n")
    test_api_fix()
