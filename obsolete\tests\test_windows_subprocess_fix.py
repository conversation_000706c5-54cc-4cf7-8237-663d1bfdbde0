#!/usr/bin/env python3
"""
Test script to verify the Windows subprocess fix works correctly.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the app directory to the path
app_dir = Path(__file__).parent / "app"
sys.path.insert(0, str(app_dir))

from services.simple_orchestrator import ProcessOrchestratorService

async def test_windows_subprocess():
    """Test that subprocess creation works on Windows."""
    print("Testing Windows subprocess fix...")
    print(f"Platform: {sys.platform}")
    
    # Check current event loop
    try:
        loop = asyncio.get_running_loop()
        print(f"Current event loop: {type(loop)}")
        if hasattr(loop, '_selector'):
            print(f"Event loop selector: {type(loop._selector)}")
    except Exception as e:
        print(f"Could not get event loop info: {e}")
    
    # Create orchestrator instance
    orchestrator = ProcessOrchestratorService()
    print("Created ProcessOrchestratorService instance")
    
    # Test a simple subprocess creation
    try:
        print("Testing basic subprocess creation...")
        process = await asyncio.create_subprocess_exec(
            'python', '--version',
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        stdout, stderr = await process.communicate()
        print(f"Subprocess test successful!")
        print(f"Python version: {stdout.decode().strip()}")
        print(f"Return code: {process.returncode}")
        
        if process.returncode == 0:
            print("✅ Windows subprocess fix is working correctly!")
            return True
        else:
            print("❌ Subprocess returned non-zero exit code")
            return False
            
    except Exception as e:
        print(f"❌ Subprocess test failed: {e}")
        print(f"Error type: {type(e)}")
        return False

def main():
    """Main test function."""
    print("=" * 60)
    print("Windows Subprocess Fix Verification Test")
    print("=" * 60)
    
    # Run the async test
    try:
        result = asyncio.run(test_windows_subprocess())
        if result:
            print("\n🎉 All tests passed! Windows subprocess fix is working.")
        else:
            print("\n❌ Tests failed. Check the errors above.")
    except Exception as e:
        print(f"\n❌ Test execution failed: {e}")
        print(f"Error type: {type(e)}")

if __name__ == "__main__":
    main()
