#%%
import pandas as pd
import os
import psycopg2
from dotenv import load_dotenv
import pymysql
from sqlalchemy import create_engine
load_dotenv()

local_db = create_engine(os.environ.get('WILL9700_DB'), isolation_level="AUTOCOMMIT",)
db = os.environ.get('REMOTE_DATABASE')
print(f"{db=}")
#db ="postgresql://vdpbbxpmswyyfo:<EMAIL>:5432/d27tclro6qeosh"
remote_db = create_engine(db)

db2 = os.environ.get('AIVEN_DATABASE_URL')
print(f"{db2=}")
#db="postgresql://avnadmin:<EMAIL>:16762/defaultdb?sslmode=require"
remote_db2 = create_engine(db2)

#q= f"select * from public.test;"
#q=f"select txn_date from option_daily_report where txn_id = (select max(txn_id) from option_daily_report);"
#txn_date= pd.read_sql(q, local_db)
#%%
# Check Last Txn Date
# q_date=f"select max(txn_date) from v_index_option_value;"
# txn_date= pd.read_sql(q_date, local_db).iloc[0][0]

#%%
# Check Last Txn Date for option_daily_report
# avoid lookup view due to performance
q_date=f"select max(txn_date) from option_daily_report;"
start = pd.Timestamp.now()
txn_date= pd.read_sql(q_date, local_db).iloc[0][0]
end = pd.Timestamp.now()
print(q_date)
print(f"Time to select last txn date: {end - start}")    
print(f'option_daily_report => Last Txn Date= {txn_date}')

option_value_tables =['v_index_option_value','v_weekly_option_value','v_stock_option_value' , 'v_weekly_iv','v_monthly_iv'  ]
# option_value_tables =['v_index_option_value','v_weekly_option_value']
#option_value_tables =['v_stock_option_value' ]
for t in option_value_tables:
    # Read from Local
    # Keep Last day's Txn Only due to Heroku Free Plan Limitation
    q=f"select * from {t} where txn_date='{txn_date}';"
    print(q)
    start = pd.Timestamp.now()
    df= pd.read_sql(q, local_db)
    end = pd.Timestamp.now()
    print(f"Rows = {len(df)} Selected {t} from Local db: {local_db} ")
    print(f"Time to read {t} from Local db: {end - start}")

    # Truncate Remote1 
    try:
        start = pd.Timestamp.now()
        remote_db.execute(f'TRUNCATE TABLE {t};')
        end = pd.Timestamp.now()
        print(f"Time to Truncate {t} from Remote db: {end - start}")
    except Exception as e:
        print(f"Error Truncate table {t} =>" + str(e), flush = True)

    # Save to Remote 1
    try:
        print(f"Save {t} to Remote DB1: {remote_db}")
        start = pd.Timestamp.now()
        df.to_sql(name= t, con=remote_db, if_exists = 'append', index=False)    
        end = pd.Timestamp.now()
        print(f"Time to Save {t} to Remote db: {end - start}")
        # Check Results
        q_insert=f"select txn_date, count(*) from {t}  where txn_date>='{txn_date}' group by txn_date;"
        start = pd.Timestamp.now()
        check_insert = pd.read_sql(q_insert, remote_db)
        end = pd.Timestamp.now()
        print(q_insert)
        print(check_insert)
        print(f"Time to Check {t} from Remote db1: {end - start}")
    except Exception as e:
        print(f"Error Save to Remote 1 {t} =>" + str(e), flush = True)

    # Repeat for db2
    # Truncate Remote3 
    try:
        start = pd.Timestamp.now()
        remote_db2.execute(f'TRUNCATE TABLE {t};')
        end = pd.Timestamp.now()
        print(f"Time to Truncate {t} from Remote db2: {end - start}")
    except Exception as e:
        print(f"Error Truncate table {t} =>" + str(e), flush = True)

    # Save to Remote 1
    try:
        print(f"Save {t} to Remote DB2: {remote_db2}")
        start = pd.Timestamp.now()
        df.to_sql(name= t, con=remote_db2, if_exists = 'append', index=False)    
        end = pd.Timestamp.now()
        print(f"Time to Save {t} to Remote db: {end - start}")
        # Check Results
        q_insert=f"select txn_date, count(*) from {t}  where txn_date>='{txn_date}' group by txn_date;"
        start = pd.Timestamp.now()
        check_insert = pd.read_sql(q_insert, remote_db2)
        end = pd.Timestamp.now()
        print(q_insert)
        print(check_insert)
        print(f"Time to Check {t} from Remote db: {end - start}")
    except Exception as e:
        print(f"Error Save to Remote 1 {t} =>" + str(e), flush = True)


#%%
local_db.dispose()
remote_db.dispose()
remote_db2.dispose()

# q=f"select * from v_weekly_option_value where txn_date>'{txn_date}';"
# df= pd.read_sql(q, local_db)
# df.to_sql(name='v_weekly_option_value', con=remote_db, if_exists = 'append', index=False)    
#df2= pd.read_sql(q, remote_db)

#%%
# q=f"select * from v_stock_option_value where txn_date>'{txn_date}';"
# df= pd.read_sql(q, local_db)
# df.to_sql(name='v_stock_option_value', con=remote_db, if_exists = 'append', index=False)    
#df2= pd.read_sql(q, remote_db)

#%%
# C:\Users\<USER>\Users\willp>heroku run -a cbbc-db python saveCBBC_XLS.py