FROM python:3.13.2-slim

# Set working directory
WORKDIR /app

# Install system dependencies and Chrome for Selenium
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    postgresql-client \
    libpq-dev \
    wget \
    gnupg \
    unzip \
    tzdata \
    apt-utils \
    locales \
    fonts-noto-cjk \
    fonts-wqy-microhei \
    fonts-wqy-zenhei \
    && rm -rf /var/lib/apt/lists/*

# Install Chinese Font
# RUN apt-get update && \
#     apt-get install -y --no-install-recommends \
#     apt-utils \
#     locales

# RUN apt-get update && \
#     apt-get install -y --no-install-recommends \
#     fonts-noto-cjk \
#     fonts-wqy-microhei \
#     fonts-wqy-zenhei

ENV PYTHONIOENCODING=utf-8

# Set timezone to New Zealand
ENV TZ=Pacific/Auckland
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Install Google Chrome for Selenium
RUN wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | apt-key add - \
    && echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" > /etc/apt/sources.list.d/google-chrome.list \
    && apt-get update \
    && apt-get install -y google-chrome-stable \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY ./requirements.txt /app/requirements.txt
RUN pip install --no-cache-dir -r /app/requirements.txt

# Remove only conflicting packages, keep websockets for uvicorn
RUN pip uninstall -y python-socketio || true

# Install uvicorn with standard extras (includes WebSocket support)
RUN pip install --no-cache-dir 'uvicorn[standard]==0.34.2'

# Ensure correct websocket packages are installed with compatible versions
RUN pip install --no-cache-dir websockets==13.0 websocket-client==1.8.0 selenium==4.33.0 webdriver-manager==4.0.2

# Copy application code directly to /app (not /app/app/)
COPY ./dashboard/backend/app/ /app/

# Copy the scripts folder
COPY ./scripts/ /app/scripts/

# Create logs directory
RUN mkdir -p /app/logs

# Expose port
EXPOSE ${BACKEND_PORT}

# Default command
CMD ["sh", "-c", "uvicorn main:app --host 0.0.0.0 --port $BACKEND_PORT"]
