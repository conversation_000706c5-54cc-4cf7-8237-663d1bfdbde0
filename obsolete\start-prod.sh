#!/bin/bash

# HKEX Dashboard Production Startup Script

echo "Starting HKEX Dashboard in Production Mode..."

# Check if .env.production file exists
if [ ! -f .env ]; then
    echo "Creating .env file from .env.production..."
    cp .env.production .env
    echo "Please update .env file with your production configuration."
fi

# Create necessary directories
mkdir -p logs
mkdir -p nginx/ssl

# Run database migrations
echo "Running database migrations..."
cd backend
docker-compose exec backend alembic upgrade head
cd ..

# Start production environment
echo "Starting production containers..."
docker-compose up -d --build

# Check service status
echo "Checking service status..."
sleep 10
docker-compose ps

echo "Production environment started!"
echo "Application: http://localhost"
echo "Health Check: http://localhost/health"

# Show logs
echo "Showing recent logs..."
docker-compose logs --tail=20
