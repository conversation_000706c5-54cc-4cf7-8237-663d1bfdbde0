#!/usr/bin/env node

/**
 * Favicon Generator Script
 * 
 * This script generates ICO favicon files from SVG sources for different environments.
 * It creates blue favicons for development and red favicons for production.
 * 
 * Usage: node scripts/generate-favicons.js
 */

const fs = require('fs');
const path = require('path');

// Check if we can use a favicon generation library
let sharp;
try {
  sharp = require('sharp');
} catch (error) {
  console.warn('Sharp not available. Install with: npm install sharp');
  console.warn('Falling back to SVG favicon references...');
  sharp = null;
}

const publicDir = path.join(__dirname, '..', 'public');

async function generateFavicons() {
  console.log('Generating environment-specific favicons...');

  if (sharp) {
    try {
      // Generate development favicon (blue)
      await sharp({
        create: {
          width: 32,
          height: 32,
          channels: 4,
          background: { r: 37, g: 99, b: 235, alpha: 1 } // Blue background
        }
      })
      .composite([
        {
          input: Buffer.from(`
            <svg width="32" height="32">
              <text x="16" y="22" font-family="Arial" font-size="18" font-weight="bold" fill="white" text-anchor="middle">D</text>
              <circle cx="24" cy="8" r="3" fill="#10b981"/>
            </svg>
          `),
          top: 0,
          left: 0
        }
      ])
      .png()
      .toFile(path.join(publicDir, 'favicon-dev.png'));

      // Generate production favicon (red)
      await sharp({
        create: {
          width: 32,
          height: 32,
          channels: 4,
          background: { r: 220, g: 38, b: 38, alpha: 1 } // Red background
        }
      })
      .composite([
        {
          input: Buffer.from(`
            <svg width="32" height="32">
              <text x="16" y="22" font-family="Arial" font-size="18" font-weight="bold" fill="white" text-anchor="middle">P</text>
              <circle cx="24" cy="8" r="3" fill="#fbbf24"/>
            </svg>
          `),
          top: 0,
          left: 0
        }
      ])
      .png()
      .toFile(path.join(publicDir, 'favicon-prod.png'));

      console.log('✅ Generated PNG favicons successfully');
    } catch (error) {
      console.error('Error generating PNG favicons:', error);
    }
  }

  // Update manifest.json to include environment-aware icons
  const manifestPath = path.join(publicDir, 'manifest.json');
  
  let manifest = {};
  if (fs.existsSync(manifestPath)) {
    try {
      manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
    } catch (error) {
      console.warn('Could not parse existing manifest.json, creating new one');
    }
  }

  // Update manifest with environment-aware configuration
  manifest = {
    ...manifest,
    name: "HKEX Dashboard",
    short_name: "HKEX Dashboard",
    description: "HKEX Data Pipeline Dashboard - Monitor and orchestrate data import processes",
    icons: [
      {
        src: "favicon-dev.svg",
        sizes: "32x32",
        type: "image/svg+xml",
        purpose: "any maskable"
      },
      {
        src: "favicon-prod.svg", 
        sizes: "32x32",
        type: "image/svg+xml",
        purpose: "any maskable"
      }
    ],
    start_url: "/",
    display: "standalone",
    theme_color: "#000000",
    background_color: "#ffffff"
  };

  fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2));
  console.log('✅ Updated manifest.json');

  console.log('\n📝 Manual steps needed:');
  console.log('1. Convert SVG files to ICO format using an online tool or ImageMagick:');
  console.log('   - Convert favicon-dev.svg → favicon-dev.ico');
  console.log('   - Convert favicon-prod.svg → favicon-prod.ico');
  console.log('2. Install sharp for automatic PNG generation: npm install sharp');
  console.log('\n🎨 Favicon created:');
  console.log('   - Development: Blue favicon with "D" indicator');
  console.log('   - Production: Red favicon with "P" indicator');
}

generateFavicons().catch(console.error);
