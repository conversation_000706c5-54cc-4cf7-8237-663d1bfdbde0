#!/usr/bin/env python3
"""
Direct test of UpdateIndexOptionPostgres.py execution
"""

import subprocess
import sys
import os

print("=== Direct Script Test ===")

# Set working directory
script_dir = r"O:\Github\MaxPain\MaxPain2024"
script_path = os.path.join(script_dir, "UpdateIndexOptionPostgres.py")

print(f"Script path: {script_path}")
print(f"Script exists: {os.path.exists(script_path)}")
print(f"Working directory: {script_dir}")
print(f"Python executable: {sys.executable}")

# Test 1: Run help command
print("\n=== Test 1: Help Command ===")
try:
    result = subprocess.run(
        [sys.executable, script_path, "--help"],
        cwd=script_dir,
        capture_output=True,
        text=True,
        timeout=15
    )
    print(f"Return code: {result.returncode}")
    print(f"STDOUT:\n{result.stdout}")
    if result.stderr:
        print(f"STDERR:\n{result.stderr}")
except Exception as e:
    print(f"Error: {e}")

# Test 2: Run dry-run
print("\n=== Test 2: Dry Run ===")
try:
    result = subprocess.run(
        [sys.executable, script_path, "--dry-run", "--date", "2024-12-20"],
        cwd=script_dir,
        capture_output=True,
        text=True,
        timeout=30
    )
    print(f"Return code: {result.returncode}")
    print(f"STDOUT:\n{result.stdout}")
    if result.stderr:
        print(f"STDERR:\n{result.stderr}")
except Exception as e:
    print(f"Error: {e}")

print("\n=== Test Complete ===")
