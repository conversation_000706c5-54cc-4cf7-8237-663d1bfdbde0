#!/bin/bash

# Development Environment Validation Script
# This script checks if the development environment is ready for local development

echo "=== MaxPain2024 Development Environment Validation ==="
echo "Version: 2.0 - Updated $(date '+%Y-%m-%d')"
echo ""

# Initialize validation status
VALIDATION_ERRORS=0
VALIDATION_WARNINGS=0

# Function to log errors
log_error() {
    echo "❌ ERROR: $1"
    ((VALIDATION_ERRORS++))
}

# Function to log warnings
log_warning() {
    echo "⚠️  WARNING: $1"
    ((VALIDATION_WARNINGS++))
}

# Function to log success
log_success() {
    echo "✅ $1"
}

# Function to check command availability
check_command() {
    if command -v "$1" &> /dev/null; then
        log_success "$1 is installed"
        return 0
    else
        log_error "$1 is not installed"
        return 1
    fi
}

# Load environment variables from .env file
echo "=== Environment Configuration ==="
if [ -f .env ]; then
    log_success ".env file found"
    source .env
    
    # Check critical environment variables
    if [ -n "$FRONTEND_PORT" ]; then
        log_success "FRONTEND_PORT is set to: $FRONTEND_PORT"
    else
        log_warning "FRONTEND_PORT not set, using default: 3080"
        FRONTEND_PORT=3080
    fi
    
    if [ -n "$BACKEND_PORT" ]; then
        log_success "BACKEND_PORT is set to: $BACKEND_PORT"
    else
        log_warning "BACKEND_PORT not set, using default: 8004"
        BACKEND_PORT=8004
    fi
    
    if [ -n "$DATABASE_URL" ]; then
        log_success "DATABASE_URL is configured"
    else
        log_warning "DATABASE_URL is not set in .env file (may use default for dev)"
    fi
else
    log_warning ".env file not found. Development may use defaults."
fi

echo ""

# Check if required directories exist
echo "=== Directory Structure Validation ==="
REQUIRED_DIRS=(
    "C:/output/MaxPain/logs"
    "C:/output/MaxPain"
    "dashboard"
    "dashboard/backend"
    "dashboard/backend/app"
    "dashboard/frontend"
    "dashboard/frontend/src"
    "dashboard/nginx"
    "scripts"
    "logs"
)

for dir in "${REQUIRED_DIRS[@]}"; do
    if [ ! -d "$dir" ]; then
        log_error "Missing directory: $dir"
        echo "   Attempting to create directory..."
        mkdir -p "$dir" 2>/dev/null
        if [ $? -eq 0 ]; then
            log_success "Created: $dir"
        else
            log_error "Failed to create: $dir"
        fi
    else
        log_success "Directory exists: $dir"
    fi
done

echo ""

# Check development tools
echo "=== Development Tools Validation ==="

# Check Docker and Docker Compose
check_command "docker"
if [ $? -eq 0 ]; then
    if docker info &> /dev/null; then
        log_success "Docker daemon is running"
        
        # Check Docker version
        DOCKER_VERSION=$(docker --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' | head -1)
        log_success "Docker version: $DOCKER_VERSION"
    else
        log_error "Docker daemon is not running. Please start Docker Desktop."
    fi
fi

check_command "docker-compose"
if [ $? -eq 0 ]; then
    COMPOSE_VERSION=$(docker-compose --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' | head -1)
    log_success "Docker Compose version: $COMPOSE_VERSION"
fi

# Check Python
check_command "python"
if [ $? -eq 0 ]; then
    PYTHON_VERSION=$(python --version 2>&1 | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')
    log_success "Python version: $PYTHON_VERSION"
    
    # Check if it's Python 3.8+
    PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d. -f1)
    PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d. -f2)
    if [ "$PYTHON_MAJOR" -eq 3 ] && [ "$PYTHON_MINOR" -ge 8 ]; then
        log_success "Python version is compatible (3.8+)"
    else
        log_warning "Python version may be too old (requires 3.8+)"
    fi
fi

# Check Node.js and npm
check_command "node"
if [ $? -eq 0 ]; then
    NODE_VERSION=$(node --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')
    log_success "Node.js version: $NODE_VERSION"
    
    # Check if it's Node 16+
    NODE_MAJOR=$(echo $NODE_VERSION | cut -d. -f1)
    if [ "$NODE_MAJOR" -ge 16 ]; then
        log_success "Node.js version is compatible (16+)"
    else
        log_warning "Node.js version may be too old (requires 16+)"
    fi
fi

check_command "npm"
if [ $? -eq 0 ]; then
    NPM_VERSION=$(npm --version)
    log_success "npm version: $NPM_VERSION"
fi

# Check Git
check_command "git"
if [ $? -eq 0 ]; then
    GIT_VERSION=$(git --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')
    log_success "Git version: $GIT_VERSION"
fi

echo ""

# Validate Docker Compose files
echo "=== Docker Compose Configuration Validation ==="
if [ -f "docker-compose.dev.yml" ]; then
    log_success "docker-compose.dev.yml exists"
    if docker-compose -f docker-compose.dev.yml config &> /dev/null; then
        log_success "docker-compose.dev.yml is valid"
    else
        log_error "docker-compose.dev.yml has validation errors"
        echo "   Running docker-compose config for details:"
        docker-compose -f docker-compose.dev.yml config 2>&1 | head -10
    fi
else
    log_error "docker-compose.dev.yml not found"
fi

if [ -f "docker-compose.yml" ]; then
    log_success "docker-compose.yml exists (for production reference)"
else
    log_warning "docker-compose.yml not found (production file)"
fi

echo ""

# Check required files for development
echo "=== Essential Development Files Validation ==="
REQUIRED_FILES=(
    "docker-compose.dev.yml"
    "dashboard/backend/Dockerfile.dev"
    "dashboard/frontend/Dockerfile.dev"
    "dashboard/nginx/Dockerfile"
    "dashboard/nginx/nginx.conf.template"
    "dashboard/nginx/start-nginx.sh"
    "requirements.txt"
    "dashboard/frontend/package.json"
    "run.bat"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [ -f "$file" ]; then
        log_success "File exists: $file"
        
        # Additional file-specific checks for development
        case "$file" in
            "requirements.txt")
                if grep -q "fastapi" "$file" && grep -q "uvicorn" "$file"; then
                    log_success "requirements.txt contains essential packages"
                else
                    log_warning "requirements.txt may be missing essential packages"
                fi
                ;;
            "dashboard/frontend/package.json")
                if grep -q "react" "$file" && grep -q "react-scripts" "$file"; then
                    log_success "package.json contains essential React packages"
                else
                    log_warning "package.json may be missing essential packages"
                fi
                
                # Check if node_modules exists
                if [ -d "dashboard/frontend/node_modules" ]; then
                    log_success "node_modules directory exists"
                else
                    log_warning "node_modules not found. Run 'npm install' in dashboard/frontend/"
                fi
                ;;
        esac
    else
        log_error "Missing file: $file"
    fi
done

echo ""

# Check development-specific application structure
echo "=== Development Application Structure ==="
DEV_BACKEND_FILES=(
    "dashboard/backend/app"
    "dashboard/backend/app/main.py"
    "dashboard/backend/app/core"
    "dashboard/backend/app/tasks"
    "dashboard/backend/app/core/config.py"
)

for item in "${DEV_BACKEND_FILES[@]}"; do
    if [ -e "$item" ]; then
        log_success "Backend component exists: $item"
    else
        log_error "Missing backend component: $item"
    fi
done

DEV_FRONTEND_FILES=(
    "dashboard/frontend/src"
    "dashboard/frontend/public"
    "dashboard/frontend/src/components"
    "dashboard/frontend/src/config"
)

for item in "${DEV_FRONTEND_FILES[@]}"; do
    if [ -e "$item" ]; then
        log_success "Frontend component exists: $item"
    else
        log_error "Missing frontend component: $item"
    fi
done

echo ""

# Check development ports (different from production)
echo "=== Development Port Availability Check ==="
DEV_PORTS=($FRONTEND_PORT $BACKEND_PORT 5678 6379)  # 5678 is debug port

for port in "${DEV_PORTS[@]}"; do
    if command -v netstat &> /dev/null; then
        if netstat -an 2>/dev/null | grep ":$port " | grep LISTEN &> /dev/null; then
            log_warning "Port $port is already in use"
        else
            log_success "Port $port is available"
        fi
    elif command -v ss &> /dev/null; then
        if ss -tuln 2>/dev/null | grep ":$port " &> /dev/null; then
            log_warning "Port $port is already in use"
        else
            log_success "Port $port is available"
        fi
    else
        log_warning "Cannot check port $port (netstat/ss not available)"
    fi
done

# Check Python virtual environment
echo "=== Python Environment Check ==="
if [ -d "env" ] || [ -d "venv" ] || [ -n "$VIRTUAL_ENV" ]; then
    log_success "Python virtual environment detected"
    if [ -n "$VIRTUAL_ENV" ]; then
        log_success "Virtual environment is active: $VIRTUAL_ENV"
    else
        log_warning "Virtual environment exists but may not be active"
    fi
else
    log_warning "No Python virtual environment detected. Consider creating one."
fi

# Check if Python dependencies are installed
if [ -f "requirements.txt" ]; then
    if command -v pip &> /dev/null; then
        # Check if key packages are installed
        if pip list | grep -q "fastapi"; then
            log_success "FastAPI is installed"
        else
            log_warning "FastAPI not installed. Run: pip install -r requirements.txt"
        fi

        if pip list | grep -q "uvicorn"; then
            log_success "Uvicorn is installed"
        else
            log_warning "Uvicorn not installed. Run: pip install -r requirements.txt"
        fi
    fi
fi

echo ""

# Check system resources for development
echo "=== Development System Resources Check ==="
if command -v free &> /dev/null; then
    MEMORY_GB=$(free -g | awk '/^Mem:/{print $2}')
    if [ "$MEMORY_GB" -ge 8 ]; then
        log_success "System memory: ${MEMORY_GB}GB (excellent for development)"
    elif [ "$MEMORY_GB" -ge 4 ]; then
        log_success "System memory: ${MEMORY_GB}GB (sufficient for development)"
    else
        log_warning "System memory: ${MEMORY_GB}GB (may be limited for development)"
    fi
fi

if command -v df &> /dev/null; then
    DISK_USAGE=$(df -h . | awk 'NR==2{print $5}' | sed 's/%//')
    if [ "$DISK_USAGE" -lt 80 ]; then
        log_success "Disk usage: ${DISK_USAGE}% (sufficient space)"
    else
        log_warning "Disk usage: ${DISK_USAGE}% (consider freeing up space)"
    fi
fi

echo ""

# Network connectivity check
echo "=== Network Connectivity Check ==="
if command -v ping &> /dev/null; then
    if ping -c 1 google.com &> /dev/null; then
        log_success "Internet connectivity available"
    else
        log_warning "Internet connectivity issues detected"
    fi
fi

# Check database connectivity for development
if [ -n "$DATABASE_URL" ]; then
    echo "=== Database Connectivity Check ==="
    if command -v psql &> /dev/null; then
        if echo "SELECT 1;" | psql "$DATABASE_URL" &> /dev/null; then
            log_success "Database connection successful"
        else
            log_warning "Database connection failed (may be expected in dev environment)"
        fi
    else
        log_warning "psql not available, cannot test database connection"
    fi
fi

echo ""

# Development-specific checks
echo "=== Development-Specific Checks ==="

# Check if hot reload is configured
if [ -f "docker-compose.dev.yml" ]; then
    if grep -q "volumes:" "docker-compose.dev.yml" && grep -q "reload" "docker-compose.dev.yml"; then
        log_success "Hot reload appears to be configured"
    else
        log_warning "Hot reload may not be properly configured"
    fi
fi

# Check for development debugging setup
if [ -f "docker-compose.dev.yml" ]; then
    if grep -q "5678:5678" "docker-compose.dev.yml"; then
        log_success "Debug port (5678) is configured"
    else
        log_warning "Debug port may not be configured"
    fi
fi

# Check for development environment variables
if [ -f ".env" ]; then
    if grep -q "DEBUG=true" ".env" || grep -q "ENVIRONMENT=development" ".env"; then
        log_success "Development environment variables detected"
    else
        log_warning "Development environment variables may not be set"
    fi
fi

echo ""

# Final validation summary
echo "=== Development Validation Summary ==="
echo "Errors: $VALIDATION_ERRORS"
echo "Warnings: $VALIDATION_WARNINGS"

if [ $VALIDATION_ERRORS -eq 0 ]; then
    echo ""
    log_success "Development environment validation PASSED!"
    echo ""
    echo "🚀 Ready for development! You can start the development environment with:"
    echo "   ./run.bat dev"
    echo "   or"
    echo "   docker-compose -f docker-compose.dev.yml up --build"
    echo ""
    echo "🔧 Development services will be available at:"
    echo "   - Frontend (with hot reload): http://localhost:$FRONTEND_PORT"
    echo "   - Backend API (with auto-reload): http://localhost:$BACKEND_PORT"
    echo "   - API documentation: http://localhost:$BACKEND_PORT/docs"
    echo "   - Debug port: 5678 (for Python debugging)"
    echo ""
    echo "💡 Development tips:"
    echo "   - Code changes will auto-reload in both frontend and backend"
    echo "   - Logs are mounted to C:/output/MaxPain/logs"
    echo "   - Use 'docker-compose -f docker-compose.dev.yml logs -f' to view logs"
    echo ""
    exit 0
else
    echo ""
    log_error "Development environment validation FAILED!"
    echo "   Please fix the $VALIDATION_ERRORS error(s) before starting development."
    if [ $VALIDATION_WARNINGS -gt 0 ]; then
        echo "   Also consider addressing the $VALIDATION_WARNINGS warning(s) for better development experience."
    fi
    echo ""
    echo "🔧 Common fixes:"
    echo "   - Install missing tools (Docker, Node.js, Python)"
    echo "   - Run 'npm install' in dashboard/frontend/"
    echo "   - Run 'pip install -r requirements.txt' for Python dependencies"
    echo "   - Create missing directories"
    echo "   - Check .env file configuration"
    echo ""
    exit 1
fi
